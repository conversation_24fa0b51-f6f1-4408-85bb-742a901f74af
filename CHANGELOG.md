# Changelog

All notable changes to Compress<PERSON>low will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [1.0.0] - 2025-06-26

### Added - CompressFlow Launch 🚀
- **Multiple file processing**: Upload and process multiple images simultaneously
- **Batch export functionality**: "Download All" button for bulk downloads
- **File selector dropdown**: Switch between uploaded images for preview
- **Enhanced UI feedback**: Progress notifications and error handling
- **Bulk processing optimization**: Process files in chunks of 10 for better performance
- **Throttled downloads**: Prevent browser blocking with 300ms delays between downloads
- **Comprehensive documentation**: README, CONTRIBUTING, and DEPLOYMENT guides
- **GitHub Actions workflow**: Automated CI/CD pipeline setup
- **Enhanced package.json**: Additional scripts and metadata

### Enhanced (from Original Squoosh)
- **Multi-file drag & drop**: Support for selecting multiple images at once
- **Batch compression**: Apply same settings to all uploaded files
- **Download management**: Automatic URL cleanup and memory management
- **Error handling**: Better error messages and recovery for batch operations
- **User experience**: Improved notifications and progress tracking

### Technical Improvements
- **Code organization**: Better separation of batch processing logic
- **Performance**: Optimized memory usage for multiple file handling
- **Reliability**: Enhanced error handling for edge cases
- **Maintainability**: Updated documentation and contribution guidelines

### Infrastructure
- **Repository setup**: Configured for GitHub with proper branching
- **Deployment ready**: Multiple deployment options documented
- **Development workflow**: Enhanced npm scripts for better DX
- **CI/CD pipeline**: GitHub Actions for automated testing and deployment

## [Base] - Squoosh Foundation

### Inherited Features
- **WebAssembly codecs**: High-performance image compression
- **Multiple formats**: JPEG, PNG, WebP, AVIF support
- **Client-side processing**: No server uploads, complete privacy
- **Real-time preview**: Side-by-side comparison with original
- **Advanced options**: Fine-tuned codec settings
- **Progressive Web App**: Offline functionality and installability
- **Responsive design**: Works on desktop and mobile devices

---

## Future Roadmap

### Planned Features
- [ ] **HEIC format support**: Add support for Apple's HEIC format
- [ ] **JXL improvements**: Enhanced JPEG XL codec integration
- [ ] **Drag & drop reordering**: Reorder files in the processing queue
- [ ] **Compression presets**: Save and load common compression settings
- [ ] **Batch resize**: Apply resize operations to all files
- [ ] **Export formats**: ZIP download option for batch exports
- [ ] **Progress indicators**: Individual file progress tracking
- [ ] **File filtering**: Filter by file type or size
- [ ] **Internationalization**: Multi-language support
- [ ] **Accessibility**: Enhanced screen reader and keyboard support

### Technical Debt
- [ ] **Test coverage**: Add comprehensive test suite
- [ ] **TypeScript**: Improve type coverage
- [ ] **Performance**: Optimize for very large file batches
- [ ] **Memory management**: Better cleanup for long sessions
- [ ] **Error boundaries**: React error boundaries for better UX

---

**CompressFlow** - Evolved from Squoosh, enhanced for efficiency! 🎯
