name: Deploy CompressFlow

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Build application
      run: npm run build
      
    - name: Run tests (if any)
      run: npm test --if-present
      
    # Uncomment and configure for your preferred deployment target
    
    # Deploy to GitHub Pages
    # - name: Deploy to GitHub Pages
    #   if: github.ref == 'refs/heads/main'
    #   uses: peaceiris/actions-gh-pages@v3
    #   with:
    #     github_token: ${{ secrets.GITHUB_TOKEN }}
    #     publish_dir: .tmp/build/static
    
    # Deploy to Vercel
    # - name: Deploy to Vercel
    #   if: github.ref == 'refs/heads/main'
    #   uses: amondnet/vercel-action@v25
    #   with:
    #     vercel-token: ${{ secrets.VERCEL_TOKEN }}
    #     vercel-org-id: ${{ secrets.ORG_ID }}
    #     vercel-project-id: ${{ secrets.PROJECT_ID }}
    #     working-directory: .tmp/build/static
    
    # Deploy to Netlify
    # - name: Deploy to Netlify
    #   if: github.ref == 'refs/heads/main'
    #   uses: nwtgck/actions-netlify@v2.0
    #   with:
    #     publish-dir: '.tmp/build/static'
    #     production-branch: main
    #     github-token: ${{ secrets.GITHUB_TOKEN }}
    #     deploy-message: "Deploy from GitHub Actions"
    #   env:
    #     NETLIFY_AUTH_TOKEN: ${{ secrets.NETLIFY_AUTH_TOKEN }}
    #     NETLIFY_SITE_ID: ${{ secrets.NETLIFY_SITE_ID }}
