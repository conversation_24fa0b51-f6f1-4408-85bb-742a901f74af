---
name: Bug report
about: Something is not working as expected
labels: 

---

**Before you start**
Please take a look at the [FAQ](https://github.com/GoogleChromeLabs/compressflow/wiki/FAQ) as well as the already opened issues! If nothing fits your problem, go ahead and fill out the following template:

**Describe the bug**
A clear and concise description of what the bug is.

**To Reproduce**
Steps to reproduce the behavior:
1. Go to '...'
2. Click on '....'
3. Scroll down to '....'
4. See error

**Expected behavior**
A clear and concise description of what you expected to happen.

**Version:**
 - OS w/ version: [e.g. iOS 12]
 - Browser w/ version [e.g. Chrome 70]
 - Node version: [e.g. 10.11.0]
 - npm version: [e.g. 6.4.1]

**Is your issue related to the quality of image compression?**
Please attach original and output images (you can drag & drop to attach). 
- Original image 
- Output image from CompressFlow

**Additional context, screenshots, screencasts**
Add any other context about the problem here.
