# 🔥 CompressFlow - Enhanced Bulk Compression

Lightning-fast **bulk image compression** with cyberpunk neon UI and advanced export capabilities.

![CompressFlow Logo](https://page1.genspark.site/v1/base64_upload/2271c90f5513b3d57e744142ec978e2d)

## ⚡ Enhanced Features

### 🚀 **Bulk Processing**
- **Multiple file upload** - Drag & drop entire folders
- **Batch compression** - Process dozens of images simultaneously  
- **Bulk export** - Download all compressed images as ZIP
- **Progress tracking** - Real-time compression status

### 🎮 **Cyberpunk Experience**
- **Neon-powered UI** - Electric green & pink accents
- **Dark theme** - Easy on the eyes during long sessions
- **Smooth animations** - Futuristic interaction feedback
- **Gaming-inspired** - RGB lighting effects

### 🔒 **Privacy & Performance**
- **Client-side only** - No server uploads, complete privacy
- **WebAssembly powered** - Desktop-class compression speed
- **PWA enabled** - Install and use offline
- **Multiple formats** - JPEG, PNG, WebP, AVIF support

## 🚀 **Live Demo**
**[compressflow.netlify.app](https://compressflow.netlify.app)**

## 🛠️ **Quick Start**
```bash
npm install
npm run dev
```

## 📊 **Compression Stats**
- **Up to 90% size reduction** for photos
- **Lossless PNG optimization** 
- **WebP/AVIF modern formats**
- **Batch processing** up to 100 files

## 🎯 **Use Cases**
- **Web developers** - Optimize assets for faster loading
- **Photographers** - Reduce portfolio file sizes
- **Content creators** - Prepare images for social media
- **E-commerce** - Optimize product images

## 🔧 **Advanced Features**
- **Custom quality settings** per format
- **Resize during compression**
- **Format conversion** (PNG → WebP, etc.)
- **Metadata preservation** options

---

**Built with ❤️ by the CompressFlow team**
