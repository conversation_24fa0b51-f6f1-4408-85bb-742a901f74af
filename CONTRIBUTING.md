# Contributing to <PERSON><PERSON>ress<PERSON><PERSON>

Thank you for your interest in contributing to CompressFlow! We welcome contributions from the community to help make image compression more efficient and accessible.

## 🚀 Getting Started

### Prerequisites
- Node.js 16+
- npm or yarn
- Basic knowledge of TypeScript/JavaScript
- Familiarity with Preact/React (helpful but not required)

### Development Setup

1. **Fork the repository** on GitHub
2. **Clone your fork** locally:
   ```bash
   git clone https://github.com/YOUR_USERNAME/compressflow.git
   cd compressflow
   ```
3. **Install dependencies**:
   ```bash
   npm install
   ```
4. **Start development server**:
   ```bash
   npm run dev
   ```
5. **Make your changes** and test them
6. **Submit a pull request**

## 🎯 Areas for Contribution

### High Priority
- **New compression formats** (HEIC, JXL improvements)
- **Performance optimizations** for batch processing
- **UI/UX improvements** for multiple file handling
- **Mobile responsiveness** enhancements
- **Accessibility improvements**

### Medium Priority
- **Additional image processors** (filters, effects)
- **Better error handling** and user feedback
- **Internationalization** (i18n)
- **Progressive Web App** features
- **Documentation improvements**

### Low Priority
- **Code refactoring** and cleanup
- **Test coverage** improvements
- **Build process** optimizations

## 📝 Contribution Guidelines

### Code Style
- Use **TypeScript** for new code
- Follow existing **code formatting** (Prettier configured)
- Write **meaningful commit messages**
- Add **comments** for complex logic

### Pull Request Process
1. **Create a feature branch** from `main`
2. **Make your changes** with clear, focused commits
3. **Test thoroughly** - ensure all features work
4. **Update documentation** if needed
5. **Submit PR** with clear description of changes

### Commit Message Format
```
type(scope): brief description

Longer description if needed

Fixes #issue-number
```

Examples:
- `feat(compression): add HEIC format support`
- `fix(ui): resolve batch download button styling`
- `docs(readme): update installation instructions`

## 🧪 Testing

### Manual Testing Checklist
- [ ] Single file compression works
- [ ] Multiple file upload works
- [ ] Batch download functions correctly
- [ ] All compression formats work
- [ ] UI is responsive on mobile
- [ ] No console errors

### Running Tests
```bash
npm test  # Run any automated tests
npm run lint  # Check code formatting
```

## 🐛 Reporting Issues

When reporting bugs, please include:
- **Browser and version**
- **Steps to reproduce**
- **Expected vs actual behavior**
- **Screenshots** (if applicable)
- **Console errors** (if any)

## 💡 Feature Requests

For new features:
- **Check existing issues** first
- **Describe the use case** clearly
- **Explain the benefit** to users
- **Consider implementation complexity**

## 📄 License

By contributing, you agree that your contributions will be licensed under the Apache 2.0 License.

## 🤝 Code of Conduct

Please be respectful and constructive in all interactions. We're all here to make CompressFlow better!

## 🙋‍♀️ Questions?

Feel free to open an issue for questions or reach out to the maintainers. We're happy to help new contributors get started!
