if(!self.define){let e={};const r=(r,n)=>(r=r.startsWith(location.origin)?r:new URL(r+".js",n).href,e[r]||new Promise((e=>{if("document"in self){const n=document.createElement("link");n.rel="preload",n.as="script",n.href=r,n.onload=()=>{const n=document.createElement("script");n.src=r,n.onload=e,document.head.appendChild(n)},document.head.appendChild(n)}else self.nextDefineUri=r,importScripts(r),e()})).then((()=>{let n=e[r];if(!n)throw new Error(`Module ${r} didn’t register its module`);return n})));self.define=(n,t)=>{const o=self.nextDefineUri||("document"in self?document.currentScript.src:"")||location.href;if(e[o])return;let i={};const s=e=>r(e,o),a={module:{uri:o},exports:i,require:s};e[o]=Promise.resolve().then((()=>Promise.all(n.map((e=>a[e]||s(e)))))).then((e=>(t(...e),i)))}}define(["module","require","exports"],(function(e,r,n){function t(e,r){return new Promise((n=>{e.addEventListener("message",(function t({data:o}){null!=o&&o.type===r&&(e.removeEventListener("message",t),n(o))}))}))}t(self,"wasm_bindgen_worker_init").then((async e=>{const n=await r("./squoosh_oxipng-b619b9e2");await n.default(e.module,e.memory),postMessage({type:"wasm_bindgen_worker_ready"}),n.wbg_rayon_start_worker(e.receiver)})),n.startWorkers=async function(r,n,o){const i={type:"wasm_bindgen_worker_init",module:r,memory:n,receiver:o.receiver()};try{await Promise.all(Array.from({length:o.numThreads()},(()=>{const r=new Worker(new URL("/c/workerHelpers-9499ba45.js",e.uri));return r.postMessage(i),t(r,"wasm_bindgen_worker_ready")}))),o.build()}finally{o.free()}},Object.defineProperty(n,"__esModule",{value:!0})}));
