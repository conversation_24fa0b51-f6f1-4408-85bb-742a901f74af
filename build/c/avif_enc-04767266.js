define(["module","require","exports"],(function(n,r,t){var e,o=(e=n.uri,function(r){var t,o;(r=void 0!==(r=r||{})?r:{}).ready=new Promise((function(n,r){t=n,o=r}));var a,i={};for(a in r)r.hasOwnProperty(a)&&(i[a]=r[a]);var u,c=!0,f="";f=self.location.href,e&&(f=e),f=0!==f.indexOf("blob:")?f.substr(0,f.replace(/[?#].*/,"").lastIndexOf("/")+1):"",u=function(n){var r=new XMLHttpRequest;return r.open("GET",n,!1),r.responseType="arraybuffer",r.send(null),new Uint8Array(r.response)};var l=r.print||console.log.bind(console),s=r.printErr||console.warn.bind(console);for(a in i)i.hasOwnProperty(a)&&(r[a]=i[a]);i=null,r.arguments&&r.arguments,r.thisProgram&&r.thisProgram,r.quit&&r.quit;var p,v,d=0;r.wasmBinary&&(p=r.wasmBinary),r.noExitRuntime,"object"!=typeof WebAssembly&&B("no native wasm support detected");var h=!1,y=new TextDecoder("utf8");function g(n,r){if(!n)return"";for(var t=n+r,e=n;!(e>=t)&&b[e];)++e;return y.decode(b.subarray(n,e))}var m,w,b,T,A,_,C,P,E,j,W=new TextDecoder("utf-16le");function k(n,r){for(var t=n,e=t>>1,o=e+r/2;!(e>=o)&&A[e];)++e;return t=e<<1,W.decode(b.subarray(n,t))}function F(n,r,t){if(void 0===t&&(t=2147483647),t<2)return 0;for(var e=r,o=(t-=2)<2*n.length?t/2:n.length,a=0;a<o;++a){var i=n.charCodeAt(a);T[r>>1]=i,r+=2}return T[r>>1]=0,r-e}function R(n){return 2*n.length}function I(n,r){for(var t=0,e="";!(t>=r/4);){var o=_[n+4*t>>2];if(0==o)break;if(++t,o>=65536){var a=o-65536;e+=String.fromCharCode(55296|a>>10,56320|1023&a)}else e+=String.fromCharCode(o)}return e}function S(n,r,t){if(void 0===t&&(t=2147483647),t<4)return 0;for(var e=r,o=e+t-4,a=0;a<n.length;++a){var i=n.charCodeAt(a);if(i>=55296&&i<=57343&&(i=65536+((1023&i)<<10)|1023&n.charCodeAt(++a)),_[r>>2]=i,(r+=4)+4>o)break}return _[r>>2]=0,r-e}function U(n){for(var r=0,t=0;t<n.length;++t){var e=n.charCodeAt(t);e>=55296&&e<=57343&&++t,r+=4}return r}function H(n){m=n,r.HEAP8=w=new Int8Array(n),r.HEAP16=T=new Int16Array(n),r.HEAP32=_=new Int32Array(n),r.HEAPU8=b=new Uint8Array(n),r.HEAPU16=A=new Uint16Array(n),r.HEAPU32=C=new Uint32Array(n),r.HEAPF32=P=new Float32Array(n),r.HEAPF64=E=new Float64Array(n)}r.INITIAL_MEMORY;var O=[],x=[],D=[],V=0,M=null;function B(n){r.onAbort&&r.onAbort(n),s(n="Aborted("+n+")"),h=!0,n+=". Build with -s ASSERTIONS=1 for more info.";var t=new WebAssembly.RuntimeError(n);throw o(t),t}r.preloadedImages={},r.preloadedAudios={};var q,N,z="data:application/octet-stream;base64,";function L(n){return n.startsWith(z)}function G(n){try{if(n==q&&p)return new Uint8Array(p);if(u)return u(n);throw"both async and sync fetching of the wasm failed"}catch(n){B(n)}}function J(n){for(;n.length>0;){var t=n.shift();if("function"!=typeof t){var e=t.func;"number"==typeof e?void 0===t.arg?Y(e)():Y(e)(t.arg):e(void 0===t.arg?null:t.arg)}else t(r)}}r.locateFile?L(q="avif_enc.wasm")||(N=q,q=r.locateFile?r.locateFile(N,f):f+N):q=new URL("/c/avif_enc-90ce2a03.wasm",n.uri).toString();var X=[];function Y(n){var r=X[n];return r||(n>=X.length&&(X.length=n+1),X[n]=r=j.get(n)),r}var Z={mappings:{},buffers:[null,[],[]],printChar:function(n,r){var t=Z.buffers[n];0===r||10===r?((1===n?l:s)(function(n,r,t){for(var e=r+t,o=r;n[o]&&!(o>=e);)++o;return y.decode(n.subarray?n.subarray(r,o):new Uint8Array(n.slice(r,o)))}(t,0)),t.length=0):t.push(r)},varargs:void 0,get:function(){return Z.varargs+=4,_[Z.varargs-4>>2]},getStr:function(n){return g(n)},get64:function(n,r){return n}},$={};function K(n){for(;n.length;){var r=n.pop();n.pop()(r)}}function Q(n){return this.fromWireType(C[n>>2])}var nn={},rn={},tn={},en=48,on=57;function an(n){if(void 0===n)return"_unknown";var r=(n=n.replace(/[^a-zA-Z0-9_]/g,"$")).charCodeAt(0);return r>=en&&r<=on?"_"+n:n}function un(n,r){return n=an(n),new Function("body","return function "+n+'() {\n    "use strict";    return body.apply(this, arguments);\n};\n')(r)}function cn(n,r){var t=un(r,(function(n){this.name=r,this.message=n;var t=new Error(n).stack;void 0!==t&&(this.stack=this.toString()+"\n"+t.replace(/^Error(:[^\n]*)?\n/,""))}));return t.prototype=Object.create(n.prototype),t.prototype.constructor=t,t.prototype.toString=function(){return void 0===this.message?this.name:this.name+": "+this.message},t}var fn=void 0;function ln(n){throw new fn(n)}function sn(n,r,t){function e(r){var e=t(r);e.length!==n.length&&ln("Mismatched type converter count");for(var o=0;o<n.length;++o)gn(n[o],e[o])}n.forEach((function(n){tn[n]=r}));var o=new Array(r.length),a=[],i=0;r.forEach((function(n,r){rn.hasOwnProperty(n)?o[r]=rn[n]:(a.push(n),nn.hasOwnProperty(n)||(nn[n]=[]),nn[n].push((function(){o[r]=rn[n],++i===a.length&&e(o)})))})),0===a.length&&e(o)}function pn(n){switch(n){case 1:return 0;case 2:return 1;case 4:return 2;case 8:return 3;default:throw new TypeError("Unknown type size: "+n)}}var vn=void 0;function dn(n){for(var r="",t=n;b[t];)r+=vn[b[t++]];return r}var hn=void 0;function yn(n){throw new hn(n)}function gn(n,r,t){if(t=t||{},!("argPackAdvance"in r))throw new TypeError("registerType registeredInstance requires argPackAdvance");var e=r.name;if(n||yn('type "'+e+'" must have a positive integer typeid pointer'),rn.hasOwnProperty(n)){if(t.ignoreDuplicateRegistrations)return;yn("Cannot register type '"+e+"' twice")}if(rn[n]=r,delete tn[n],nn.hasOwnProperty(n)){var o=nn[n];delete nn[n],o.forEach((function(n){n()}))}}var mn=[],wn=[{},{value:void 0},{value:null},{value:!0},{value:!1}];function bn(n){n>4&&0==--wn[n].refcount&&(wn[n]=void 0,mn.push(n))}function Tn(){for(var n=0,r=5;r<wn.length;++r)void 0!==wn[r]&&++n;return n}function An(){for(var n=5;n<wn.length;++n)if(void 0!==wn[n])return wn[n];return null}var _n={toValue:function(n){return n||yn("Cannot use deleted val. handle = "+n),wn[n].value},toHandle:function(n){switch(n){case void 0:return 1;case null:return 2;case!0:return 3;case!1:return 4;default:var r=mn.length?mn.pop():wn.length;return wn[r]={refcount:1,value:n},r}}};function Cn(n){if(null===n)return"null";var r=typeof n;return"object"===r||"array"===r||"function"===r?n.toString():""+n}function Pn(n,r){switch(r){case 2:return function(n){return this.fromWireType(P[n>>2])};case 3:return function(n){return this.fromWireType(E[n>>3])};default:throw new TypeError("Unknown float type: "+n)}}function En(n,r,t,e,o){var a=r.length;a<2&&yn("argTypes array size mismatch! Must at least get return value and 'this' types!");for(var i=null!==r[1]&&null!==t,u=!1,c=1;c<r.length;++c)if(null!==r[c]&&void 0===r[c].destructorFunction){u=!0;break}var f="void"!==r[0].name,l="",s="";for(c=0;c<a-2;++c)l+=(0!==c?", ":"")+"arg"+c,s+=(0!==c?", ":"")+"arg"+c+"Wired";var p="return function "+an(n)+"("+l+") {\nif (arguments.length !== "+(a-2)+") {\nthrowBindingError('function "+n+" called with ' + arguments.length + ' arguments, expected "+(a-2)+" args!');\n}\n";u&&(p+="var destructors = [];\n");var v=u?"destructors":"null",d=["throwBindingError","invoker","fn","runDestructors","retType","classParam"],h=[yn,e,o,K,r[0],r[1]];for(i&&(p+="var thisWired = classParam.toWireType("+v+", this);\n"),c=0;c<a-2;++c)p+="var arg"+c+"Wired = argType"+c+".toWireType("+v+", arg"+c+"); // "+r[c+2].name+"\n",d.push("argType"+c),h.push(r[c+2]);if(i&&(s="thisWired"+(s.length>0?", ":"")+s),p+=(f?"var rv = ":"")+"invoker(fn"+(s.length>0?", ":"")+s+");\n",u)p+="runDestructors(destructors);\n";else for(c=i?1:2;c<r.length;++c){var y=1===c?"thisWired":"arg"+(c-2)+"Wired";null!==r[c].destructorFunction&&(p+=y+"_dtor("+y+"); // "+r[c].name+"\n",d.push(y+"_dtor"),h.push(r[c].destructorFunction))}return f&&(p+="var ret = retType.fromWireType(rv);\nreturn ret;\n"),p+="}\n",d.push(p),function(n,r){if(!(n instanceof Function))throw new TypeError("new_ called with constructor type "+typeof n+" which is not a function");var t=un(n.name||"unknownFunctionName",(function(){}));t.prototype=n.prototype;var e=new t,o=n.apply(e,r);return o instanceof Object?o:e}(Function,d).apply(null,h)}function jn(n,t,e){r.hasOwnProperty(n)?((void 0===e||void 0!==r[n].overloadTable&&void 0!==r[n].overloadTable[e])&&yn("Cannot register public name '"+n+"' twice"),function(n,r,t){if(void 0===n[r].overloadTable){var e=n[r];n[r]=function(){return n[r].overloadTable.hasOwnProperty(arguments.length)||yn("Function '"+t+"' called with an invalid number of arguments ("+arguments.length+") - expects one of ("+n[r].overloadTable+")!"),n[r].overloadTable[arguments.length].apply(this,arguments)},n[r].overloadTable=[],n[r].overloadTable[e.argCount]=e}}(r,n,n),r.hasOwnProperty(e)&&yn("Cannot register multiple overloads of a function with the same number of arguments ("+e+")!"),r[n].overloadTable[e]=t):(r[n]=t,void 0!==e&&(r[n].numArguments=e))}function Wn(n,t,e){return n.includes("j")?function(n,t,e){var o=r["dynCall_"+n];return e&&e.length?o.apply(null,[t].concat(e)):o.call(null,t)}(n,t,e):Y(t).apply(null,e)}function kn(n,r){var t,e,o,a=(n=dn(n)).includes("j")?(t=n,e=r,o=[],function(){o.length=arguments.length;for(var n=0;n<arguments.length;n++)o[n]=arguments[n];return Wn(t,e,o)}):Y(r);return"function"!=typeof a&&yn("unknown function pointer with signature "+n+": "+r),a}var Fn=void 0;function Rn(n){var r=Bn(n),t=dn(r);return Mn(r),t}function In(n,r,t){switch(r){case 0:return t?function(n){return w[n]}:function(n){return b[n]};case 1:return t?function(n){return T[n>>1]}:function(n){return A[n>>1]};case 2:return t?function(n){return _[n>>2]}:function(n){return C[n>>2]};default:throw new TypeError("Unknown integer type: "+n)}}var Sn={};function Un(){return"object"==typeof globalThis?globalThis:Function("return this")()}function Hn(n,r){var t=rn[n];return void 0===t&&yn(r+" has unknown type "+Rn(n)),t}var On={};function xn(n){try{return v.grow(n-m.byteLength+65535>>>16),H(v.buffer),1}catch(n){}}fn=r.InternalError=cn(Error,"InternalError"),function(){for(var n=new Array(256),r=0;r<256;++r)n[r]=String.fromCharCode(r);vn=n}(),hn=r.BindingError=cn(Error,"BindingError"),r.count_emval_handles=Tn,r.get_first_emval=An,Fn=r.UnboundTypeError=cn(Error,"UnboundTypeError");var Dn={s:function(n,r,t){return Z.varargs=t,0},M:function(n,r,t){return Z.varargs=t,0},O:function(n,r,t){Z.varargs=t},D:function(n){var r=$[n];delete $[n];var t=r.rawConstructor,e=r.rawDestructor,o=r.fields;sn([n],o.map((function(n){return n.getterReturnType})).concat(o.map((function(n){return n.setterArgumentType}))),(function(n){var a={};return o.forEach((function(r,t){var e=r.fieldName,i=n[t],u=r.getter,c=r.getterContext,f=n[t+o.length],l=r.setter,s=r.setterContext;a[e]={read:function(n){return i.fromWireType(u(c,n))},write:function(n,r){var t=[];l(s,n,f.toWireType(t,r)),K(t)}}})),[{name:r.name,fromWireType:function(n){var r={};for(var t in a)r[t]=a[t].read(n);return e(n),r},toWireType:function(n,r){for(var o in a)if(!(o in r))throw new TypeError('Missing field:  "'+o+'"');var i=t();for(o in a)a[o].write(i,r[o]);return null!==n&&n.push(e,i),i},argPackAdvance:8,readValueFromPointer:Q,destructorFunction:e}]}))},B:function(n,r,t,e,o){},Q:function(n,r,t,e,o){var a=pn(t);gn(n,{name:r=dn(r),fromWireType:function(n){return!!n},toWireType:function(n,r){return r?e:o},argPackAdvance:8,readValueFromPointer:function(n){var e;if(1===t)e=w;else if(2===t)e=T;else{if(4!==t)throw new TypeError("Unknown boolean type size: "+r);e=_}return this.fromWireType(e[n>>a])},destructorFunction:null})},P:function(n,r){gn(n,{name:r=dn(r),fromWireType:function(n){var r=_n.toValue(n);return bn(n),r},toWireType:function(n,r){return _n.toHandle(r)},argPackAdvance:8,readValueFromPointer:Q,destructorFunction:null})},u:function(n,r,t){var e=pn(t);gn(n,{name:r=dn(r),fromWireType:function(n){return n},toWireType:function(n,r){if("number"!=typeof r&&"boolean"!=typeof r)throw new TypeError('Cannot convert "'+Cn(r)+'" to '+this.name);return r},argPackAdvance:8,readValueFromPointer:Pn(r,e),destructorFunction:null})},y:function(n,t,e,o,a,i){var u=function(n,r){for(var t=[],e=0;e<n;e++)t.push(_[(r>>2)+e]);return t}(t,e);n=dn(n),a=kn(o,a),jn(n,(function(){!function(n,r){var t=[],e={};throw r.forEach((function n(r){e[r]||rn[r]||(tn[r]?tn[r].forEach(n):(t.push(r),e[r]=!0))})),new Fn(n+": "+t.map(Rn).join([", "]))}("Cannot call "+n+" due to unbound types",u)}),t-1),sn([],u,(function(e){var o=[e[0],null].concat(e.slice(1));return function(n,t,e){r.hasOwnProperty(n)||ln("Replacing nonexistant public symbol"),void 0!==r[n].overloadTable&&void 0!==e?r[n].overloadTable[e]=t:(r[n]=t,r[n].argCount=e)}(n,En(n,o,null,a,i),t-1),[]}))},i:function(n,r,t,e,o){r=dn(r),-1===o&&(o=4294967295);var a=pn(t),i=function(n){return n};if(0===e){var u=32-8*t;i=function(n){return n<<u>>>u}}var c=r.includes("unsigned");gn(n,{name:r,fromWireType:i,toWireType:function(n,t){if("number"!=typeof t&&"boolean"!=typeof t)throw new TypeError('Cannot convert "'+Cn(t)+'" to '+this.name);if(t<e||t>o)throw new TypeError('Passing a number "'+Cn(t)+'" from JS side to C/C++ side to an argument of type "'+r+'", which is outside the valid range ['+e+", "+o+"]!");return c?t>>>0:0|t},argPackAdvance:8,readValueFromPointer:In(r,a,0!==e),destructorFunction:null})},g:function(n,r,t){var e=[Int8Array,Uint8Array,Int16Array,Uint16Array,Int32Array,Uint32Array,Float32Array,Float64Array][r];function o(n){var r=C,t=r[n>>=2],o=r[n+1];return new e(m,o,t)}gn(n,{name:t=dn(t),fromWireType:o,argPackAdvance:8,readValueFromPointer:o},{ignoreDuplicateRegistrations:!0})},v:function(n,r){var t="std::string"===(r=dn(r));gn(n,{name:r,fromWireType:function(n){var r,e=C[n>>2];if(t)for(var o=n+4,a=0;a<=e;++a){var i=n+4+a;if(a==e||0==b[i]){var u=g(o,i-o);void 0===r?r=u:(r+=String.fromCharCode(0),r+=u),o=i+1}}else{var c=new Array(e);for(a=0;a<e;++a)c[a]=String.fromCharCode(b[n+4+a]);r=c.join("")}return Mn(n),r},toWireType:function(n,r){var e;r instanceof ArrayBuffer&&(r=new Uint8Array(r));var o="string"==typeof r;o||r instanceof Uint8Array||r instanceof Uint8ClampedArray||r instanceof Int8Array||yn("Cannot pass non-string to std::string"),e=t&&o?function(){return function(n){for(var r=0,t=0;t<n.length;++t){var e=n.charCodeAt(t);e>=55296&&e<=57343&&(e=65536+((1023&e)<<10)|1023&n.charCodeAt(++t)),e<=127?++r:r+=e<=2047?2:e<=65535?3:4}return r}(r)}:function(){return r.length};var a=e(),i=Vn(4+a+1);if(C[i>>2]=a,t&&o)!function(n,r,t,e){if(!(e>0))return 0;for(var o=t+e-1,a=0;a<n.length;++a){var i=n.charCodeAt(a);if(i>=55296&&i<=57343&&(i=65536+((1023&i)<<10)|1023&n.charCodeAt(++a)),i<=127){if(t>=o)break;r[t++]=i}else if(i<=2047){if(t+1>=o)break;r[t++]=192|i>>6,r[t++]=128|63&i}else if(i<=65535){if(t+2>=o)break;r[t++]=224|i>>12,r[t++]=128|i>>6&63,r[t++]=128|63&i}else{if(t+3>=o)break;r[t++]=240|i>>18,r[t++]=128|i>>12&63,r[t++]=128|i>>6&63,r[t++]=128|63&i}}r[t]=0}(r,b,i+4,a+1);else if(o)for(var u=0;u<a;++u){var c=r.charCodeAt(u);c>255&&(Mn(i),yn("String has UTF-16 code units that do not fit in 8 bits")),b[i+4+u]=c}else for(u=0;u<a;++u)b[i+4+u]=r[u];return null!==n&&n.push(Mn,i),i},argPackAdvance:8,readValueFromPointer:Q,destructorFunction:function(n){Mn(n)}})},o:function(n,r,t){var e,o,a,i,u;t=dn(t),2===r?(e=k,o=F,i=R,a=function(){return A},u=1):4===r&&(e=I,o=S,i=U,a=function(){return C},u=2),gn(n,{name:t,fromWireType:function(n){for(var t,o=C[n>>2],i=a(),c=n+4,f=0;f<=o;++f){var l=n+4+f*r;if(f==o||0==i[l>>u]){var s=e(c,l-c);void 0===t?t=s:(t+=String.fromCharCode(0),t+=s),c=l+r}}return Mn(n),t},toWireType:function(n,e){"string"!=typeof e&&yn("Cannot pass non-string to C++ string type "+t);var a=i(e),c=Vn(4+a+r);return C[c>>2]=a>>u,o(e,c+4,a+r),null!==n&&n.push(Mn,c),c},argPackAdvance:8,readValueFromPointer:Q,destructorFunction:function(n){Mn(n)}})},H:function(n,r,t,e,o,a){$[n]={name:dn(r),rawConstructor:kn(t,e),rawDestructor:kn(o,a),fields:[]}},f:function(n,r,t,e,o,a,i,u,c,f){$[n].fields.push({fieldName:dn(r),getterReturnType:t,getter:kn(e,o),getterContext:a,setterArgumentType:i,setter:kn(u,c),setterContext:f})},R:function(n,r){gn(n,{isVoid:!0,name:r=dn(r),argPackAdvance:0,fromWireType:function(){},toWireType:function(n,r){}})},I:function(){throw"longjmp"},j:bn,S:function(n){return 0===n?_n.toHandle(Un()):(n=void 0===(t=Sn[r=n])?dn(r):t,_n.toHandle(Un()[n]));var r,t},x:function(n){n>4&&(wn[n].refcount+=1)},N:function(n,t,e,o){n=_n.toValue(n);var a=On[t];return a||(a=function(n){for(var t="",e=0;e<n;++e)t+=(0!==e?", ":"")+"arg"+e;var o="return function emval_allocator_"+n+"(constructor, argTypes, args) {\n";for(e=0;e<n;++e)o+="var argType"+e+" = requireRegisteredType(Module['HEAP32'][(argTypes >>> 2) + "+e+'], "parameter '+e+'");\nvar arg'+e+" = argType"+e+".readValueFromPointer(args);\nargs += argType"+e+"['argPackAdvance'];\n";return o+="var obj = new constructor("+t+");\nreturn valueToHandle(obj);\n}\n",new Function("requireRegisteredType","Module","valueToHandle",o)(Hn,r,_n.toHandle)}(t),On[t]=a),a(n,e,o)},d:function(){B("")},J:function(n,r,t){b.copyWithin(n,r,r+t)},n:function(n){var r,t,e=b.length,o=2147483648;if((n>>>=0)>o)return!1;for(var a=1;a<=4;a*=2){var i=e*(1+.2/a);if(i=Math.min(i,n+100663296),xn(Math.min(o,((r=Math.max(n,i))%(t=65536)>0&&(r+=t-r%t),r))))return!0}return!1},t:function(n){return 0},K:function(n,r,t,e){var o=Z.getStreamFromFD(n),a=Z.doReadv(o,r,t);return _[e>>2]=a,0},A:function(n,r,t,e,o){},L:function(n,r,t,e){for(var o=0,a=0;a<t;a++){var i=_[r>>2],u=_[r+4>>2];r+=8;for(var c=0;c<u;c++)Z.printChar(n,b[i+c]);o+=u}return _[e>>2]=o,0},b:function(){return d},h:function(n,r,t){var e=qn();try{return Y(n)(r,t)}catch(n){if(Nn(e),n!==n+0&&"longjmp"!==n)throw n;zn(1,0)}},k:function(n,r,t,e,o){var a=qn();try{return Y(n)(r,t,e,o)}catch(n){if(Nn(a),n!==n+0&&"longjmp"!==n)throw n;zn(1,0)}},G:function(n,r,t,e,o,a,i,u,c,f){var l=qn();try{return Y(n)(r,t,e,o,a,i,u,c,f)}catch(n){if(Nn(l),n!==n+0&&"longjmp"!==n)throw n;zn(1,0)}},E:function(n,r,t,e,o,a,i,u,c,f,l){var s=qn();try{return Y(n)(r,t,e,o,a,i,u,c,f,l)}catch(n){if(Nn(s),n!==n+0&&"longjmp"!==n)throw n;zn(1,0)}},F:function(n,r,t,e,o,a,i,u,c,f,l,s){var p=qn();try{return Y(n)(r,t,e,o,a,i,u,c,f,l,s)}catch(n){if(Nn(p),n!==n+0&&"longjmp"!==n)throw n;zn(1,0)}},z:function(n,r,t,e,o,a){var i=qn();try{return Gn(n,r,t,e,o,a)}catch(n){if(Nn(i),n!==n+0&&"longjmp"!==n)throw n;zn(1,0)}},l:function(n,r){var t=qn();try{Y(n)(r)}catch(n){if(Nn(t),n!==n+0&&"longjmp"!==n)throw n;zn(1,0)}},e:function(n,r,t){var e=qn();try{Y(n)(r,t)}catch(n){if(Nn(e),n!==n+0&&"longjmp"!==n)throw n;zn(1,0)}},r:function(n,r,t,e){var o=qn();try{Y(n)(r,t,e)}catch(n){if(Nn(o),n!==n+0&&"longjmp"!==n)throw n;zn(1,0)}},c:function(n,r,t,e,o){var a=qn();try{Y(n)(r,t,e,o)}catch(n){if(Nn(a),n!==n+0&&"longjmp"!==n)throw n;zn(1,0)}},C:function(n,r,t,e,o,a){var i=qn();try{Y(n)(r,t,e,o,a)}catch(n){if(Nn(i),n!==n+0&&"longjmp"!==n)throw n;zn(1,0)}},p:function(n,r,t,e,o,a,i){var u=qn();try{Y(n)(r,t,e,o,a,i)}catch(n){if(Nn(u),n!==n+0&&"longjmp"!==n)throw n;zn(1,0)}},m:function(n,r,t,e,o,a,i,u,c,f,l){var s=qn();try{Y(n)(r,t,e,o,a,i,u,c,f,l)}catch(n){if(Nn(s),n!==n+0&&"longjmp"!==n)throw n;zn(1,0)}},q:function(n,r,t,e,o,a,i,u,c,f,l,s){var p=qn();try{Y(n)(r,t,e,o,a,i,u,c,f,l,s)}catch(n){if(Nn(p),n!==n+0&&"longjmp"!==n)throw n;zn(1,0)}},a:function(n){d=n},w:function(n){var r=Date.now()/1e3|0;return n&&(_[n>>2]=r),r}};!function(){var n={a:Dn};function t(n,t){var e,o=n.exports;r.asm=o,H((v=r.asm.T).buffer),j=r.asm.aa,e=r.asm.U,x.unshift(e),function(){if(V--,r.monitorRunDependencies&&r.monitorRunDependencies(V),0==V&&M){var n=M;M=null,n()}}()}function e(n){t(n.instance)}function a(r){return(!p&&c&&"function"==typeof fetch?fetch(q,{credentials:"same-origin"}).then((function(n){if(!n.ok)throw"failed to load wasm binary file at '"+q+"'";return n.arrayBuffer()})).catch((function(){return G(q)})):Promise.resolve().then((function(){return G(q)}))).then((function(r){return WebAssembly.instantiate(r,n)})).then((function(n){return n})).then(r,(function(n){s("failed to asynchronously prepare wasm: "+n),B(n)}))}if(V++,r.monitorRunDependencies&&r.monitorRunDependencies(V),r.instantiateWasm)try{return r.instantiateWasm(n,t)}catch(n){return s("Module.instantiateWasm callback failed with error: "+n),!1}(p||"function"!=typeof WebAssembly.instantiateStreaming||L(q)||"function"!=typeof fetch?a(e):fetch(q,{credentials:"same-origin"}).then((function(r){return WebAssembly.instantiateStreaming(r,n).then(e,(function(n){return s("wasm streaming compile failed: "+n),s("falling back to ArrayBuffer instantiation"),a(e)}))}))).catch(o)}(),r.___wasm_call_ctors=function(){return(r.___wasm_call_ctors=r.asm.U).apply(null,arguments)};var Vn=r._malloc=function(){return(Vn=r._malloc=r.asm.V).apply(null,arguments)},Mn=r._free=function(){return(Mn=r._free=r.asm.W).apply(null,arguments)},Bn=r.___getTypeName=function(){return(Bn=r.___getTypeName=r.asm.X).apply(null,arguments)};r.___embind_register_native_and_builtin_types=function(){return(r.___embind_register_native_and_builtin_types=r.asm.Y).apply(null,arguments)};var qn=r.stackSave=function(){return(qn=r.stackSave=r.asm.Z).apply(null,arguments)},Nn=r.stackRestore=function(){return(Nn=r.stackRestore=r.asm._).apply(null,arguments)},zn=r._setThrew=function(){return(zn=r._setThrew=r.asm.$).apply(null,arguments)};r.dynCall_jiiiiiiiii=function(){return(r.dynCall_jiiiiiiiii=r.asm.ba).apply(null,arguments)};var Ln,Gn=r.dynCall_ijiii=function(){return(Gn=r.dynCall_ijiii=r.asm.ca).apply(null,arguments)};function Jn(n){function e(){Ln||(Ln=!0,r.calledRun=!0,h||(J(x),t(r),r.onRuntimeInitialized&&r.onRuntimeInitialized(),function(){if(r.postRun)for("function"==typeof r.postRun&&(r.postRun=[r.postRun]);r.postRun.length;)n=r.postRun.shift(),D.unshift(n);var n;J(D)}()))}V>0||(function(){if(r.preRun)for("function"==typeof r.preRun&&(r.preRun=[r.preRun]);r.preRun.length;)n=r.preRun.shift(),O.unshift(n);var n;J(O)}(),V>0||(r.setStatus?(r.setStatus("Running..."),setTimeout((function(){setTimeout((function(){r.setStatus("")}),1),e()}),1)):e()))}if(r.dynCall_jiji=function(){return(r.dynCall_jiji=r.asm.da).apply(null,arguments)},r.dynCall_jiiiiiiii=function(){return(r.dynCall_jiiiiiiii=r.asm.ea).apply(null,arguments)},r.dynCall_jiiiiii=function(){return(r.dynCall_jiiiiii=r.asm.fa).apply(null,arguments)},r.dynCall_jiiiii=function(){return(r.dynCall_jiiiii=r.asm.ga).apply(null,arguments)},r.dynCall_iiijii=function(){return(r.dynCall_iiijii=r.asm.ha).apply(null,arguments)},M=function n(){Ln||Jn(),Ln||(M=n)},r.run=Jn,r.preInit)for("function"==typeof r.preInit&&(r.preInit=[r.preInit]);r.preInit.length>0;)r.preInit.pop()();return Jn(),r.ready});t.default=o}));
