define(["module","require","exports"],(function(n,r,t){var e,o=(e=n.uri,function(r){var t,o;(r=void 0!==(r=r||{})?r:{}).ready=new Promise((function(n,r){t=n,o=r}));var a,i={};for(a in r)r.hasOwnProperty(a)&&(i[a]=r[a]);var u,c=!0,f="";f=self.location.href,e&&(f=e),f=0!==f.indexOf("blob:")?f.substr(0,f.replace(/[?#].*/,"").lastIndexOf("/")+1):"",u=function(n){var r=new XMLHttpRequest;return r.open("GET",n,!1),r.responseType="arraybuffer",r.send(null),new Uint8Array(r.response)};var s=r.print||console.log.bind(console),l=r.printErr||console.warn.bind(console);for(a in i)i.hasOwnProperty(a)&&(r[a]=i[a]);i=null,r.arguments&&r.arguments,r.thisProgram&&r.thisProgram,r.quit&&r.quit;var p,v,d=0;r.wasmBinary&&(p=r.wasmBinary),r.noExitRuntime,"object"!=typeof WebAssembly&&D("no native wasm support detected");var h=!1,y=new TextDecoder("utf8");function g(n,r){if(!n)return"";for(var t=n+r,e=n;!(e>=t)&&b[e];)++e;return y.decode(b.subarray(n,e))}var m,w,b,T,A,_,P,C,E,k,W=new TextDecoder("utf-16le");function F(n,r){for(var t=n,e=t>>1,o=e+r/2;!(e>=o)&&A[e];)++e;return t=e<<1,W.decode(b.subarray(n,t))}function R(n,r,t){if(void 0===t&&(t=2147483647),t<2)return 0;for(var e=r,o=(t-=2)<2*n.length?t/2:n.length,a=0;a<o;++a){var i=n.charCodeAt(a);T[r>>1]=i,r+=2}return T[r>>1]=0,r-e}function j(n){return 2*n.length}function I(n,r){for(var t=0,e="";!(t>=r/4);){var o=_[n+4*t>>2];if(0==o)break;if(++t,o>=65536){var a=o-65536;e+=String.fromCharCode(55296|a>>10,56320|1023&a)}else e+=String.fromCharCode(o)}return e}function S(n,r,t){if(void 0===t&&(t=2147483647),t<4)return 0;for(var e=r,o=e+t-4,a=0;a<n.length;++a){var i=n.charCodeAt(a);if(i>=55296&&i<=57343&&(i=65536+((1023&i)<<10)|1023&n.charCodeAt(++a)),_[r>>2]=i,(r+=4)+4>o)break}return _[r>>2]=0,r-e}function U(n){for(var r=0,t=0;t<n.length;++t){var e=n.charCodeAt(t);e>=55296&&e<=57343&&++t,r+=4}return r}function H(n){m=n,r.HEAP8=w=new Int8Array(n),r.HEAP16=T=new Int16Array(n),r.HEAP32=_=new Int32Array(n),r.HEAPU8=b=new Uint8Array(n),r.HEAPU16=A=new Uint16Array(n),r.HEAPU32=P=new Uint32Array(n),r.HEAPF32=C=new Float32Array(n),r.HEAPF64=E=new Float64Array(n)}r.INITIAL_MEMORY;var O=[],x=[],M=[],V=0,B=null;function D(n){r.onAbort&&r.onAbort(n),l(n="Aborted("+n+")"),h=!0,n+=". Build with -s ASSERTIONS=1 for more info.";var t=new WebAssembly.RuntimeError(n);throw o(t),t}r.preloadedImages={},r.preloadedAudios={};var q,z,N="data:application/octet-stream;base64,";function L(n){return n.startsWith(N)}function G(n){try{if(n==q&&p)return new Uint8Array(p);if(u)return u(n);throw"both async and sync fetching of the wasm failed"}catch(n){D(n)}}function J(n){for(;n.length>0;){var t=n.shift();if("function"!=typeof t){var e=t.func;"number"==typeof e?void 0===t.arg?X(e)():X(e)(t.arg):e(void 0===t.arg?null:t.arg)}else t(r)}}r.locateFile?L(q="avif_dec.wasm")||(z=q,q=r.locateFile?r.locateFile(z,f):f+z):q=new URL("/c/avif_dec-d634d9c0.wasm",n.uri).toString();var K=[];function X(n){var r=K[n];return r||(n>=K.length&&(K.length=n+1),K[n]=r=k.get(n)),r}function Y(n){switch(n){case 1:return 0;case 2:return 1;case 4:return 2;case 8:return 3;default:throw new TypeError("Unknown type size: "+n)}}var Z=void 0;function $(n){for(var r="",t=n;b[t];)r+=Z[b[t++]];return r}var Q={},nn={},rn={},tn=48,en=57;function on(n){if(void 0===n)return"_unknown";var r=(n=n.replace(/[^a-zA-Z0-9_]/g,"$")).charCodeAt(0);return r>=tn&&r<=en?"_"+n:n}function an(n,r){return n=on(n),new Function("body","return function "+n+'() {\n    "use strict";    return body.apply(this, arguments);\n};\n')(r)}function un(n,r){var t=an(r,(function(n){this.name=r,this.message=n;var t=new Error(n).stack;void 0!==t&&(this.stack=this.toString()+"\n"+t.replace(/^Error(:[^\n]*)?\n/,""))}));return t.prototype=Object.create(n.prototype),t.prototype.constructor=t,t.prototype.toString=function(){return void 0===this.message?this.name:this.name+": "+this.message},t}var cn=void 0;function fn(n){throw new cn(n)}var sn=void 0;function ln(n){throw new sn(n)}function pn(n,r,t){if(t=t||{},!("argPackAdvance"in r))throw new TypeError("registerType registeredInstance requires argPackAdvance");var e=r.name;if(n||fn('type "'+e+'" must have a positive integer typeid pointer'),nn.hasOwnProperty(n)){if(t.ignoreDuplicateRegistrations)return;fn("Cannot register type '"+e+"' twice")}if(nn[n]=r,delete rn[n],Q.hasOwnProperty(n)){var o=Q[n];delete Q[n],o.forEach((function(n){n()}))}}var vn=[],dn=[{},{value:void 0},{value:null},{value:!0},{value:!1}];function hn(n){n>4&&0==--dn[n].refcount&&(dn[n]=void 0,vn.push(n))}function yn(){for(var n=0,r=5;r<dn.length;++r)void 0!==dn[r]&&++n;return n}function gn(){for(var n=5;n<dn.length;++n)if(void 0!==dn[n])return dn[n];return null}var mn={toValue:function(n){return n||fn("Cannot use deleted val. handle = "+n),dn[n].value},toHandle:function(n){switch(n){case void 0:return 1;case null:return 2;case!0:return 3;case!1:return 4;default:var r=vn.length?vn.pop():dn.length;return dn[r]={refcount:1,value:n},r}}};function wn(n){return this.fromWireType(P[n>>2])}function bn(n){if(null===n)return"null";var r=typeof n;return"object"===r||"array"===r||"function"===r?n.toString():""+n}function Tn(n,r){switch(r){case 2:return function(n){return this.fromWireType(C[n>>2])};case 3:return function(n){return this.fromWireType(E[n>>3])};default:throw new TypeError("Unknown float type: "+n)}}function An(n){for(;n.length;){var r=n.pop();n.pop()(r)}}function _n(n,r,t,e,o){var a=r.length;a<2&&fn("argTypes array size mismatch! Must at least get return value and 'this' types!");for(var i=null!==r[1]&&null!==t,u=!1,c=1;c<r.length;++c)if(null!==r[c]&&void 0===r[c].destructorFunction){u=!0;break}var f="void"!==r[0].name,s="",l="";for(c=0;c<a-2;++c)s+=(0!==c?", ":"")+"arg"+c,l+=(0!==c?", ":"")+"arg"+c+"Wired";var p="return function "+on(n)+"("+s+") {\nif (arguments.length !== "+(a-2)+") {\nthrowBindingError('function "+n+" called with ' + arguments.length + ' arguments, expected "+(a-2)+" args!');\n}\n";u&&(p+="var destructors = [];\n");var v=u?"destructors":"null",d=["throwBindingError","invoker","fn","runDestructors","retType","classParam"],h=[fn,e,o,An,r[0],r[1]];for(i&&(p+="var thisWired = classParam.toWireType("+v+", this);\n"),c=0;c<a-2;++c)p+="var arg"+c+"Wired = argType"+c+".toWireType("+v+", arg"+c+"); // "+r[c+2].name+"\n",d.push("argType"+c),h.push(r[c+2]);if(i&&(l="thisWired"+(l.length>0?", ":"")+l),p+=(f?"var rv = ":"")+"invoker(fn"+(l.length>0?", ":"")+l+");\n",u)p+="runDestructors(destructors);\n";else for(c=i?1:2;c<r.length;++c){var y=1===c?"thisWired":"arg"+(c-2)+"Wired";null!==r[c].destructorFunction&&(p+=y+"_dtor("+y+"); // "+r[c].name+"\n",d.push(y+"_dtor"),h.push(r[c].destructorFunction))}return f&&(p+="var ret = retType.fromWireType(rv);\nreturn ret;\n"),p+="}\n",d.push(p),function(n,r){if(!(n instanceof Function))throw new TypeError("new_ called with constructor type "+typeof n+" which is not a function");var t=an(n.name||"unknownFunctionName",(function(){}));t.prototype=n.prototype;var e=new t,o=n.apply(e,r);return o instanceof Object?o:e}(Function,d).apply(null,h)}function Pn(n,t,e){r.hasOwnProperty(n)?((void 0===e||void 0!==r[n].overloadTable&&void 0!==r[n].overloadTable[e])&&fn("Cannot register public name '"+n+"' twice"),function(n,r,t){if(void 0===n[r].overloadTable){var e=n[r];n[r]=function(){return n[r].overloadTable.hasOwnProperty(arguments.length)||fn("Function '"+t+"' called with an invalid number of arguments ("+arguments.length+") - expects one of ("+n[r].overloadTable+")!"),n[r].overloadTable[arguments.length].apply(this,arguments)},n[r].overloadTable=[],n[r].overloadTable[e.argCount]=e}}(r,n,n),r.hasOwnProperty(e)&&fn("Cannot register multiple overloads of a function with the same number of arguments ("+e+")!"),r[n].overloadTable[e]=t):(r[n]=t,void 0!==e&&(r[n].numArguments=e))}function Cn(n,t,e){return n.includes("j")?function(n,t,e){var o=r["dynCall_"+n];return e&&e.length?o.apply(null,[t].concat(e)):o.call(null,t)}(n,t,e):X(t).apply(null,e)}function En(n,r){var t,e,o,a=(n=$(n)).includes("j")?(t=n,e=r,o=[],function(){o.length=arguments.length;for(var n=0;n<arguments.length;n++)o[n]=arguments[n];return Cn(t,e,o)}):X(r);return"function"!=typeof a&&fn("unknown function pointer with signature "+n+": "+r),a}var kn=void 0;function Wn(n){var r=Vn(n),t=$(r);return Mn(r),t}function Fn(n,r,t){switch(r){case 0:return t?function(n){return w[n]}:function(n){return b[n]};case 1:return t?function(n){return T[n>>1]}:function(n){return A[n>>1]};case 2:return t?function(n){return _[n>>2]}:function(n){return P[n>>2]};default:throw new TypeError("Unknown integer type: "+n)}}var Rn={};function jn(){return"object"==typeof globalThis?globalThis:Function("return this")()}function In(n,r){var t=nn[n];return void 0===t&&fn(r+" has unknown type "+Wn(n)),t}var Sn={};function Un(n){try{return v.grow(n-m.byteLength+65535>>>16),H(v.buffer),1}catch(n){}}var Hn={mappings:{},buffers:[null,[],[]],printChar:function(n,r){var t=Hn.buffers[n];0===r||10===r?((1===n?s:l)(function(n,r,t){for(var e=r+t,o=r;n[o]&&!(o>=e);)++o;return y.decode(n.subarray?n.subarray(r,o):new Uint8Array(n.slice(r,o)))}(t,0)),t.length=0):t.push(r)},varargs:void 0,get:function(){return Hn.varargs+=4,_[Hn.varargs-4>>2]},getStr:function(n){return g(n)},get64:function(n,r){return n}};!function(){for(var n=new Array(256),r=0;r<256;++r)n[r]=String.fromCharCode(r);Z=n}(),cn=r.BindingError=un(Error,"BindingError"),sn=r.InternalError=un(Error,"InternalError"),r.count_emval_handles=yn,r.get_first_emval=gn,kn=r.UnboundTypeError=un(Error,"UnboundTypeError");var On={u:function(n,r,t,e,o){},p:function(n,r,t,e,o){var a=Y(t);pn(n,{name:r=$(r),fromWireType:function(n){return!!n},toWireType:function(n,r){return r?e:o},argPackAdvance:8,readValueFromPointer:function(n){var e;if(1===t)e=w;else if(2===t)e=T;else{if(4!==t)throw new TypeError("Unknown boolean type size: "+r);e=_}return this.fromWireType(e[n>>a])},destructorFunction:null})},D:function(n,r){pn(n,{name:r=$(r),fromWireType:function(n){var r=mn.toValue(n);return hn(n),r},toWireType:function(n,r){return mn.toHandle(r)},argPackAdvance:8,readValueFromPointer:wn,destructorFunction:null})},o:function(n,r,t){var e=Y(t);pn(n,{name:r=$(r),fromWireType:function(n){return n},toWireType:function(n,r){if("number"!=typeof r&&"boolean"!=typeof r)throw new TypeError('Cannot convert "'+bn(r)+'" to '+this.name);return r},argPackAdvance:8,readValueFromPointer:Tn(r,e),destructorFunction:null})},s:function(n,t,e,o,a,i){var u=function(n,r){for(var t=[],e=0;e<n;e++)t.push(_[(r>>2)+e]);return t}(t,e);n=$(n),a=En(o,a),Pn(n,(function(){!function(n,r){var t=[],e={};throw r.forEach((function n(r){e[r]||nn[r]||(rn[r]?rn[r].forEach(n):(t.push(r),e[r]=!0))})),new kn(n+": "+t.map(Wn).join([", "]))}("Cannot call "+n+" due to unbound types",u)}),t-1),function(n,r,t){function e(r){var e=t(r);e.length!==n.length&&ln("Mismatched type converter count");for(var o=0;o<n.length;++o)pn(n[o],e[o])}n.forEach((function(n){rn[n]=r}));var o=new Array(r.length),a=[],i=0;r.forEach((function(n,r){nn.hasOwnProperty(n)?o[r]=nn[n]:(a.push(n),Q.hasOwnProperty(n)||(Q[n]=[]),Q[n].push((function(){o[r]=nn[n],++i===a.length&&e(o)})))})),0===a.length&&e(o)}([],u,(function(e){var o=[e[0],null].concat(e.slice(1));return function(n,t,e){r.hasOwnProperty(n)||ln("Replacing nonexistant public symbol"),void 0!==r[n].overloadTable&&void 0!==e?r[n].overloadTable[e]=t:(r[n]=t,r[n].argCount=e)}(n,_n(n,o,null,a,i),t-1),[]}))},e:function(n,r,t,e,o){r=$(r),-1===o&&(o=4294967295);var a=Y(t),i=function(n){return n};if(0===e){var u=32-8*t;i=function(n){return n<<u>>>u}}var c=r.includes("unsigned");pn(n,{name:r,fromWireType:i,toWireType:function(n,t){if("number"!=typeof t&&"boolean"!=typeof t)throw new TypeError('Cannot convert "'+bn(t)+'" to '+this.name);if(t<e||t>o)throw new TypeError('Passing a number "'+bn(t)+'" from JS side to C/C++ side to an argument of type "'+r+'", which is outside the valid range ['+e+", "+o+"]!");return c?t>>>0:0|t},argPackAdvance:8,readValueFromPointer:Fn(r,a,0!==e),destructorFunction:null})},d:function(n,r,t){var e=[Int8Array,Uint8Array,Int16Array,Uint16Array,Int32Array,Uint32Array,Float32Array,Float64Array][r];function o(n){var r=P,t=r[n>>=2],o=r[n+1];return new e(m,o,t)}pn(n,{name:t=$(t),fromWireType:o,argPackAdvance:8,readValueFromPointer:o},{ignoreDuplicateRegistrations:!0})},k:function(n,r){var t="std::string"===(r=$(r));pn(n,{name:r,fromWireType:function(n){var r,e=P[n>>2];if(t)for(var o=n+4,a=0;a<=e;++a){var i=n+4+a;if(a==e||0==b[i]){var u=g(o,i-o);void 0===r?r=u:(r+=String.fromCharCode(0),r+=u),o=i+1}}else{var c=new Array(e);for(a=0;a<e;++a)c[a]=String.fromCharCode(b[n+4+a]);r=c.join("")}return Mn(n),r},toWireType:function(n,r){var e;r instanceof ArrayBuffer&&(r=new Uint8Array(r));var o="string"==typeof r;o||r instanceof Uint8Array||r instanceof Uint8ClampedArray||r instanceof Int8Array||fn("Cannot pass non-string to std::string"),e=t&&o?function(){return function(n){for(var r=0,t=0;t<n.length;++t){var e=n.charCodeAt(t);e>=55296&&e<=57343&&(e=65536+((1023&e)<<10)|1023&n.charCodeAt(++t)),e<=127?++r:r+=e<=2047?2:e<=65535?3:4}return r}(r)}:function(){return r.length};var a=e(),i=xn(4+a+1);if(P[i>>2]=a,t&&o)!function(n,r,t,e){if(!(e>0))return 0;for(var o=t+e-1,a=0;a<n.length;++a){var i=n.charCodeAt(a);if(i>=55296&&i<=57343&&(i=65536+((1023&i)<<10)|1023&n.charCodeAt(++a)),i<=127){if(t>=o)break;r[t++]=i}else if(i<=2047){if(t+1>=o)break;r[t++]=192|i>>6,r[t++]=128|63&i}else if(i<=65535){if(t+2>=o)break;r[t++]=224|i>>12,r[t++]=128|i>>6&63,r[t++]=128|63&i}else{if(t+3>=o)break;r[t++]=240|i>>18,r[t++]=128|i>>12&63,r[t++]=128|i>>6&63,r[t++]=128|63&i}}r[t]=0}(r,b,i+4,a+1);else if(o)for(var u=0;u<a;++u){var c=r.charCodeAt(u);c>255&&(Mn(i),fn("String has UTF-16 code units that do not fit in 8 bits")),b[i+4+u]=c}else for(u=0;u<a;++u)b[i+4+u]=r[u];return null!==n&&n.push(Mn,i),i},argPackAdvance:8,readValueFromPointer:wn,destructorFunction:function(n){Mn(n)}})},j:function(n,r,t){var e,o,a,i,u;t=$(t),2===r?(e=F,o=R,i=j,a=function(){return A},u=1):4===r&&(e=I,o=S,i=U,a=function(){return P},u=2),pn(n,{name:t,fromWireType:function(n){for(var t,o=P[n>>2],i=a(),c=n+4,f=0;f<=o;++f){var s=n+4+f*r;if(f==o||0==i[s>>u]){var l=e(c,s-c);void 0===t?t=l:(t+=String.fromCharCode(0),t+=l),c=s+r}}return Mn(n),t},toWireType:function(n,e){"string"!=typeof e&&fn("Cannot pass non-string to C++ string type "+t);var a=i(e),c=xn(4+a+r);return P[c>>2]=a>>u,o(e,c+4,a+r),null!==n&&n.push(Mn,c),c},argPackAdvance:8,readValueFromPointer:wn,destructorFunction:function(n){Mn(n)}})},q:function(n,r){pn(n,{isVoid:!0,name:r=$(r),argPackAdvance:0,fromWireType:function(){},toWireType:function(n,r){}})},z:function(){throw"longjmp"},g:hn,h:function(n){return 0===n?mn.toHandle(jn()):(n=void 0===(t=Rn[r=n])?$(r):t,mn.toHandle(jn()[n]));var r,t},l:function(n){n>4&&(dn[n].refcount+=1)},n:function(n,t,e,o){n=mn.toValue(n);var a=Sn[t];return a||(a=function(n){for(var t="",e=0;e<n;++e)t+=(0!==e?", ":"")+"arg"+e;var o="return function emval_allocator_"+n+"(constructor, argTypes, args) {\n";for(e=0;e<n;++e)o+="var argType"+e+" = requireRegisteredType(Module['HEAP32'][(argTypes >>> 2) + "+e+'], "parameter '+e+'");\nvar arg'+e+" = argType"+e+".readValueFromPointer(args);\nargs += argType"+e+"['argPackAdvance'];\n";return o+="var obj = new constructor("+t+");\nreturn valueToHandle(obj);\n}\n",new Function("requireRegisteredType","Module","valueToHandle",o)(In,r,mn.toHandle)}(t),Sn[t]=a),a(n,e,o)},a:function(){D("")},A:function(n,r,t){b.copyWithin(n,r,r+t)},i:function(n){var r,t,e=b.length,o=2147483648;if((n>>>=0)>o)return!1;for(var a=1;a<=4;a*=2){var i=e*(1+.2/a);if(i=Math.min(i,n+100663296),Un(Math.min(o,((r=Math.max(n,i))%(t=65536)>0&&(r+=t-r%t),r))))return!0}return!1},C:function(n){return 0},t:function(n,r,t,e,o){},B:function(n,r,t,e){for(var o=0,a=0;a<t;a++){var i=_[r>>2],u=_[r+4>>2];r+=8;for(var c=0;c<u;c++)Hn.printChar(n,b[i+c]);o+=u}return _[e>>2]=o,0},b:function(){return d},f:function(n,r,t){var e=Dn();try{return X(n)(r,t)}catch(n){if(qn(e),n!==n+0&&"longjmp"!==n)throw n;zn(1,0)}},v:function(n,r,t,e,o){var a=Dn();try{return X(n)(r,t,e,o)}catch(n){if(qn(a),n!==n+0&&"longjmp"!==n)throw n;zn(1,0)}},w:function(n){var r=Dn();try{X(n)()}catch(n){if(qn(r),n!==n+0&&"longjmp"!==n)throw n;zn(1,0)}},x:function(n,r){var t=Dn();try{X(n)(r)}catch(n){if(qn(t),n!==n+0&&"longjmp"!==n)throw n;zn(1,0)}},m:function(n,r,t,e,o){var a=Dn();try{X(n)(r,t,e,o)}catch(n){if(qn(a),n!==n+0&&"longjmp"!==n)throw n;zn(1,0)}},y:function(n,r,t,e,o,a,i,u){var c=Dn();try{X(n)(r,t,e,o,a,i,u)}catch(n){if(qn(c),n!==n+0&&"longjmp"!==n)throw n;zn(1,0)}},c:function(n){d=n},r:function(){l("missing function: setjmp"),D(-1)}};!function(){var n={a:On};function t(n,t){var e,o=n.exports;r.asm=o,H((v=r.asm.E).buffer),k=r.asm.N,e=r.asm.F,x.unshift(e),function(){if(V--,r.monitorRunDependencies&&r.monitorRunDependencies(V),0==V&&B){var n=B;B=null,n()}}()}function e(n){t(n.instance)}function a(r){return(!p&&c&&"function"==typeof fetch?fetch(q,{credentials:"same-origin"}).then((function(n){if(!n.ok)throw"failed to load wasm binary file at '"+q+"'";return n.arrayBuffer()})).catch((function(){return G(q)})):Promise.resolve().then((function(){return G(q)}))).then((function(r){return WebAssembly.instantiate(r,n)})).then((function(n){return n})).then(r,(function(n){l("failed to asynchronously prepare wasm: "+n),D(n)}))}if(V++,r.monitorRunDependencies&&r.monitorRunDependencies(V),r.instantiateWasm)try{return r.instantiateWasm(n,t)}catch(n){return l("Module.instantiateWasm callback failed with error: "+n),!1}(p||"function"!=typeof WebAssembly.instantiateStreaming||L(q)||"function"!=typeof fetch?a(e):fetch(q,{credentials:"same-origin"}).then((function(r){return WebAssembly.instantiateStreaming(r,n).then(e,(function(n){return l("wasm streaming compile failed: "+n),l("falling back to ArrayBuffer instantiation"),a(e)}))}))).catch(o)}(),r.___wasm_call_ctors=function(){return(r.___wasm_call_ctors=r.asm.F).apply(null,arguments)};var xn=r._malloc=function(){return(xn=r._malloc=r.asm.G).apply(null,arguments)},Mn=r._free=function(){return(Mn=r._free=r.asm.H).apply(null,arguments)},Vn=r.___getTypeName=function(){return(Vn=r.___getTypeName=r.asm.I).apply(null,arguments)};r.___embind_register_native_and_builtin_types=function(){return(r.___embind_register_native_and_builtin_types=r.asm.J).apply(null,arguments)};var Bn,Dn=r.stackSave=function(){return(Dn=r.stackSave=r.asm.K).apply(null,arguments)},qn=r.stackRestore=function(){return(qn=r.stackRestore=r.asm.L).apply(null,arguments)},zn=r._setThrew=function(){return(zn=r._setThrew=r.asm.M).apply(null,arguments)};function Nn(n){function e(){Bn||(Bn=!0,r.calledRun=!0,h||(J(x),t(r),r.onRuntimeInitialized&&r.onRuntimeInitialized(),function(){if(r.postRun)for("function"==typeof r.postRun&&(r.postRun=[r.postRun]);r.postRun.length;)n=r.postRun.shift(),M.unshift(n);var n;J(M)}()))}V>0||(function(){if(r.preRun)for("function"==typeof r.preRun&&(r.preRun=[r.preRun]);r.preRun.length;)n=r.preRun.shift(),O.unshift(n);var n;J(O)}(),V>0||(r.setStatus?(r.setStatus("Running..."),setTimeout((function(){setTimeout((function(){r.setStatus("")}),1),e()}),1)):e()))}if(r.dynCall_iiijii=function(){return(r.dynCall_iiijii=r.asm.O).apply(null,arguments)},r.dynCall_jiji=function(){return(r.dynCall_jiji=r.asm.P).apply(null,arguments)},B=function n(){Bn||Nn(),Bn||(B=n)},r.run=Nn,r.preInit)for("function"==typeof r.preInit&&(r.preInit=[r.preInit]);r.preInit.length>0;)r.preInit.pop()();return Nn(),r.ready});t.default=o}));
