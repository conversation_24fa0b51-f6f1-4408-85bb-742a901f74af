define(["module","require","exports"],(function(n,r,t){var e,o=(e=n.uri,function(r){var t,o;(r=void 0!==(r=r||{})?r:{}).ready=new Promise((function(n,r){t=n,o=r}));var i,a={};for(i in r)r.hasOwnProperty(i)&&(a[i]=r[i]);var u,c=!0,f="";f=self.location.href,e&&(f=e),f=0!==f.indexOf("blob:")?f.substr(0,f.lastIndexOf("/")+1):"",u=function(n){var r=new XMLHttpRequest;return r.open("GET",n,!1),r.responseType="arraybuffer",r.send(null),new Uint8Array(r.response)};var s,l,p=r.print||console.log.bind(console),h=r.printErr||console.warn.bind(console);for(i in a)a.hasOwnProperty(i)&&(r[i]=a[i]);a=null,r.arguments&&r.arguments,r.thisProgram&&r.thisProgram,r.quit&&r.quit,r.wasmBinary&&(s=r.wasmBinary),r.noExitRuntime,"object"!=typeof WebAssembly&&M("no native wasm support detected");var v=!1,d=new TextDecoder("utf8");function y(n,r){if(!n)return"";for(var t=n+r,e=n;!(e>=t)&&w[e];)++e;return d.decode(w.subarray(n,e))}var g,m,w,T,_,b,E,F,A,C,P=new TextDecoder("utf-16le");function R(n,r){for(var t=n,e=t>>1,o=e+r/2;!(e>=o)&&_[e];)++e;return t=e<<1,P.decode(w.subarray(n,t))}function O(n,r,t){if(void 0===t&&(t=2147483647),t<2)return 0;for(var e=r,o=(t-=2)<2*n.length?t/2:n.length,i=0;i<o;++i){var a=n.charCodeAt(i);T[r>>1]=a,r+=2}return T[r>>1]=0,r-e}function S(n){return 2*n.length}function W(n,r){for(var t=0,e="";!(t>=r/4);){var o=b[n+4*t>>2];if(0==o)break;if(++t,o>=65536){var i=o-65536;e+=String.fromCharCode(55296|i>>10,56320|1023&i)}else e+=String.fromCharCode(o)}return e}function k(n,r,t){if(void 0===t&&(t=2147483647),t<4)return 0;for(var e=r,o=e+t-4,i=0;i<n.length;++i){var a=n.charCodeAt(i);if(a>=55296&&a<=57343&&(a=65536+((1023&a)<<10)|1023&n.charCodeAt(++i)),b[r>>2]=a,(r+=4)+4>o)break}return b[r>>2]=0,r-e}function U(n){for(var r=0,t=0;t<n.length;++t){var e=n.charCodeAt(t);e>=55296&&e<=57343&&++t,r+=4}return r}function I(n){g=n,r.HEAP8=m=new Int8Array(n),r.HEAP16=T=new Int16Array(n),r.HEAP32=b=new Int32Array(n),r.HEAPU8=w=new Uint8Array(n),r.HEAPU16=_=new Uint16Array(n),r.HEAPU32=E=new Uint32Array(n),r.HEAPF32=F=new Float32Array(n),r.HEAPF64=A=new Float64Array(n)}r.INITIAL_MEMORY;var x=[],D=[],H=[],j=0,N=null;function M(n){r.onAbort&&r.onAbort(n),h(n+=""),v=!0,n="abort("+n+"). Build with -s ASSERTIONS=1 for more info.";var t=new WebAssembly.RuntimeError(n);throw o(t),t}r.preloadedImages={},r.preloadedAudios={};var B,V="data:application/octet-stream;base64,";function q(n){return n.startsWith(V)}if(r.locateFile)q(z="wp2_enc.wasm")||(B=z,z=r.locateFile?r.locateFile(B,f):f+B);else var z=new URL("/c/wp2_enc-89317929.wasm",n.uri).toString();function G(n){try{if(n==z&&s)return new Uint8Array(s);if(u)return u(n);throw"both async and sync fetching of the wasm failed"}catch(n){M(n)}}function Z(n){for(;n.length>0;){var t=n.shift();if("function"!=typeof t){var e=t.func;"number"==typeof e?void 0===t.arg?C.get(e)():C.get(e)(t.arg):e(void 0===t.arg?null:t.arg)}else t(r)}}var L={DESTRUCTOR_OFFSET:0,REFCOUNT_OFFSET:4,TYPE_OFFSET:8,CAUGHT_OFFSET:12,RETHROWN_OFFSET:13,SIZE:16};function Y(n){this.excPtr=n,this.ptr=n-L.SIZE,this.set_type=function(n){b[this.ptr+L.TYPE_OFFSET>>2]=n},this.get_type=function(){return b[this.ptr+L.TYPE_OFFSET>>2]},this.set_destructor=function(n){b[this.ptr+L.DESTRUCTOR_OFFSET>>2]=n},this.get_destructor=function(){return b[this.ptr+L.DESTRUCTOR_OFFSET>>2]},this.set_refcount=function(n){b[this.ptr+L.REFCOUNT_OFFSET>>2]=n},this.set_caught=function(n){n=n?1:0,m[this.ptr+L.CAUGHT_OFFSET|0]=n},this.get_caught=function(){return 0!=m[this.ptr+L.CAUGHT_OFFSET|0]},this.set_rethrown=function(n){n=n?1:0,m[this.ptr+L.RETHROWN_OFFSET|0]=n},this.get_rethrown=function(){return 0!=m[this.ptr+L.RETHROWN_OFFSET|0]},this.init=function(n,r){this.set_type(n),this.set_destructor(r),this.set_refcount(0),this.set_caught(!1),this.set_rethrown(!1)},this.add_ref=function(){var n=b[this.ptr+L.REFCOUNT_OFFSET>>2];b[this.ptr+L.REFCOUNT_OFFSET>>2]=n+1},this.release_ref=function(){var n=b[this.ptr+L.REFCOUNT_OFFSET>>2];return b[this.ptr+L.REFCOUNT_OFFSET>>2]=n-1,1===n}}var J={};function X(n){for(;n.length;){var r=n.pop();n.pop()(r)}}function $(n){return this.fromWireType(E[n>>2])}var K={},Q={},nn={},rn=48,tn=57;function en(n){if(void 0===n)return"_unknown";var r=(n=n.replace(/[^a-zA-Z0-9_]/g,"$")).charCodeAt(0);return r>=rn&&r<=tn?"_"+n:n}function on(n,r){return n=en(n),new Function("body","return function "+n+'() {\n    "use strict";    return body.apply(this, arguments);\n};\n')(r)}function an(n,r){var t=on(r,(function(n){this.name=r,this.message=n;var t=new Error(n).stack;void 0!==t&&(this.stack=this.toString()+"\n"+t.replace(/^Error(:[^\n]*)?\n/,""))}));return t.prototype=Object.create(n.prototype),t.prototype.constructor=t,t.prototype.toString=function(){return void 0===this.message?this.name:this.name+": "+this.message},t}var un=void 0;function cn(n){throw new un(n)}function fn(n,r,t){function e(r){var e=t(r);e.length!==n.length&&cn("Mismatched type converter count");for(var o=0;o<n.length;++o)dn(n[o],e[o])}n.forEach((function(n){nn[n]=r}));var o=new Array(r.length),i=[],a=0;r.forEach((function(n,r){Q.hasOwnProperty(n)?o[r]=Q[n]:(i.push(n),K.hasOwnProperty(n)||(K[n]=[]),K[n].push((function(){o[r]=Q[n],++a===i.length&&e(o)})))})),0===i.length&&e(o)}function sn(n){switch(n){case 1:return 0;case 2:return 1;case 4:return 2;case 8:return 3;default:throw new TypeError("Unknown type size: "+n)}}var ln=void 0;function pn(n){for(var r="",t=n;w[t];)r+=ln[w[t++]];return r}var hn=void 0;function vn(n){throw new hn(n)}function dn(n,r,t){if(t=t||{},!("argPackAdvance"in r))throw new TypeError("registerType registeredInstance requires argPackAdvance");var e=r.name;if(n||vn('type "'+e+'" must have a positive integer typeid pointer'),Q.hasOwnProperty(n)){if(t.ignoreDuplicateRegistrations)return;vn("Cannot register type '"+e+"' twice")}if(Q[n]=r,delete nn[n],K.hasOwnProperty(n)){var o=K[n];delete K[n],o.forEach((function(n){n()}))}}var yn=[],gn=[{},{value:void 0},{value:null},{value:!0},{value:!1}];function mn(n){n>4&&0==--gn[n].refcount&&(gn[n]=void 0,yn.push(n))}function wn(){for(var n=0,r=5;r<gn.length;++r)void 0!==gn[r]&&++n;return n}function Tn(){for(var n=5;n<gn.length;++n)if(void 0!==gn[n])return gn[n];return null}function _n(n){switch(n){case void 0:return 1;case null:return 2;case!0:return 3;case!1:return 4;default:var r=yn.length?yn.pop():gn.length;return gn[r]={refcount:1,value:n},r}}function bn(n){if(null===n)return"null";var r=typeof n;return"object"===r||"array"===r||"function"===r?n.toString():""+n}function En(n,r){switch(r){case 2:return function(n){return this.fromWireType(F[n>>2])};case 3:return function(n){return this.fromWireType(A[n>>3])};default:throw new TypeError("Unknown float type: "+n)}}function Fn(n,r,t,e,o){var i=r.length;i<2&&vn("argTypes array size mismatch! Must at least get return value and 'this' types!");for(var a=null!==r[1]&&null!==t,u=!1,c=1;c<r.length;++c)if(null!==r[c]&&void 0===r[c].destructorFunction){u=!0;break}var f="void"!==r[0].name,s="",l="";for(c=0;c<i-2;++c)s+=(0!==c?", ":"")+"arg"+c,l+=(0!==c?", ":"")+"arg"+c+"Wired";var p="return function "+en(n)+"("+s+") {\nif (arguments.length !== "+(i-2)+") {\nthrowBindingError('function "+n+" called with ' + arguments.length + ' arguments, expected "+(i-2)+" args!');\n}\n";u&&(p+="var destructors = [];\n");var h=u?"destructors":"null",v=["throwBindingError","invoker","fn","runDestructors","retType","classParam"],d=[vn,e,o,X,r[0],r[1]];for(a&&(p+="var thisWired = classParam.toWireType("+h+", this);\n"),c=0;c<i-2;++c)p+="var arg"+c+"Wired = argType"+c+".toWireType("+h+", arg"+c+"); // "+r[c+2].name+"\n",v.push("argType"+c),d.push(r[c+2]);if(a&&(l="thisWired"+(l.length>0?", ":"")+l),p+=(f?"var rv = ":"")+"invoker(fn"+(l.length>0?", ":"")+l+");\n",u)p+="runDestructors(destructors);\n";else for(c=a?1:2;c<r.length;++c){var y=1===c?"thisWired":"arg"+(c-2)+"Wired";null!==r[c].destructorFunction&&(p+=y+"_dtor("+y+"); // "+r[c].name+"\n",v.push(y+"_dtor"),d.push(r[c].destructorFunction))}return f&&(p+="var ret = retType.fromWireType(rv);\nreturn ret;\n"),p+="}\n",v.push(p),function(n,r){if(!(n instanceof Function))throw new TypeError("new_ called with constructor type "+typeof n+" which is not a function");var t=on(n.name||"unknownFunctionName",(function(){}));t.prototype=n.prototype;var e=new t,o=n.apply(e,r);return o instanceof Object?o:e}(Function,v).apply(null,d)}function An(n,t,e){r.hasOwnProperty(n)?((void 0===e||void 0!==r[n].overloadTable&&void 0!==r[n].overloadTable[e])&&vn("Cannot register public name '"+n+"' twice"),function(n,r,t){if(void 0===n[r].overloadTable){var e=n[r];n[r]=function(){return n[r].overloadTable.hasOwnProperty(arguments.length)||vn("Function '"+t+"' called with an invalid number of arguments ("+arguments.length+") - expects one of ("+n[r].overloadTable+")!"),n[r].overloadTable[arguments.length].apply(this,arguments)},n[r].overloadTable=[],n[r].overloadTable[e.argCount]=e}}(r,n,n),r.hasOwnProperty(e)&&vn("Cannot register multiple overloads of a function with the same number of arguments ("+e+")!"),r[n].overloadTable[e]=t):(r[n]=t,void 0!==e&&(r[n].numArguments=e))}function Cn(n,t,e){return n.includes("j")?function(n,t,e){var o=r["dynCall_"+n];return e&&e.length?o.apply(null,[t].concat(e)):o.call(null,t)}(n,t,e):C.get(t).apply(null,e)}function Pn(n,r){var t,e,o,i=(n=pn(n)).includes("j")?(t=n,e=r,o=[],function(){o.length=arguments.length;for(var n=0;n<arguments.length;n++)o[n]=arguments[n];return Cn(t,e,o)}):C.get(r);return"function"!=typeof i&&vn("unknown function pointer with signature "+n+": "+r),i}var Rn=void 0;function On(n){var r=Bn(n),t=pn(r);return Nn(r),t}function Sn(n,r,t){switch(r){case 0:return t?function(n){return m[n]}:function(n){return w[n]};case 1:return t?function(n){return T[n>>1]}:function(n){return _[n>>1]};case 2:return t?function(n){return b[n>>2]}:function(n){return E[n>>2]};default:throw new TypeError("Unknown integer type: "+n)}}var Wn={};function kn(){return"object"==typeof globalThis?globalThis:Function("return this")()}function Un(n,r){var t=Q[n];return void 0===t&&vn(r+" has unknown type "+On(n)),t}var In={};function xn(n){try{return l.grow(n-g.byteLength+65535>>>16),I(l.buffer),1}catch(n){}}var Dn={mappings:{},buffers:[null,[],[]],printChar:function(n,r){var t=Dn.buffers[n];0===r||10===r?((1===n?p:h)(function(n,r,t){for(var e=r+t,o=r;n[o]&&!(o>=e);)++o;return d.decode(n.subarray?n.subarray(r,o):new Uint8Array(n.slice(r,o)))}(t,0)),t.length=0):t.push(r)},varargs:void 0,get:function(){return Dn.varargs+=4,b[Dn.varargs-4>>2]},getStr:function(n){return y(n)},get64:function(n,r){return n}};un=r.InternalError=an(Error,"InternalError"),function(){for(var n=new Array(256),r=0;r<256;++r)n[r]=String.fromCharCode(r);ln=n}(),hn=r.BindingError=an(Error,"BindingError"),r.count_emval_handles=wn,r.get_first_emval=Tn,Rn=r.UnboundTypeError=an(Error,"UnboundTypeError");var Hn={o:function(n,r,t,e){M("Assertion failed: "+y(n)+", at: "+[r?y(r):"unknown filename",t,e?y(e):"unknown function"])},z:function(n){return Mn(n+L.SIZE)+L.SIZE},B:function(n,r){},y:function(n,r,t){throw new Y(n).init(r,t),n},l:function(n){var r=J[n];delete J[n];var t=r.rawConstructor,e=r.rawDestructor,o=r.fields;fn([n],o.map((function(n){return n.getterReturnType})).concat(o.map((function(n){return n.setterArgumentType}))),(function(n){var i={};return o.forEach((function(r,t){var e=r.fieldName,a=n[t],u=r.getter,c=r.getterContext,f=n[t+o.length],s=r.setter,l=r.setterContext;i[e]={read:function(n){return a.fromWireType(u(c,n))},write:function(n,r){var t=[];s(l,n,f.toWireType(t,r)),X(t)}}})),[{name:r.name,fromWireType:function(n){var r={};for(var t in i)r[t]=i[t].read(n);return e(n),r},toWireType:function(n,r){for(var o in i)if(!(o in r))throw new TypeError('Missing field:  "'+o+'"');var a=t();for(o in i)i[o].write(a,r[o]);return null!==n&&n.push(e,a),a},argPackAdvance:8,readValueFromPointer:$,destructorFunction:e}]}))},q:function(n,r,t,e,o){},w:function(n,r,t,e,o){var i=sn(t);dn(n,{name:r=pn(r),fromWireType:function(n){return!!n},toWireType:function(n,r){return r?e:o},argPackAdvance:8,readValueFromPointer:function(n){var e;if(1===t)e=m;else if(2===t)e=T;else{if(4!==t)throw new TypeError("Unknown boolean type size: "+r);e=b}return this.fromWireType(e[n>>i])},destructorFunction:null})},v:function(n,r){dn(n,{name:r=pn(r),fromWireType:function(n){var r=gn[n].value;return mn(n),r},toWireType:function(n,r){return _n(r)},argPackAdvance:8,readValueFromPointer:$,destructorFunction:null})},h:function(n,r,t){var e=sn(t);dn(n,{name:r=pn(r),fromWireType:function(n){return n},toWireType:function(n,r){if("number"!=typeof r&&"boolean"!=typeof r)throw new TypeError('Cannot convert "'+bn(r)+'" to '+this.name);return r},argPackAdvance:8,readValueFromPointer:En(r,e),destructorFunction:null})},k:function(n,t,e,o,i,a){var u=function(n,r){for(var t=[],e=0;e<n;e++)t.push(b[(r>>2)+e]);return t}(t,e);n=pn(n),i=Pn(o,i),An(n,(function(){!function(n,r){var t=[],e={};throw r.forEach((function n(r){e[r]||Q[r]||(nn[r]?nn[r].forEach(n):(t.push(r),e[r]=!0))})),new Rn(n+": "+t.map(On).join([", "]))}("Cannot call "+n+" due to unbound types",u)}),t-1),fn([],u,(function(e){var o=[e[0],null].concat(e.slice(1));return function(n,t,e){r.hasOwnProperty(n)||cn("Replacing nonexistant public symbol"),void 0!==r[n].overloadTable&&void 0!==e?r[n].overloadTable[e]=t:(r[n]=t,r[n].argCount=e)}(n,Fn(n,o,null,i,a),t-1),[]}))},c:function(n,r,t,e,o){r=pn(r),-1===o&&(o=4294967295);var i=sn(t),a=function(n){return n};if(0===e){var u=32-8*t;a=function(n){return n<<u>>>u}}var c=r.includes("unsigned");dn(n,{name:r,fromWireType:a,toWireType:function(n,t){if("number"!=typeof t&&"boolean"!=typeof t)throw new TypeError('Cannot convert "'+bn(t)+'" to '+this.name);if(t<e||t>o)throw new TypeError('Passing a number "'+bn(t)+'" from JS side to C/C++ side to an argument of type "'+r+'", which is outside the valid range ['+e+", "+o+"]!");return c?t>>>0:0|t},argPackAdvance:8,readValueFromPointer:Sn(r,i,0!==e),destructorFunction:null})},a:function(n,r,t){var e=[Int8Array,Uint8Array,Int16Array,Uint16Array,Int32Array,Uint32Array,Float32Array,Float64Array][r];function o(n){var r=E,t=r[n>>=2],o=r[n+1];return new e(g,o,t)}dn(n,{name:t=pn(t),fromWireType:o,argPackAdvance:8,readValueFromPointer:o},{ignoreDuplicateRegistrations:!0})},i:function(n,r){var t="std::string"===(r=pn(r));dn(n,{name:r,fromWireType:function(n){var r,e=E[n>>2];if(t)for(var o=n+4,i=0;i<=e;++i){var a=n+4+i;if(i==e||0==w[a]){var u=y(o,a-o);void 0===r?r=u:(r+=String.fromCharCode(0),r+=u),o=a+1}}else{var c=new Array(e);for(i=0;i<e;++i)c[i]=String.fromCharCode(w[n+4+i]);r=c.join("")}return Nn(n),r},toWireType:function(n,r){var e;r instanceof ArrayBuffer&&(r=new Uint8Array(r));var o="string"==typeof r;o||r instanceof Uint8Array||r instanceof Uint8ClampedArray||r instanceof Int8Array||vn("Cannot pass non-string to std::string"),e=t&&o?function(){return function(n){for(var r=0,t=0;t<n.length;++t){var e=n.charCodeAt(t);e>=55296&&e<=57343&&(e=65536+((1023&e)<<10)|1023&n.charCodeAt(++t)),e<=127?++r:r+=e<=2047?2:e<=65535?3:4}return r}(r)}:function(){return r.length};var i=e(),a=Mn(4+i+1);if(E[a>>2]=i,t&&o)!function(n,r,t,e){if(!(e>0))return 0;for(var o=t+e-1,i=0;i<n.length;++i){var a=n.charCodeAt(i);if(a>=55296&&a<=57343&&(a=65536+((1023&a)<<10)|1023&n.charCodeAt(++i)),a<=127){if(t>=o)break;r[t++]=a}else if(a<=2047){if(t+1>=o)break;r[t++]=192|a>>6,r[t++]=128|63&a}else if(a<=65535){if(t+2>=o)break;r[t++]=224|a>>12,r[t++]=128|a>>6&63,r[t++]=128|63&a}else{if(t+3>=o)break;r[t++]=240|a>>18,r[t++]=128|a>>12&63,r[t++]=128|a>>6&63,r[t++]=128|63&a}}r[t]=0}(r,w,a+4,i+1);else if(o)for(var u=0;u<i;++u){var c=r.charCodeAt(u);c>255&&(Nn(a),vn("String has UTF-16 code units that do not fit in 8 bits")),w[a+4+u]=c}else for(u=0;u<i;++u)w[a+4+u]=r[u];return null!==n&&n.push(Nn,a),a},argPackAdvance:8,readValueFromPointer:$,destructorFunction:function(n){Nn(n)}})},e:function(n,r,t){var e,o,i,a,u;t=pn(t),2===r?(e=R,o=O,a=S,i=function(){return _},u=1):4===r&&(e=W,o=k,a=U,i=function(){return E},u=2),dn(n,{name:t,fromWireType:function(n){for(var t,o=E[n>>2],a=i(),c=n+4,f=0;f<=o;++f){var s=n+4+f*r;if(f==o||0==a[s>>u]){var l=e(c,s-c);void 0===t?t=l:(t+=String.fromCharCode(0),t+=l),c=s+r}}return Nn(n),t},toWireType:function(n,e){"string"!=typeof e&&vn("Cannot pass non-string to C++ string type "+t);var i=a(e),c=Mn(4+i+r);return E[c>>2]=i>>u,o(e,c+4,i+r),null!==n&&n.push(Nn,c),c},argPackAdvance:8,readValueFromPointer:$,destructorFunction:function(n){Nn(n)}})},m:function(n,r,t,e,o,i){J[n]={name:pn(r),rawConstructor:Pn(t,e),rawDestructor:Pn(o,i),fields:[]}},b:function(n,r,t,e,o,i,a,u,c,f){J[n].fields.push({fieldName:pn(r),getterReturnType:t,getter:Pn(e,o),getterContext:i,setterArgumentType:a,setter:Pn(u,c),setterContext:f})},x:function(n,r){dn(n,{isVoid:!0,name:r=pn(r),argPackAdvance:0,fromWireType:function(){},toWireType:function(n,r){}})},g:mn,A:function(n){return 0===n?_n(kn()):(n=void 0===(t=Wn[r=n])?pn(r):t,_n(kn()[n]));var r,t},j:function(n){n>4&&(gn[n].refcount+=1)},n:function(n,t,e,o){n=function(n){return n||vn("Cannot use deleted val. handle = "+n),gn[n].value}(n);var i=In[t];return i||(i=function(n){for(var t="",e=0;e<n;++e)t+=(0!==e?", ":"")+"arg"+e;var o="return function emval_allocator_"+n+"(constructor, argTypes, args) {\n";for(e=0;e<n;++e)o+="var argType"+e+" = requireRegisteredType(Module['HEAP32'][(argTypes >>> 2) + "+e+'], "parameter '+e+'");\nvar arg'+e+" = argType"+e+".readValueFromPointer(args);\nargs += argType"+e+"['argPackAdvance'];\n";return o+="var obj = new constructor("+t+");\nreturn __emval_register(obj);\n}\n",new Function("requireRegisteredType","Module","__emval_register",o)(Un,r,_n)}(t),In[t]=i),i(n,e,o)},f:function(){M()},s:function(n,r,t){w.copyWithin(n,r,r+t)},d:function(n){var r,t,e=w.length,o=2147483648;if((n>>>=0)>o)return!1;for(var i=1;i<=4;i*=2){var a=e*(1+.2/i);if(a=Math.min(a,n+100663296),xn(Math.min(o,((r=Math.max(n,a))%(t=65536)>0&&(r+=t-r%t),r))))return!0}return!1},u:function(n){return 0},p:function(n,r,t,e,o){},t:function(n,r,t,e){for(var o=0,i=0;i<t;i++){for(var a=b[r+8*i>>2],u=b[r+(8*i+4)>>2],c=0;c<u;c++)Dn.printChar(n,w[a+c]);o+=u}return b[e>>2]=o,0},r:function(n){}};!function(){var n={a:Hn};function t(n,t){var e,o=n.exports;r.asm=o,I((l=r.asm.C).buffer),C=r.asm.I,e=r.asm.D,D.unshift(e),function(){if(j--,r.monitorRunDependencies&&r.monitorRunDependencies(j),0==j&&N){var n=N;N=null,n()}}()}function e(n){t(n.instance)}function i(r){return(!s&&c&&"function"==typeof fetch?fetch(z,{credentials:"same-origin"}).then((function(n){if(!n.ok)throw"failed to load wasm binary file at '"+z+"'";return n.arrayBuffer()})).catch((function(){return G(z)})):Promise.resolve().then((function(){return G(z)}))).then((function(r){return WebAssembly.instantiate(r,n)})).then(r,(function(n){h("failed to asynchronously prepare wasm: "+n),M(n)}))}if(j++,r.monitorRunDependencies&&r.monitorRunDependencies(j),r.instantiateWasm)try{return r.instantiateWasm(n,t)}catch(n){return h("Module.instantiateWasm callback failed with error: "+n),!1}(s||"function"!=typeof WebAssembly.instantiateStreaming||q(z)||"function"!=typeof fetch?i(e):fetch(z,{credentials:"same-origin"}).then((function(r){return WebAssembly.instantiateStreaming(r,n).then(e,(function(n){return h("wasm streaming compile failed: "+n),h("falling back to ArrayBuffer instantiation"),i(e)}))}))).catch(o)}(),r.___wasm_call_ctors=function(){return(r.___wasm_call_ctors=r.asm.D).apply(null,arguments)};var jn,Nn=r._free=function(){return(Nn=r._free=r.asm.E).apply(null,arguments)},Mn=r._malloc=function(){return(Mn=r._malloc=r.asm.F).apply(null,arguments)},Bn=r.___getTypeName=function(){return(Bn=r.___getTypeName=r.asm.G).apply(null,arguments)};function Vn(n){function e(){jn||(jn=!0,r.calledRun=!0,v||(Z(D),t(r),r.onRuntimeInitialized&&r.onRuntimeInitialized(),function(){if(r.postRun)for("function"==typeof r.postRun&&(r.postRun=[r.postRun]);r.postRun.length;)n=r.postRun.shift(),H.unshift(n);var n;Z(H)}()))}j>0||(function(){if(r.preRun)for("function"==typeof r.preRun&&(r.preRun=[r.preRun]);r.preRun.length;)n=r.preRun.shift(),x.unshift(n);var n;Z(x)}(),j>0||(r.setStatus?(r.setStatus("Running..."),setTimeout((function(){setTimeout((function(){r.setStatus("")}),1),e()}),1)):e()))}if(r.___embind_register_native_and_builtin_types=function(){return(r.___embind_register_native_and_builtin_types=r.asm.H).apply(null,arguments)},r.dynCall_jiji=function(){return(r.dynCall_jiji=r.asm.J).apply(null,arguments)},N=function n(){jn||Vn(),jn||(N=n)},r.run=Vn,r.preInit)for("function"==typeof r.preInit&&(r.preInit=[r.preInit]);r.preInit.length>0;)r.preInit.pop()();return Vn(),r.ready});t.default=o}));
