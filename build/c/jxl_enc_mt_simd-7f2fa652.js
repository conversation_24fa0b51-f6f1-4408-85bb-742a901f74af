define(["module","require","exports"],(function(e,r,t){var n,a=(n=e.uri,function(r){function t(){return b.buffer!=O&&Q(b.buffer),k}function a(){return b.buffer!=O&&Q(b.buffer),x}function o(){return b.buffer!=O&&Q(b.buffer),F}function i(){return b.buffer!=O&&Q(b.buffer),D}function u(){return b.buffer!=O&&Q(b.buffer),R}function s(){return b.buffer!=O&&Q(b.buffer),P}function c(){return b.buffer!=O&&Q(b.buffer),W}var f,l;(r=void 0!==(r=r||{})?r:{}).ready=new Promise((function(e,r){f=e,l=r}));var d,h={};for(d in r)r.hasOwnProperty(d)&&(h[d]=r[d]);var p,m="./this.program",v=function(e,r){throw r},g=!0,_=r.ENVIRONMENT_IS_PTHREAD||!1,E="";function y(e){return r.locateFile?r.locateFile(e,E):E+e}E=self.location.href,n&&(E=n),E=0!==E.indexOf("blob:")?E.substr(0,E.lastIndexOf("/")+1):"",p=function(e){var r=new XMLHttpRequest;return r.open("GET",e,!1),r.responseType="arraybuffer",r.send(null),new Uint8Array(r.response)};var w,T=r.print||console.log.bind(console),A=r.printErr||console.warn.bind(console);for(d in h)h.hasOwnProperty(d)&&(r[d]=h[d]);h=null,r.arguments&&r.arguments,r.thisProgram&&(m=r.thisProgram),r.quit&&(v=r.quit),r.wasmBinary&&(w=r.wasmBinary);var b,C,S=r.noExitRuntime||!0;"object"!=typeof WebAssembly&&ae("no native wasm support detected");var O,k,x,F,D,R,P,N,W,I=!1;function M(e,r){e||ae("Assertion failed: "+r)}function L(e,r,t){for(var n=r+t,a="";!(r>=n);){var o=e[r++];if(!o)return a;if(128&o){var i=63&e[r++];if(192!=(224&o)){var u=63&e[r++];if((o=224==(240&o)?(15&o)<<12|i<<6|u:(7&o)<<18|i<<12|u<<6|63&e[r++])<65536)a+=String.fromCharCode(o);else{var s=o-65536;a+=String.fromCharCode(55296|s>>10,56320|1023&s)}}else a+=String.fromCharCode((31&o)<<6|i)}else a+=String.fromCharCode(o)}return a}function H(e,r){return e?L(a(),e,r):""}function U(e,r,t,n){if(!(n>0))return 0;for(var a=t,o=t+n-1,i=0;i<e.length;++i){var u=e.charCodeAt(i);if(u>=55296&&u<=57343&&(u=65536+((1023&u)<<10)|1023&e.charCodeAt(++i)),u<=127){if(t>=o)break;r[t++]=u}else if(u<=2047){if(t+1>=o)break;r[t++]=192|u>>6,r[t++]=128|63&u}else if(u<=65535){if(t+2>=o)break;r[t++]=224|u>>12,r[t++]=128|u>>6&63,r[t++]=128|63&u}else{if(t+3>=o)break;r[t++]=240|u>>18,r[t++]=128|u>>12&63,r[t++]=128|u>>6&63,r[t++]=128|63&u}}return r[t]=0,t-a}function B(e,r,t){return U(e,a(),r,t)}function j(e){for(var r=0,t=0;t<e.length;++t){var n=e.charCodeAt(t);n>=55296&&n<=57343&&(n=65536+((1023&n)<<10)|1023&e.charCodeAt(++t)),n<=127?++r:r+=n<=2047?2:n<=65535?3:4}return r}function G(e,r){for(var t="",n=0;!(n>=r/2);++n){var a=o()[e+2*n>>1];if(0==a)break;t+=String.fromCharCode(a)}return t}function Y(e,r,t){if(void 0===t&&(t=2147483647),t<2)return 0;for(var n=r,a=(t-=2)<2*e.length?t/2:e.length,i=0;i<a;++i){var u=e.charCodeAt(i);o()[r>>1]=u,r+=2}return o()[r>>1]=0,r-n}function V(e){return 2*e.length}function q(e,r){for(var t=0,n="";!(t>=r/4);){var a=u()[e+4*t>>2];if(0==a)break;if(++t,a>=65536){var o=a-65536;n+=String.fromCharCode(55296|o>>10,56320|1023&o)}else n+=String.fromCharCode(a)}return n}function z(e,r,t){if(void 0===t&&(t=2147483647),t<4)return 0;for(var n=r,a=n+t-4,o=0;o<e.length;++o){var i=e.charCodeAt(o);if(i>=55296&&i<=57343&&(i=65536+((1023&i)<<10)|1023&e.charCodeAt(++o)),u()[r>>2]=i,(r+=4)+4>a)break}return u()[r>>2]=0,r-n}function K(e){for(var r=0,t=0;t<e.length;++t){var n=e.charCodeAt(t);n>=55296&&n<=57343&&++t,r+=4}return r}function Q(e){O=e,r.HEAP8=k=new Int8Array(e),r.HEAP16=F=new Int16Array(e),r.HEAP32=R=new Int32Array(e),r.HEAPU8=x=new Uint8Array(e),r.HEAPU16=D=new Uint16Array(e),r.HEAPU32=P=new Uint32Array(e),r.HEAPF32=N=new Float32Array(e),r.HEAPF64=W=new Float64Array(e)}_&&(O=r.buffer);var X,Z=r.INITIAL_MEMORY||16777216;if(_)b=r.wasmMemory,O=r.buffer;else if(r.wasmMemory)b=r.wasmMemory;else if(!((b=new WebAssembly.Memory({initial:Z/65536,maximum:32768,shared:!0})).buffer instanceof SharedArrayBuffer))throw A("requested a shared WebAssembly.Memory but the returned buffer is not a SharedArrayBuffer, indicating that while the browser has SharedArrayBuffer it does not have WebAssembly threads support - you may need to set a flag"),Error("bad memory");b&&(O=b.buffer),Z=O.byteLength,Q(O);var J=[],$=[],ee=[];function re(){_||fe($)}var te=0,ne=null;function ae(e){r.onAbort&&r.onAbort(e),_&&console.error("Pthread aborting at "+(new Error).stack),A(e+=""),I=!0,e="abort("+e+"). Build with -s ASSERTIONS=1 for more info.";var t=new WebAssembly.RuntimeError(e);throw l(t),t}r.preloadedImages={},r.preloadedAudios={};var oe="data:application/octet-stream;base64,";function ie(e){return e.startsWith(oe)}if(r.locateFile)ie(ue="jxl_enc_mt_simd.wasm")||(ue=y(ue));else var ue=new URL("/c/jxl_enc_mt_simd-efe18ebf.wasm",e.uri).toString();function se(e){try{if(e==ue&&w)return new Uint8Array(w);if(p)return p(e);throw"both async and sync fetching of the wasm failed"}catch(e){ae(e)}}var ce={64256:function(){throw"Canceled!"},64274:function(e,r){setTimeout((function(){zr(e,r)}),0)}};function fe(e){for(;e.length>0;){var t=e.shift();if("function"!=typeof t){var n=t.func;"number"==typeof n?void 0===t.arg?X.get(n)():X.get(n)(t.arg):n(void 0===t.arg?null:t.arg)}else t(r)}}var le={EPERM:63,ENOENT:44,ESRCH:71,EINTR:27,EIO:29,ENXIO:60,E2BIG:1,ENOEXEC:45,EBADF:8,ECHILD:12,EAGAIN:6,EWOULDBLOCK:6,ENOMEM:48,EACCES:2,EFAULT:21,ENOTBLK:105,EBUSY:10,EEXIST:20,EXDEV:75,ENODEV:43,ENOTDIR:54,EISDIR:31,EINVAL:28,ENFILE:41,EMFILE:33,ENOTTY:59,ETXTBSY:74,EFBIG:22,ENOSPC:51,ESPIPE:70,EROFS:69,EMLINK:34,EPIPE:64,EDOM:18,ERANGE:68,ENOMSG:49,EIDRM:24,ECHRNG:106,EL2NSYNC:156,EL3HLT:107,EL3RST:108,ELNRNG:109,EUNATCH:110,ENOCSI:111,EL2HLT:112,EDEADLK:16,ENOLCK:46,EBADE:113,EBADR:114,EXFULL:115,ENOANO:104,EBADRQC:103,EBADSLT:102,EDEADLOCK:16,EBFONT:101,ENOSTR:100,ENODATA:116,ETIME:117,ENOSR:118,ENONET:119,ENOPKG:120,EREMOTE:121,ENOLINK:47,EADV:122,ESRMNT:123,ECOMM:124,EPROTO:65,EMULTIHOP:36,EDOTDOT:125,EBADMSG:9,ENOTUNIQ:126,EBADFD:127,EREMCHG:128,ELIBACC:129,ELIBBAD:130,ELIBSCN:131,ELIBMAX:132,ELIBEXEC:133,ENOSYS:52,ENOTEMPTY:55,ENAMETOOLONG:37,ELOOP:32,EOPNOTSUPP:138,EPFNOSUPPORT:139,ECONNRESET:15,ENOBUFS:42,EAFNOSUPPORT:5,EPROTOTYPE:67,ENOTSOCK:57,ENOPROTOOPT:50,ESHUTDOWN:140,ECONNREFUSED:14,EADDRINUSE:3,ECONNABORTED:13,ENETUNREACH:40,ENETDOWN:38,ETIMEDOUT:73,EHOSTDOWN:142,EHOSTUNREACH:23,EINPROGRESS:26,EALREADY:7,EDESTADDRREQ:17,EMSGSIZE:35,EPROTONOSUPPORT:66,ESOCKTNOSUPPORT:137,EADDRNOTAVAIL:4,ENETRESET:39,EISCONN:30,ENOTCONN:53,ETOOMANYREFS:141,EUSERS:136,EDQUOT:19,ESTALE:72,ENOTSUP:138,ENOMEDIUM:148,EILSEQ:25,EOVERFLOW:61,ECANCELED:11,ENOTRECOVERABLE:56,EOWNERDEAD:62,ESTRPIPE:135};function de(e,r){if(e<=0||e>t().length||!0&e||r<0)return-28;if(0==r)return 0;r>=2147483647&&(r=1/0);var n=Atomics.load(u(),ft>>2),a=0;if(n==e&&Atomics.compareExchange(u(),ft>>2,n,0)==n&&(a=1,--r<=0))return 1;var o=Atomics.notify(u(),e>>2,r);if(o>=0)return o+a;throw"Atomics.notify returned an unexpected value "+o}function he(e){if(_)throw"Internal Error! cleanupThread() can only ever be called from main application thread!";if(!e)throw"Internal Error! Null pthread_ptr in cleanupThread!";var r=pe.pthreads[e];if(r){u()[e+12>>2]=0;var t=r.worker;pe.returnWorkerToPool(t)}}r._emscripten_futex_wake=de;var pe={unusedWorkers:[],runningWorkers:[],tlsInitFunctions:[],initMainThreadBlock:function(){for(var e=navigator.hardwareConcurrency,r=0;r<e;++r)pe.allocateUnusedWorker()},initRuntime:function(){for(var e=jr(228),r=0;r<57;++r)s()[e/4+r]=0;u()[e+12>>2]=e;var t=e+152;u()[t>>2]=t;var n=jr(512);for(r=0;r<128;++r)s()[n/4+r]=0;Atomics.store(s(),e+100>>2,n),Atomics.store(s(),e+40>>2,e),Jr(e,!g,1),Vr(e)},initWorker:function(){},pthreads:{},threadExitHandlers:[],runExitHandlers:function(){for(;pe.threadExitHandlers.length>0;)pe.threadExitHandlers.pop()();_&&rt()&&tt()},runExitHandlersAndDeinitThread:function(e,r){Atomics.store(s(),e+56>>2,1),Atomics.store(s(),e+60>>2,0),pe.runExitHandlers(),Atomics.store(s(),e+4>>2,r),Atomics.store(s(),e+0>>2,1),de(e+0,2147483647),Jr(0,0,0)},setExitStatus:function(e){},threadExit:function(e){var r=rt();r&&(pe.runExitHandlersAndDeinitThread(r,e),_&&postMessage({cmd:"exit"}))},threadCancel:function(){pe.runExitHandlersAndDeinitThread(rt(),-1),postMessage({cmd:"cancelDone"})},terminateAllThreads:function(){for(var e in pe.pthreads)(n=pe.pthreads[e])&&n.worker&&pe.returnWorkerToPool(n.worker);pe.pthreads={};for(var r=0;r<pe.unusedWorkers.length;++r)(t=pe.unusedWorkers[r]).terminate();for(pe.unusedWorkers=[],r=0;r<pe.runningWorkers.length;++r){var t,n=(t=pe.runningWorkers[r]).pthread;pe.freeThreadData(n),t.terminate()}pe.runningWorkers=[]},freeThreadData:function(e){if(e){if(e.threadInfoStruct){var r=u()[e.threadInfoStruct+100>>2];u()[e.threadInfoStruct+100>>2]=0,Gr(r),Gr(e.threadInfoStruct)}e.threadInfoStruct=0,e.allocatedOwnStack&&e.stackBase&&Gr(e.stackBase),e.stackBase=0,e.worker&&(e.worker.pthread=null)}},returnWorkerToPool:function(e){pe.runWithoutMainThreadQueuedCalls((function(){delete pe.pthreads[e.pthread.threadInfoStruct],pe.unusedWorkers.push(e),pe.runningWorkers.splice(pe.runningWorkers.indexOf(e),1),pe.freeThreadData(e.pthread),e.pthread=void 0}))},runWithoutMainThreadQueuedCalls:function(e){u()[ct>>2]=0;try{e()}finally{u()[ct>>2]=1}},receiveObjectTransfer:function(e){},threadInit:function(){for(var e in pe.tlsInitFunctions)pe.tlsInitFunctions[e]()},loadWasmModuleToWorker:function(e,t){e.onmessage=function(n){var a=n.data,o=a.cmd;if(e.pthread&&(pe.currentProxiedOperationCallerThread=e.pthread.threadInfoStruct),a.targetThread&&a.targetThread!=rt()){var i=pe.pthreads[a.targetThread];return i?i.worker.postMessage(n.data,a.transferList):console.error('Internal error! Worker sent a message "'+o+'" to target pthread '+a.targetThread+", but that thread no longer exists!"),void(pe.currentProxiedOperationCallerThread=void 0)}if("processQueuedMainThreadWork"===o)Qr();else if("spawnThread"===o)Dr(n.data);else if("cleanupThread"===o)he(a.thread);else if("killThread"===o)!function(e){if(_)throw"Internal Error! killThread() can only ever be called from main application thread!";if(!e)throw"Internal Error! Null pthread_ptr in killThread!";u()[e+12>>2]=0;var r=pe.pthreads[e];r.worker.terminate(),pe.freeThreadData(r),pe.runningWorkers.splice(pe.runningWorkers.indexOf(r.worker),1),r.worker.pthread=void 0}(a.thread);else if("cancelThread"===o)!function(e){if(_)throw"Internal Error! cancelThread() can only ever be called from main application thread!";if(!e)throw"Internal Error! Null pthread_ptr in cancelThread!";pe.pthreads[e].worker.postMessage({cmd:"cancel"})}(a.thread);else if("loaded"===o)e.loaded=!0,t&&t(e),e.runPthread&&(e.runPthread(),delete e.runPthread);else if("print"===o)T("Thread "+a.threadId+": "+a.text);else if("printErr"===o)A("Thread "+a.threadId+": "+a.text);else if("alert"===o)alert("Thread "+a.threadId+": "+a.text);else if("exit"===o)e.pthread&&Atomics.load(s(),e.pthread.threadInfoStruct+64>>2)&&pe.returnWorkerToPool(e);else if("exitProcess"===o)try{!function(e,t){if(!t||!ge()||0!==e){if(!t&&_)throw postMessage({cmd:"exitProcess",returnCode:e}),new lt(e);ge()||(pe.terminateAllThreads(),r.onExit&&r.onExit(e),I=!0),v(e,new lt(e))}}(a.returnCode)}catch(n){if(n instanceof lt)return;throw n}else"cancelDone"===o?pe.returnWorkerToPool(e):"objectTransfer"===o?pe.receiveObjectTransfer(n.data):"setimmediate"===n.data.target?e.postMessage(n.data):A("worker sent an unknown command "+o);pe.currentProxiedOperationCallerThread=void 0},e.onerror=function(e){A("pthread sent an error! "+e.filename+":"+e.lineno+": "+e.message)},e.postMessage({cmd:"load",urlOrBlob:r.mainScriptUrlOrBlob,wasmMemory:b,wasmModule:C})},allocateUnusedWorker:function(){if(r.locateFile){var t=y("jxl_enc_mt_simd.worker.js");pe.unusedWorkers.push(new Worker(t))}else pe.unusedWorkers.push(new Worker(new URL("/c/jxl_enc_mt_simd.worker-b10289f0.js",e.uri)))},getNewWorker:function(){return 0==pe.unusedWorkers.length&&(pe.allocateUnusedWorker(),pe.loadWasmModuleToWorker(pe.unusedWorkers[0])),pe.unusedWorkers.pop()},busySpinWait:function(e){for(var r=performance.now()+e;performance.now()<r;);}};r.establishStackSpace=function(e,r){it(e,r),at(e)},r.invokeEntryPoint=function(e,r){return X.get(e)(r)};var me,ve=0;function ge(){return S||ve>0}r.keepRuntimeAlive=ge,me=_?function(){return performance.now()-r.__performance_now_clock_drift}:function(){return performance.now()};var _e={DESTRUCTOR_OFFSET:0,REFCOUNT_OFFSET:4,TYPE_OFFSET:8,CAUGHT_OFFSET:12,RETHROWN_OFFSET:13,SIZE:16};function Ee(e){this.excPtr=e,this.ptr=e-_e.SIZE,this.set_type=function(e){u()[this.ptr+_e.TYPE_OFFSET>>2]=e},this.get_type=function(){return u()[this.ptr+_e.TYPE_OFFSET>>2]},this.set_destructor=function(e){u()[this.ptr+_e.DESTRUCTOR_OFFSET>>2]=e},this.get_destructor=function(){return u()[this.ptr+_e.DESTRUCTOR_OFFSET>>2]},this.set_refcount=function(e){u()[this.ptr+_e.REFCOUNT_OFFSET>>2]=e},this.set_caught=function(e){e=e?1:0,t()[this.ptr+_e.CAUGHT_OFFSET|0]=e},this.get_caught=function(){return 0!=t()[this.ptr+_e.CAUGHT_OFFSET|0]},this.set_rethrown=function(e){e=e?1:0,t()[this.ptr+_e.RETHROWN_OFFSET|0]=e},this.get_rethrown=function(){return 0!=t()[this.ptr+_e.RETHROWN_OFFSET|0]},this.init=function(e,r){this.set_type(e),this.set_destructor(r),this.set_refcount(0),this.set_caught(!1),this.set_rethrown(!1)},this.add_ref=function(){Atomics.add(u(),this.ptr+_e.REFCOUNT_OFFSET>>2,1)},this.release_ref=function(){return 1===Atomics.sub(u(),this.ptr+_e.REFCOUNT_OFFSET>>2,1)}}var ye={mappings:{},buffers:[null,[],[]],printChar:function(e,r){var t=ye.buffers[e];0===r||10===r?((1===e?T:A)(L(t,0)),t.length=0):t.push(r)},varargs:void 0,get:function(){return ye.varargs+=4,u()[ye.varargs-4>>2]},getStr:function(e){return H(e)},get64:function(e,r){return e}};function we(e,r,t){return _?lr(2,1,e,r,t):(ye.varargs=t,0)}function Te(e,r,t){return _?lr(3,1,e,r,t):(ye.varargs=t,0)}function Ae(e,r,t){if(_)return lr(4,1,e,r,t);ye.varargs=t}var be={};function Ce(e){for(;e.length;){var r=e.pop();e.pop()(r)}}function Se(e){return this.fromWireType(s()[e>>2])}var Oe={},ke={},xe={},Fe=48,De=57;function Re(e){if(void 0===e)return"_unknown";var r=(e=e.replace(/[^a-zA-Z0-9_]/g,"$")).charCodeAt(0);return r>=Fe&&r<=De?"_"+e:e}function Pe(e,r){return e=Re(e),new Function("body","return function "+e+'() {\n    "use strict";    return body.apply(this, arguments);\n};\n')(r)}function Ne(e,r){var t=Pe(r,(function(e){this.name=r,this.message=e;var t=new Error(e).stack;void 0!==t&&(this.stack=this.toString()+"\n"+t.replace(/^Error(:[^\n]*)?\n/,""))}));return t.prototype=Object.create(e.prototype),t.prototype.constructor=t,t.prototype.toString=function(){return void 0===this.message?this.name:this.name+": "+this.message},t}var We=void 0;function Ie(e){throw new We(e)}function Me(e,r,t){function n(r){var n=t(r);n.length!==e.length&&Ie("Mismatched type converter count");for(var a=0;a<e.length;++a)Ge(e[a],n[a])}e.forEach((function(e){xe[e]=r}));var a=new Array(r.length),o=[],i=0;r.forEach((function(e,r){ke.hasOwnProperty(e)?a[r]=ke[e]:(o.push(e),Oe.hasOwnProperty(e)||(Oe[e]=[]),Oe[e].push((function(){a[r]=ke[e],++i===o.length&&n(a)})))})),0===o.length&&n(a)}function Le(e){switch(e){case 1:return 0;case 2:return 1;case 4:return 2;case 8:return 3;default:throw new TypeError("Unknown type size: "+e)}}var He=void 0;function Ue(e){for(var r="",t=e;a()[t];)r+=He[a()[t++]];return r}var Be=void 0;function je(e){throw new Be(e)}function Ge(e,r,t){if(t=t||{},!("argPackAdvance"in r))throw new TypeError("registerType registeredInstance requires argPackAdvance");var n=r.name;if(e||je('type "'+n+'" must have a positive integer typeid pointer'),ke.hasOwnProperty(e)){if(t.ignoreDuplicateRegistrations)return;je("Cannot register type '"+n+"' twice")}if(ke[e]=r,delete xe[e],Oe.hasOwnProperty(e)){var a=Oe[e];delete Oe[e],a.forEach((function(e){e()}))}}var Ye=[],Ve=[{},{value:void 0},{value:null},{value:!0},{value:!1}];function qe(e){e>4&&0==--Ve[e].refcount&&(Ve[e]=void 0,Ye.push(e))}function ze(){for(var e=0,r=5;r<Ve.length;++r)void 0!==Ve[r]&&++e;return e}function Ke(){for(var e=5;e<Ve.length;++e)if(void 0!==Ve[e])return Ve[e];return null}function Qe(e){switch(e){case void 0:return 1;case null:return 2;case!0:return 3;case!1:return 4;default:var r=Ye.length?Ye.pop():Ve.length;return Ve[r]={refcount:1,value:e},r}}function Xe(e){if(null===e)return"null";var r=typeof e;return"object"===r||"array"===r||"function"===r?e.toString():""+e}function Ze(e,r){switch(r){case 2:return function(e){return this.fromWireType((b.buffer!=O&&Q(b.buffer),N)[e>>2])};case 3:return function(e){return this.fromWireType(c()[e>>3])};default:throw new TypeError("Unknown float type: "+e)}}function Je(e,r,t,n,a){var o=r.length;o<2&&je("argTypes array size mismatch! Must at least get return value and 'this' types!");for(var i=null!==r[1]&&null!==t,u=!1,s=1;s<r.length;++s)if(null!==r[s]&&void 0===r[s].destructorFunction){u=!0;break}var c="void"!==r[0].name,f="",l="";for(s=0;s<o-2;++s)f+=(0!==s?", ":"")+"arg"+s,l+=(0!==s?", ":"")+"arg"+s+"Wired";var d="return function "+Re(e)+"("+f+") {\nif (arguments.length !== "+(o-2)+") {\nthrowBindingError('function "+e+" called with ' + arguments.length + ' arguments, expected "+(o-2)+" args!');\n}\n";u&&(d+="var destructors = [];\n");var h=u?"destructors":"null",p=["throwBindingError","invoker","fn","runDestructors","retType","classParam"],m=[je,n,a,Ce,r[0],r[1]];for(i&&(d+="var thisWired = classParam.toWireType("+h+", this);\n"),s=0;s<o-2;++s)d+="var arg"+s+"Wired = argType"+s+".toWireType("+h+", arg"+s+"); // "+r[s+2].name+"\n",p.push("argType"+s),m.push(r[s+2]);if(i&&(l="thisWired"+(l.length>0?", ":"")+l),d+=(c?"var rv = ":"")+"invoker(fn"+(l.length>0?", ":"")+l+");\n",u)d+="runDestructors(destructors);\n";else for(s=i?1:2;s<r.length;++s){var v=1===s?"thisWired":"arg"+(s-2)+"Wired";null!==r[s].destructorFunction&&(d+=v+"_dtor("+v+"); // "+r[s].name+"\n",p.push(v+"_dtor"),m.push(r[s].destructorFunction))}return c&&(d+="var ret = retType.fromWireType(rv);\nreturn ret;\n"),d+="}\n",p.push(d),function(e,r){if(!(e instanceof Function))throw new TypeError("new_ called with constructor type "+typeof e+" which is not a function");var t=Pe(e.name||"unknownFunctionName",(function(){}));t.prototype=e.prototype;var n=new t,a=e.apply(n,r);return a instanceof Object?a:n}(Function,p).apply(null,m)}function $e(e,t,n){r.hasOwnProperty(e)?((void 0===n||void 0!==r[e].overloadTable&&void 0!==r[e].overloadTable[n])&&je("Cannot register public name '"+e+"' twice"),function(e,r,t){if(void 0===e[r].overloadTable){var n=e[r];e[r]=function(){return e[r].overloadTable.hasOwnProperty(arguments.length)||je("Function '"+t+"' called with an invalid number of arguments ("+arguments.length+") - expects one of ("+e[r].overloadTable+")!"),e[r].overloadTable[arguments.length].apply(this,arguments)},e[r].overloadTable=[],e[r].overloadTable[n.argCount]=n}}(r,e,e),r.hasOwnProperty(n)&&je("Cannot register multiple overloads of a function with the same number of arguments ("+n+")!"),r[e].overloadTable[n]=t):(r[e]=t,void 0!==n&&(r[e].numArguments=n))}function er(e,t,n){return e.includes("j")?function(e,t,n){var a=r["dynCall_"+e];return n&&n.length?a.apply(null,[t].concat(n)):a.call(null,t)}(e,t,n):X.get(t).apply(null,n)}function rr(e,r){var t,n,a,o=(e=Ue(e)).includes("j")?(t=e,n=r,a=[],function(){a.length=arguments.length;for(var e=0;e<arguments.length;e++)a[e]=arguments[e];return er(t,n,a)}):X.get(r);return"function"!=typeof o&&je("unknown function pointer with signature "+e+": "+r),o}var tr=void 0;function nr(e){var r=Yr(e),t=Ue(r);return Gr(r),t}function ar(e,r,n){switch(r){case 0:return n?function(e){return t()[e]}:function(e){return a()[e]};case 1:return n?function(e){return o()[e>>1]}:function(e){return i()[e>>1]};case 2:return n?function(e){return u()[e>>2]}:function(e){return s()[e>>2]};default:throw new TypeError("Unknown integer type: "+e)}}var or={};function ir(){return"object"==typeof globalThis?globalThis:Function("return this")()}function ur(e,r){var t=ke[e];return void 0===t&&je(r+" has unknown type "+nr(e)),t}var sr={},cr=[];function fr(e,r,n){if(e<=0||e>t().length||!0&e)return-28;var a=Atomics.wait(u(),e>>2,r,n);if("timed-out"===a)return-73;if("not-equal"===a)return-6;if("ok"===a)return 0;throw"Atomics.wait returned an unexpected value "+a}function lr(e,r){for(var t=arguments.length-2,n=nt(),a=t,o=ot(8*a),i=o>>3,u=0;u<t;u++){var s=arguments[2+u];c()[i+u]=s}var f=Xr(e,a,o,r);return at(n),f}var dr=[];function hr(e){try{return b.grow(e-O.byteLength+65535>>>16),Q(b.buffer),1}catch(e){}}var pr={inEventHandler:0,removeAllEventListeners:function(){for(var e=pr.eventHandlers.length-1;e>=0;--e)pr._removeHandler(e);pr.eventHandlers=[],pr.deferredCalls=[]},registerRemoveEventListeners:function(){pr.removeEventListenersRegistered||(pr.removeEventListenersRegistered=!0)},deferredCalls:[],deferCall:function(e,r,t){function n(e,r){if(e.length!=r.length)return!1;for(var t in e)if(e[t]!=r[t])return!1;return!0}for(var a in pr.deferredCalls){var o=pr.deferredCalls[a];if(o.targetFunction==e&&n(o.argsList,t))return}pr.deferredCalls.push({targetFunction:e,precedence:r,argsList:t}),pr.deferredCalls.sort((function(e,r){return e.precedence<r.precedence}))},removeDeferredCalls:function(e){for(var r=0;r<pr.deferredCalls.length;++r)pr.deferredCalls[r].targetFunction==e&&(pr.deferredCalls.splice(r,1),--r)},canPerformEventHandlerRequests:function(){return pr.inEventHandler&&pr.currentEventHandler.allowsDeferredCalls},runDeferredCalls:function(){if(pr.canPerformEventHandlerRequests())for(var e=0;e<pr.deferredCalls.length;++e){var r=pr.deferredCalls[e];pr.deferredCalls.splice(e,1),--e,r.targetFunction.apply(null,r.argsList)}},eventHandlers:[],removeAllHandlersOnTarget:function(e,r){for(var t=0;t<pr.eventHandlers.length;++t)pr.eventHandlers[t].target!=e||r&&r!=pr.eventHandlers[t].eventTypeString||pr._removeHandler(t--)},_removeHandler:function(e){var r=pr.eventHandlers[e];r.target.removeEventListener(r.eventTypeString,r.eventListenerFunc,r.useCapture),pr.eventHandlers.splice(e,1)},registerOrRemoveHandler:function(e){var r=function(r){++pr.inEventHandler,pr.currentEventHandler=e,pr.runDeferredCalls(),e.handlerFunc(r),pr.runDeferredCalls(),--pr.inEventHandler};if(e.callbackfunc)e.eventListenerFunc=r,e.target.addEventListener(e.eventTypeString,r,e.useCapture),pr.eventHandlers.push(e),pr.registerRemoveEventListeners();else for(var t=0;t<pr.eventHandlers.length;++t)pr.eventHandlers[t].target==e.target&&pr.eventHandlers[t].eventTypeString==e.eventTypeString&&pr._removeHandler(t--)},queueEventHandlerOnThread_iiii:function(e,r,t,n,a){var o=nt(),i=ot(12);u()[i>>2]=t,u()[i+4>>2]=n,u()[i+8>>2]=a,Zr(0,e,637534208,r,n,i),at(o)},getTargetThreadForEventCallback:function(e){switch(e){case 1:return 0;case 2:return pe.currentProxiedOperationCallerThread;default:return e}},getNodeNameForTarget:function(e){return e?e==window?"#window":e==screen?"#screen":e&&e.nodeName?e.nodeName:"":""},fullscreenEnabled:function(){return document.fullscreenEnabled||document.webkitFullscreenEnabled}};function mr(e,r,t,n){var a,o,i,s=nt(),c=ot(12),f=0;r&&(o=j(a=r)+1,i=jr(o),B(a,i,o),f=i),u()[c>>2]=f,u()[c+4>>2]=t,u()[c+8>>2]=n,Zr(0,e,657457152,0,f,c),at(s)}var vr=[0,"undefined"!=typeof document?document:0,"undefined"!=typeof window?window:0];function gr(e){var r;return e=(r=e)>2?H(r):r,vr[e]||("undefined"!=typeof document?document.querySelector(e):void 0)}function _r(e){return gr(e)}function Er(e,r,t){var n=_r(e);if(!n)return-4;if(n.canvasSharedPtr&&(u()[n.canvasSharedPtr>>2]=r,u()[n.canvasSharedPtr+4>>2]=t),!n.offscreenCanvas&&n.controlTransferredOffscreen)return n.canvasSharedPtr?(function(e,r,t,n){mr(e,r=r?H(r):"",t,n)}(u()[n.canvasSharedPtr+8>>2],e,r,t),1):-4;n.offscreenCanvas&&(n=n.offscreenCanvas);var a=!1;if(n.GLctxObject&&n.GLctxObject.GLctx){var o=n.GLctxObject.GLctx.getParameter(2978);a=0===o[0]&&0===o[1]&&o[2]===n.width&&o[3]===n.height}return n.width=r,n.height=t,a&&n.GLctxObject.GLctx.viewport(0,0,r,t),0}function yr(e,r,t){return _?lr(5,1,e,r,t):Er(e,r,t)}var wr={counter:1,buffers:[],programs:[],framebuffers:[],renderbuffers:[],textures:[],shaders:[],vaos:[],contexts:{},offscreenCanvases:{},queries:[],stringCache:{},unpackAlignment:4,recordError:function(e){wr.lastError||(wr.lastError=e)},getNewId:function(e){for(var r=wr.counter++,t=e.length;t<r;t++)e[t]=null;return r},getSource:function(e,r,t,n){for(var a="",o=0;o<r;++o){var i=n?u()[n+4*o>>2]:-1;a+=H(u()[t+4*o>>2],i<0?void 0:i)}return a},createContext:function(e,r){e.getContextSafariWebGL2Fixed||(e.getContextSafariWebGL2Fixed=e.getContext,e.getContext=function(r,t){var n=e.getContextSafariWebGL2Fixed(r,t);return"webgl"==r==n instanceof WebGLRenderingContext?n:null});var t=e.getContext("webgl",r);return t?wr.registerContext(t,r):0},registerContext:function(e,r){var t=jr(8);u()[t+4>>2]=rt();var n={handle:t,attributes:r,version:r.majorVersion,GLctx:e};return e.canvas&&(e.canvas.GLctxObject=n),wr.contexts[t]=n,(void 0===r.enableExtensionsByDefault||r.enableExtensionsByDefault)&&wr.initExtensions(n),t},makeContextCurrent:function(e){return wr.currentContext=wr.contexts[e],r.ctx=Wr=wr.currentContext&&wr.currentContext.GLctx,!(e&&!Wr)},getContext:function(e){return wr.contexts[e]},deleteContext:function(e){wr.currentContext===wr.contexts[e]&&(wr.currentContext=null),"object"==typeof pr&&pr.removeAllHandlersOnTarget(wr.contexts[e].GLctx.canvas),wr.contexts[e]&&wr.contexts[e].GLctx.canvas&&(wr.contexts[e].GLctx.canvas.GLctxObject=void 0),Gr(wr.contexts[e].handle),wr.contexts[e]=null},initExtensions:function(e){if(e||(e=wr.currentContext),!e.initExtensionsDone){e.initExtensionsDone=!0;var r,t=e.GLctx;!function(e){var r=e.getExtension("ANGLE_instanced_arrays");r&&(e.vertexAttribDivisor=function(e,t){r.vertexAttribDivisorANGLE(e,t)},e.drawArraysInstanced=function(e,t,n,a){r.drawArraysInstancedANGLE(e,t,n,a)},e.drawElementsInstanced=function(e,t,n,a,o){r.drawElementsInstancedANGLE(e,t,n,a,o)})}(t),function(e){var r=e.getExtension("OES_vertex_array_object");r&&(e.createVertexArray=function(){return r.createVertexArrayOES()},e.deleteVertexArray=function(e){r.deleteVertexArrayOES(e)},e.bindVertexArray=function(e){r.bindVertexArrayOES(e)},e.isVertexArray=function(e){return r.isVertexArrayOES(e)})}(t),function(e){var r=e.getExtension("WEBGL_draw_buffers");r&&(e.drawBuffers=function(e,t){r.drawBuffersWEBGL(e,t)})}(t),t.disjointTimerQueryExt=t.getExtension("EXT_disjoint_timer_query"),(r=t).multiDrawWebgl=r.getExtension("WEBGL_multi_draw"),(t.getSupportedExtensions()||[]).forEach((function(e){e.includes("lose_context")||e.includes("debug")||t.getExtension(e)}))}}},Tr=["default","low-power","high-performance"],Ar={};function br(){if(!br.strings){var e={USER:"web_user",LOGNAME:"web_user",PATH:"/",PWD:"/",HOME:"/home/<USER>",LANG:("object"==typeof navigator&&navigator.languages&&navigator.languages[0]||"C").replace("-","_")+".UTF-8",_:m||"./this.program"};for(var r in Ar)e[r]=Ar[r];var t=[];for(var r in e)t.push(r+"="+e[r]);br.strings=t}return br.strings}function Cr(e,r){if(_)return lr(6,1,e,r);var n=0;return br().forEach((function(a,o){var i=r+n;u()[e+4*o>>2]=i,function(e,r,n){for(var a=0;a<e.length;++a)t()[0|r++]=e.charCodeAt(a);n||(t()[0|r]=0)}(a,i),n+=a.length+1})),0}function Sr(e,r){if(_)return lr(7,1,e,r);var t=br();u()[e>>2]=t.length;var n=0;return t.forEach((function(e){n+=e.length+1})),u()[r>>2]=n,0}function Or(e){return _?lr(8,1,e):0}function kr(e,r,t,n){if(_)return lr(9,1,e,r,t,n);var a=ye.getStreamFromFD(e),o=ye.doReadv(a,r,t);return u()[n>>2]=o,0}function xr(e,r,t,n,a){if(_)return lr(10,1,e,r,t,n,a)}function Fr(e,r,t,n){if(_)return lr(11,1,e,r,t,n);for(var o=0,i=0;i<t;i++){for(var s=u()[r+8*i>>2],c=u()[r+(8*i+4)>>2],f=0;f<c;f++)ye.printChar(e,a()[s+f]);o+=c}return u()[n>>2]=o,0}function Dr(e){if(_)throw"Internal Error! spawnThread() can only ever be called from main application thread!";var r=pe.getNewWorker();if(!r)return 6;if(void 0!==r.pthread)throw"Internal error!";if(!e.pthread_ptr)throw"Internal error, no pthread ptr!";pe.runningWorkers.push(r);for(var t=jr(512),n=0;n<128;++n)u()[t+4*n>>2]=0;var a=e.stackBase+e.stackSize,o=pe.pthreads[e.pthread_ptr]={worker:r,stackBase:e.stackBase,stackSize:e.stackSize,allocatedOwnStack:e.allocatedOwnStack,threadInfoStruct:e.pthread_ptr},i=o.threadInfoStruct>>2;Atomics.store(s(),i+16,e.detached),Atomics.store(s(),i+25,t),Atomics.store(s(),i+10,o.threadInfoStruct),Atomics.store(s(),i+20,e.stackSize),Atomics.store(s(),i+19,a),Atomics.store(s(),i+26,e.stackSize),Atomics.store(s(),i+28,a),Atomics.store(s(),i+29,e.detached);var c=$r()+40;Atomics.store(s(),i+43,c),r.pthread=o;var f={cmd:"run",start_routine:e.startRoutine,arg:e.arg,threadInfoStruct:e.pthread_ptr,stackBase:e.stackBase,stackSize:e.stackSize};return r.runPthread=function(){f.time=performance.now(),r.postMessage(f,e.transferList)},r.loaded&&(r.runPthread(),delete r.runPthread),0}function Rr(){if(_){var e=rt();if(e&&!Atomics.load(s(),e+56>>2)&&2==Atomics.load(s(),e+0>>2))throw"Canceled!"}}function Pr(e){return e%4==0&&(e%100!=0||e%400==0)}function Nr(e,r){for(var t=0,n=0;n<=r;t+=e[n++]);return t}var Wr,Ir=[31,29,31,30,31,30,31,31,30,31,30,31],Mr=[31,28,31,30,31,30,31,31,30,31,30,31];function Lr(e,r){for(var t=new Date(e.getTime());r>0;){var n=Pr(t.getFullYear()),a=t.getMonth(),o=(n?Ir:Mr)[a];if(!(r>o-t.getDate()))return t.setDate(t.getDate()+r),t;r-=o-t.getDate()+1,t.setDate(1),a<11?t.setMonth(a+1):(t.setMonth(0),t.setFullYear(t.getFullYear()+1))}return t}function Hr(e,r,n,a){var o=u()[a+40>>2],i={tm_sec:u()[a>>2],tm_min:u()[a+4>>2],tm_hour:u()[a+8>>2],tm_mday:u()[a+12>>2],tm_mon:u()[a+16>>2],tm_year:u()[a+20>>2],tm_wday:u()[a+24>>2],tm_yday:u()[a+28>>2],tm_isdst:u()[a+32>>2],tm_gmtoff:u()[a+36>>2],tm_zone:o?H(o):""},s=H(n),c={"%c":"%a %b %d %H:%M:%S %Y","%D":"%m/%d/%y","%F":"%Y-%m-%d","%h":"%b","%r":"%I:%M:%S %p","%R":"%H:%M","%T":"%H:%M:%S","%x":"%m/%d/%y","%X":"%H:%M:%S","%Ec":"%c","%EC":"%C","%Ex":"%m/%d/%y","%EX":"%H:%M:%S","%Ey":"%y","%EY":"%Y","%Od":"%d","%Oe":"%e","%OH":"%H","%OI":"%I","%Om":"%m","%OM":"%M","%OS":"%S","%Ou":"%u","%OU":"%U","%OV":"%V","%Ow":"%w","%OW":"%W","%Oy":"%y"};for(var f in c)s=s.replace(new RegExp(f,"g"),c[f]);var l=["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],d=["January","February","March","April","May","June","July","August","September","October","November","December"];function h(e,r,t){for(var n="number"==typeof e?e.toString():e||"";n.length<r;)n=t[0]+n;return n}function p(e,r){return h(e,r,"0")}function m(e,r){function t(e){return e<0?-1:e>0?1:0}var n;return 0===(n=t(e.getFullYear()-r.getFullYear()))&&0===(n=t(e.getMonth()-r.getMonth()))&&(n=t(e.getDate()-r.getDate())),n}function v(e){switch(e.getDay()){case 0:return new Date(e.getFullYear()-1,11,29);case 1:return e;case 2:return new Date(e.getFullYear(),0,3);case 3:return new Date(e.getFullYear(),0,2);case 4:return new Date(e.getFullYear(),0,1);case 5:return new Date(e.getFullYear()-1,11,31);case 6:return new Date(e.getFullYear()-1,11,30)}}function g(e){var r=Lr(new Date(e.tm_year+1900,0,1),e.tm_yday),t=new Date(r.getFullYear(),0,4),n=new Date(r.getFullYear()+1,0,4),a=v(t),o=v(n);return m(a,r)<=0?m(o,r)<=0?r.getFullYear()+1:r.getFullYear():r.getFullYear()-1}var _={"%a":function(e){return l[e.tm_wday].substring(0,3)},"%A":function(e){return l[e.tm_wday]},"%b":function(e){return d[e.tm_mon].substring(0,3)},"%B":function(e){return d[e.tm_mon]},"%C":function(e){return p((e.tm_year+1900)/100|0,2)},"%d":function(e){return p(e.tm_mday,2)},"%e":function(e){return h(e.tm_mday,2," ")},"%g":function(e){return g(e).toString().substring(2)},"%G":function(e){return g(e)},"%H":function(e){return p(e.tm_hour,2)},"%I":function(e){var r=e.tm_hour;return 0==r?r=12:r>12&&(r-=12),p(r,2)},"%j":function(e){return p(e.tm_mday+Nr(Pr(e.tm_year+1900)?Ir:Mr,e.tm_mon-1),3)},"%m":function(e){return p(e.tm_mon+1,2)},"%M":function(e){return p(e.tm_min,2)},"%n":function(){return"\n"},"%p":function(e){return e.tm_hour>=0&&e.tm_hour<12?"AM":"PM"},"%S":function(e){return p(e.tm_sec,2)},"%t":function(){return"\t"},"%u":function(e){return e.tm_wday||7},"%U":function(e){var r=new Date(e.tm_year+1900,0,1),t=0===r.getDay()?r:Lr(r,7-r.getDay()),n=new Date(e.tm_year+1900,e.tm_mon,e.tm_mday);if(m(t,n)<0){var a=Nr(Pr(n.getFullYear())?Ir:Mr,n.getMonth()-1)-31,o=31-t.getDate()+a+n.getDate();return p(Math.ceil(o/7),2)}return 0===m(t,r)?"01":"00"},"%V":function(e){var r,t=new Date(e.tm_year+1900,0,4),n=new Date(e.tm_year+1901,0,4),a=v(t),o=v(n),i=Lr(new Date(e.tm_year+1900,0,1),e.tm_yday);return m(i,a)<0?"53":m(o,i)<=0?"01":(r=a.getFullYear()<e.tm_year+1900?e.tm_yday+32-a.getDate():e.tm_yday+1-a.getDate(),p(Math.ceil(r/7),2))},"%w":function(e){return e.tm_wday},"%W":function(e){var r=new Date(e.tm_year,0,1),t=1===r.getDay()?r:Lr(r,0===r.getDay()?1:7-r.getDay()+1),n=new Date(e.tm_year+1900,e.tm_mon,e.tm_mday);if(m(t,n)<0){var a=Nr(Pr(n.getFullYear())?Ir:Mr,n.getMonth()-1)-31,o=31-t.getDate()+a+n.getDate();return p(Math.ceil(o/7),2)}return 0===m(t,r)?"01":"00"},"%y":function(e){return(e.tm_year+1900).toString().substring(2)},"%Y":function(e){return e.tm_year+1900},"%z":function(e){var r=e.tm_gmtoff,t=r>=0;return r=(r=Math.abs(r)/60)/60*100+r%60,(t?"+":"-")+String("0000"+r).slice(-4)},"%Z":function(e){return e.tm_zone},"%%":function(){return"%"}};for(var f in _)s.includes(f)&&(s=s.replace(new RegExp(f,"g"),_[f](i)));var E,y,w,T,A,b,C=(E=s,y=!1,T=w>0?w:j(E)+1,A=new Array(T),b=U(E,A,0,A.length),y&&(A.length=b),A);return C.length>r?0:(function(e,r){t().set(e,r)}(C,e),C.length-1)}_||pe.initMainThreadBlock(),We=r.InternalError=Ne(Error,"InternalError"),function(){for(var e=new Array(256),r=0;r<256;++r)e[r]=String.fromCharCode(r);He=e}(),Be=r.BindingError=Ne(Error,"BindingError"),r.count_emval_handles=ze,r.get_first_emval=Ke,tr=r.UnboundTypeError=Ne(Error,"UnboundTypeError");var Ur=[null,function(e,r){if(_)return lr(1,1,e,r)},we,Te,Ae,yr,Cr,Sr,Or,kr,xr,Fr],Br={f:function(e,r,t,n){ae("Assertion failed: "+H(e)+", at: "+[r?H(r):"unknown filename",t,n?H(n):"unknown function"])},O:function(e){return jr(e+_e.SIZE)+_e.SIZE},s:function(e,r){return t=e,n=r,void pe.threadExitHandlers.push((function(){X.get(t)(n)}));var t,n},I:function(e,r,t){throw new Ee(e).init(r,t),e},o:we,R:Te,S:Ae,u:function(e){var r=be[e];delete be[e];var t=r.rawConstructor,n=r.rawDestructor,a=r.fields;Me([e],a.map((function(e){return e.getterReturnType})).concat(a.map((function(e){return e.setterArgumentType}))),(function(e){var o={};return a.forEach((function(r,t){var n=r.fieldName,i=e[t],u=r.getter,s=r.getterContext,c=e[t+a.length],f=r.setter,l=r.setterContext;o[n]={read:function(e){return i.fromWireType(u(s,e))},write:function(e,r){var t=[];f(l,e,c.toWireType(t,r)),Ce(t)}}})),[{name:r.name,fromWireType:function(e){var r={};for(var t in o)r[t]=o[t].read(e);return n(e),r},toWireType:function(e,r){for(var a in o)if(!(a in r))throw new TypeError('Missing field:  "'+a+'"');var i=t();for(a in o)o[a].write(i,r[a]);return null!==e&&e.push(n,i),i},argPackAdvance:8,readValueFromPointer:Se,destructorFunction:n}]}))},y:function(e,r,t,n,a){},U:function(e,r,n,a,i){var s=Le(n);Ge(e,{name:r=Ue(r),fromWireType:function(e){return!!e},toWireType:function(e,r){return r?a:i},argPackAdvance:8,readValueFromPointer:function(e){var a;if(1===n)a=t();else if(2===n)a=o();else{if(4!==n)throw new TypeError("Unknown boolean type size: "+r);a=u()}return this.fromWireType(a[e>>s])},destructorFunction:null})},T:function(e,r){Ge(e,{name:r=Ue(r),fromWireType:function(e){var r=Ve[e].value;return qe(e),r},toWireType:function(e,r){return Qe(r)},argPackAdvance:8,readValueFromPointer:Se,destructorFunction:null})},q:function(e,r,t){var n=Le(t);Ge(e,{name:r=Ue(r),fromWireType:function(e){return e},toWireType:function(e,r){if("number"!=typeof r&&"boolean"!=typeof r)throw new TypeError('Cannot convert "'+Xe(r)+'" to '+this.name);return r},argPackAdvance:8,readValueFromPointer:Ze(r,n),destructorFunction:null})},t:function(e,t,n,a,o,i){var s=function(e,r){for(var t=[],n=0;n<e;n++)t.push(u()[(r>>2)+n]);return t}(t,n);e=Ue(e),o=rr(a,o),$e(e,(function(){!function(e,r){var t=[],n={};throw r.forEach((function e(r){n[r]||ke[r]||(xe[r]?xe[r].forEach(e):(t.push(r),n[r]=!0))})),new tr(e+": "+t.map(nr).join([", "]))}("Cannot call "+e+" due to unbound types",s)}),t-1),Me([],s,(function(n){var a=[n[0],null].concat(n.slice(1));return function(e,t,n){r.hasOwnProperty(e)||Ie("Replacing nonexistant public symbol"),void 0!==r[e].overloadTable&&void 0!==n?r[e].overloadTable[n]=t:(r[e]=t,r[e].argCount=n)}(e,Je(e,a,null,o,i),t-1),[]}))},d:function(e,r,t,n,a){r=Ue(r),-1===a&&(a=4294967295);var o=Le(t),i=function(e){return e};if(0===n){var u=32-8*t;i=function(e){return e<<u>>>u}}var s=r.includes("unsigned");Ge(e,{name:r,fromWireType:i,toWireType:function(e,t){if("number"!=typeof t&&"boolean"!=typeof t)throw new TypeError('Cannot convert "'+Xe(t)+'" to '+this.name);if(t<n||t>a)throw new TypeError('Passing a number "'+Xe(t)+'" from JS side to C/C++ side to an argument of type "'+r+'", which is outside the valid range ['+n+", "+a+"]!");return s?t>>>0:0|t},argPackAdvance:8,readValueFromPointer:ar(r,o,0!==n),destructorFunction:null})},c:function(e,r,t){var n=[Int8Array,Uint8Array,Int16Array,Uint16Array,Int32Array,Uint32Array,Float32Array,Float64Array][r];function a(e){e>>=2;var r=s(),t=r[e],a=r[e+1];return new n(O,a,t)}Ge(e,{name:t=Ue(t),fromWireType:a,argPackAdvance:8,readValueFromPointer:a},{ignoreDuplicateRegistrations:!0})},r:function(e,r){var t="std::string"===(r=Ue(r));Ge(e,{name:r,fromWireType:function(e){var r,n=s()[e>>2];if(t)for(var o=e+4,i=0;i<=n;++i){var u=e+4+i;if(i==n||0==a()[u]){var c=H(o,u-o);void 0===r?r=c:(r+=String.fromCharCode(0),r+=c),o=u+1}}else{var f=new Array(n);for(i=0;i<n;++i)f[i]=String.fromCharCode(a()[e+4+i]);r=f.join("")}return Gr(e),r},toWireType:function(e,r){r instanceof ArrayBuffer&&(r=new Uint8Array(r));var n="string"==typeof r;n||r instanceof Uint8Array||r instanceof Uint8ClampedArray||r instanceof Int8Array||je("Cannot pass non-string to std::string");var o=(t&&n?function(){return j(r)}:function(){return r.length})(),i=jr(4+o+1);if(s()[i>>2]=o,t&&n)B(r,i+4,o+1);else if(n)for(var u=0;u<o;++u){var c=r.charCodeAt(u);c>255&&(Gr(i),je("String has UTF-16 code units that do not fit in 8 bits")),a()[i+4+u]=c}else for(u=0;u<o;++u)a()[i+4+u]=r[u];return null!==e&&e.push(Gr,i),i},argPackAdvance:8,readValueFromPointer:Se,destructorFunction:function(e){Gr(e)}})},k:function(e,r,t){var n,a,o,u,c;t=Ue(t),2===r?(n=G,a=Y,u=V,o=function(){return i()},c=1):4===r&&(n=q,a=z,u=K,o=function(){return s()},c=2),Ge(e,{name:t,fromWireType:function(e){for(var t,a=s()[e>>2],i=o(),u=e+4,f=0;f<=a;++f){var l=e+4+f*r;if(f==a||0==i[l>>c]){var d=n(u,l-u);void 0===t?t=d:(t+=String.fromCharCode(0),t+=d),u=l+r}}return Gr(e),t},toWireType:function(e,n){"string"!=typeof n&&je("Cannot pass non-string to C++ string type "+t);var o=u(n),i=jr(4+o+r);return s()[i>>2]=o>>c,a(n,i+4,o+r),null!==e&&e.push(Gr,i),i},argPackAdvance:8,readValueFromPointer:Se,destructorFunction:function(e){Gr(e)}})},v:function(e,r,t,n,a,o){be[e]={name:Ue(r),rawConstructor:rr(t,n),rawDestructor:rr(a,o),fields:[]}},e:function(e,r,t,n,a,o,i,u,s,c){be[e].fields.push({fieldName:Ue(r),getterReturnType:t,getter:rr(n,a),getterContext:o,setterArgumentType:i,setter:rr(u,s),setterContext:c})},V:function(e,r){Ge(e,{isVoid:!0,name:r=Ue(r),argPackAdvance:0,fromWireType:function(){},toWireType:function(e,r){}})},N:function(e,r){if(e==r)postMessage({cmd:"processQueuedMainThreadWork"});else if(_)postMessage({targetThread:e,cmd:"processThreadQueue"});else{var t=pe.pthreads[e],n=t&&t.worker;if(!n)return;n.postMessage({cmd:"processThreadQueue"})}return 1},i:qe,Z:function(e){return 0===e?Qe(ir()):(e=void 0===(t=or[r=e])?Ue(r):t,Qe(ir()[e]));var r,t},Y:function(e){e>4&&(Ve[e].refcount+=1)},x:function(e,t,n,a){e=function(e){return e||je("Cannot use deleted val. handle = "+e),Ve[e].value}(e);var o=sr[t];return o||(o=function(e){for(var t="",n=0;n<e;++n)t+=(0!==n?", ":"")+"arg"+n;var a="return function emval_allocator_"+e+"(constructor, argTypes, args) {\n";for(n=0;n<e;++n)a+="var argType"+n+" = requireRegisteredType(Module['HEAP32'][(argTypes >>> 2) + "+n+'], "parameter '+n+'");\nvar arg'+n+" = argType"+n+".readValueFromPointer(args);\nargs += argType"+n+"['argPackAdvance'];\n";return a+="var obj = new constructor("+t+");\nreturn __emval_register(obj);\n}\n",new Function("requireRegisteredType","Module","__emval_register",a)(ur,r,Qe)}(t),sr[t]=o),o(e,n,a)},b:function(){ae()},n:function(e,r,t){var n=function(e,r){var t;for(cr.length=0,r>>=2;t=a()[e++];){var n=t<105;n&&1&r&&r++,cr.push(n?c()[r++>>1]:u()[r]),++r}return cr}(r,t);return ce[e].apply(null,n)},J:function(){},m:function(e,r){},h:fr,g:de,j:me,B:function(e,r,t){a().copyWithin(e,r,r+t)},E:function(){return navigator.hardwareConcurrency},K:function(e,r,t){dr.length=r;for(var n=t>>3,a=0;a<r;a++)dr[a]=c()[n+a];return(e<0?ce[-e-1]:Ur[e]).apply(null,dr)},C:function(e){var r=a().length;if((e>>>=0)<=r)return!1;var t,n,o=2147483648;if(e>o)return!1;for(var i=1;i<=4;i*=2){var u=r*(1+.2/i);if(u=Math.min(u,e+100663296),hr(Math.min(o,((t=Math.max(e,u))%(n=65536)>0&&(t+=n-t%n),t))))return!0}return!1},L:function(e,r,t){return _r(e)?Er(e,r,t):yr(e,r,t)},l:function(e){},M:function(e,r){return t=e,n=r>>2,a=u()[n+6],o={alpha:!!u()[n+0],depth:!!u()[n+1],stencil:!!u()[n+2],antialias:!!u()[n+3],premultipliedAlpha:!!u()[n+4],preserveDrawingBuffer:!!u()[n+5],powerPreference:Tr[a],failIfMajorPerformanceCaveat:!!u()[n+7],majorVersion:u()[n+8],minorVersion:u()[n+9],enableExtensionsByDefault:u()[n+10],explicitSwapControl:u()[n+11],proxyContextToMainThread:u()[n+12],renderViaOffscreenBackBuffer:u()[n+13]},(i=_r(t))?o.explicitSwapControl?0:wr.createContext(i,o):0;var t,n,a,o,i},G:Cr,H:Sr,p:Or,Q:kr,w:xr,P:Fr,A:function(){pe.initRuntime()},a:b||r.wasmMemory,W:function(e,r,t,n){if("undefined"==typeof SharedArrayBuffer)return A("Current environment does not support SharedArrayBuffer, pthreads are not available!"),6;if(!e)return A("pthread_create called with a null thread pointer!"),28;var a=[];if(_&&0===a.length)return Kr(687865856,e,r,t,n);var o=0,i=0,c=0;r&&-1!=r?(o=u()[r>>2],o+=81920,i=u()[r+8>>2],c=0!==u()[r+12>>2]):o=2097152;var f=0==i;f?i=ut(16,o):M((i-=o)>0);for(var l=jr(228),d=0;d<57;++d)s()[(l>>2)+d]=0;u()[e>>2]=l,u()[l+12>>2]=l;var h=l+152;u()[h>>2]=h;var p={stackBase:i,stackSize:o,allocatedOwnStack:f,detached:c,startRoutine:t,pthread_ptr:l,arg:n,transferList:a};return _?(p.cmd="spawnThread",postMessage(p,a),0):Dr(p)},D:function(e,r){return function(e,r,t){if(!e)return A("pthread_join attempted on a null thread pointer!"),le.ESRCH;if(_&&rt()==e)return A("PThread "+e+" is attempting to join to itself!"),le.EDEADLK;if(!_&&qr()==e)return A("Main thread "+e+" is attempting to join to itself!"),le.EDEADLK;if(u()[e+12>>2]!==e)return A("pthread_join attempted on thread "+e+", which does not point to a valid thread, or does not exist anymore!"),le.ESRCH;if(Atomics.load(s(),e+64>>2))return A("Attempted to join thread "+e+", which was already detached!"),le.EINVAL;for(;;){var n=Atomics.load(s(),e+0>>2);if(1==n){var a=Atomics.load(s(),e+4>>2);return r&&(u()[r>>2]=a),Atomics.store(s(),e+64>>2,1),_?postMessage({cmd:"cleanupThread",thread:e}):he(e),0}if(!t)return le.EBUSY;Rr(),_||Qr(),fr(e+0,n,_?100:1)}}(e,r,!0)},z:function(e){},F:function(e,r,t,n){return Hr(e,r,t,n)},X:function(e){return e?(r=52,u()[et()>>2]=r,-1):0;var r}};!function(){var e={a:Br};function t(e,t){var n,a=e.exports;if(r.asm=a,X=r.asm.ca,n=r.asm._,$.unshift(n),pe.tlsInitFunctions.push(r.asm.ba),C=t,!_){var o=pe.unusedWorkers.length;pe.unusedWorkers.forEach((function(e){pe.loadWasmModuleToWorker(e,(function(){--o||function(){if(te--,r.monitorRunDependencies&&r.monitorRunDependencies(te),0==te&&ne){var e=ne;ne=null,e()}}()}))}))}}function n(e){t(e.instance,e.module)}function a(r){return(!w&&g&&"function"==typeof fetch?fetch(ue,{credentials:"same-origin"}).then((function(e){if(!e.ok)throw"failed to load wasm binary file at '"+ue+"'";return e.arrayBuffer()})).catch((function(){return se(ue)})):Promise.resolve().then((function(){return se(ue)}))).then((function(r){return WebAssembly.instantiate(r,e)})).then(r,(function(e){A("failed to asynchronously prepare wasm: "+e),ae(e)}))}if(_||(M(!_,"addRunDependency cannot be used in a pthread worker"),te++,r.monitorRunDependencies&&r.monitorRunDependencies(te)),r.instantiateWasm)try{return r.instantiateWasm(e,t)}catch(e){return A("Module.instantiateWasm callback failed with error: "+e),!1}(w||"function"!=typeof WebAssembly.instantiateStreaming||ie(ue)||"function"!=typeof fetch?a(n):fetch(ue,{credentials:"same-origin"}).then((function(r){return WebAssembly.instantiateStreaming(r,e).then(n,(function(e){return A("wasm streaming compile failed: "+e),A("falling back to ArrayBuffer instantiation"),a(n)}))}))).catch(l)}(),r.___wasm_call_ctors=function(){return(r.___wasm_call_ctors=r.asm._).apply(null,arguments)};var jr=r._malloc=function(){return(jr=r._malloc=r.asm.$).apply(null,arguments)},Gr=r._free=function(){return(Gr=r._free=r.asm.aa).apply(null,arguments)};r._emscripten_tls_init=function(){return(r._emscripten_tls_init=r.asm.ba).apply(null,arguments)};var Yr=r.___getTypeName=function(){return(Yr=r.___getTypeName=r.asm.da).apply(null,arguments)};r.___embind_register_native_and_builtin_types=function(){return(r.___embind_register_native_and_builtin_types=r.asm.ea).apply(null,arguments)},r._emscripten_current_thread_process_queued_calls=function(){return(r._emscripten_current_thread_process_queued_calls=r.asm.fa).apply(null,arguments)};var Vr=r._emscripten_register_main_browser_thread_id=function(){return(Vr=r._emscripten_register_main_browser_thread_id=r.asm.ga).apply(null,arguments)},qr=r._emscripten_main_browser_thread_id=function(){return(qr=r._emscripten_main_browser_thread_id=r.asm.ha).apply(null,arguments)},zr=r.__emscripten_do_dispatch_to_thread=function(){return(zr=r.__emscripten_do_dispatch_to_thread=r.asm.ia).apply(null,arguments)},Kr=r._emscripten_sync_run_in_main_thread_4=function(){return(Kr=r._emscripten_sync_run_in_main_thread_4=r.asm.ja).apply(null,arguments)},Qr=r._emscripten_main_thread_process_queued_calls=function(){return(Qr=r._emscripten_main_thread_process_queued_calls=r.asm.ka).apply(null,arguments)},Xr=r._emscripten_run_in_main_runtime_thread_js=function(){return(Xr=r._emscripten_run_in_main_runtime_thread_js=r.asm.la).apply(null,arguments)},Zr=r.__emscripten_call_on_thread=function(){return(Zr=r.__emscripten_call_on_thread=r.asm.ma).apply(null,arguments)},Jr=r.__emscripten_thread_init=function(){return(Jr=r.__emscripten_thread_init=r.asm.na).apply(null,arguments)},$r=r._emscripten_get_global_libc=function(){return($r=r._emscripten_get_global_libc=r.asm.oa).apply(null,arguments)},et=r.___errno_location=function(){return(et=r.___errno_location=r.asm.pa).apply(null,arguments)},rt=r._pthread_self=function(){return(rt=r._pthread_self=r.asm.qa).apply(null,arguments)},tt=r.___pthread_tsd_run_dtors=function(){return(tt=r.___pthread_tsd_run_dtors=r.asm.ra).apply(null,arguments)},nt=r.stackSave=function(){return(nt=r.stackSave=r.asm.sa).apply(null,arguments)},at=r.stackRestore=function(){return(at=r.stackRestore=r.asm.ta).apply(null,arguments)},ot=r.stackAlloc=function(){return(ot=r.stackAlloc=r.asm.ua).apply(null,arguments)},it=r._emscripten_stack_set_limits=function(){return(it=r._emscripten_stack_set_limits=r.asm.va).apply(null,arguments)},ut=r._memalign=function(){return(ut=r._memalign=r.asm.wa).apply(null,arguments)};r.dynCall_jiji=function(){return(r.dynCall_jiji=r.asm.xa).apply(null,arguments)},r.dynCall_iiji=function(){return(r.dynCall_iiji=r.asm.ya).apply(null,arguments)},r.dynCall_iiiiij=function(){return(r.dynCall_iiiiij=r.asm.za).apply(null,arguments)},r.dynCall_iiiiijj=function(){return(r.dynCall_iiiiijj=r.asm.Aa).apply(null,arguments)},r.dynCall_iiiiiijj=function(){return(r.dynCall_iiiiiijj=r.asm.Ba).apply(null,arguments)},r.dynCall_viijii=function(){return(r.dynCall_viijii=r.asm.Ca).apply(null,arguments)};var st,ct=r.__emscripten_allow_main_runtime_queued_calls=64244,ft=r.__emscripten_main_thread_futex=75060;function lt(e){this.name="ExitStatus",this.message="Program terminated with exit("+e+")",this.status=e}function dt(e){if(!(te>0)){if(_)return f(r),re(),void postMessage({cmd:"loaded"});!function(){if(!_){if(r.preRun)for("function"==typeof r.preRun&&(r.preRun=[r.preRun]);r.preRun.length;)e=r.preRun.shift(),J.unshift(e);var e;fe(J)}}(),te>0||(r.setStatus?(r.setStatus("Running..."),setTimeout((function(){setTimeout((function(){r.setStatus("")}),1),t()}),1)):t())}function t(){st||(st=!0,r.calledRun=!0,I||(re(),f(r),r.onRuntimeInitialized&&r.onRuntimeInitialized(),function(){if(!_){if(r.postRun)for("function"==typeof r.postRun&&(r.postRun=[r.postRun]);r.postRun.length;)e=r.postRun.shift(),ee.unshift(e);var e;fe(ee)}}()))}}if(r.PThread=pe,r.PThread=pe,r.wasmMemory=b,r.ExitStatus=lt,ne=function e(){st||dt(),st||(ne=e)},r.run=dt,r.preInit)for("function"==typeof r.preInit&&(r.preInit=[r.preInit]);r.preInit.length>0;)r.preInit.pop()();return _&&(S=!1,pe.initWorker()),dt(),r.ready});t.default=a}));
