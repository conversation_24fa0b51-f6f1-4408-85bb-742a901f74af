define(["require","exports","./initial-app-8f11e266","./util-06ce6ead"],(function(e,t,i,s){i.appendCss('._compress_cfw4k_1{width:100%;height:100%;contain:strict;display:grid;grid-template-rows:max-content 1fr;grid-template-areas:"header" "opts";--options-radius:7px}@media (min-width:600px){._compress_cfw4k_1{grid-template-rows:max-content 1fr;grid-template-columns:max-content 1fr max-content;grid-template-areas:"header header header" "optsLeft viewportOpts optsRight"}}._download-all-button-left_cfw4k_22{background-color:#000;border:none;padding:10px 20px;text-align:center;text-decoration:none;display:inline-block;font-size:16px;margin:4px 2px 20px;cursor:pointer;border-radius:4px;transition:background-color .3s ease;color:#fff}@media (max-width:599px){._download-all-button-left_cfw4k_22{width:100%;margin:8px 0}}._download-all-button-right_cfw4k_42:hover{background-color:rgba(95,180,228,.8)}._download-all-button-right_cfw4k_42{background-color:var(--blue);border:none;padding:10px 20px;text-align:center;text-decoration:none;display:inline-block;font-size:16px;margin:4px 8px 32px;cursor:pointer;border-radius:4px;transition:background-color .3s ease;color:#000}@media (max-width:599px){._download-all-button-right_cfw4k_42{width:100%;margin:8px 0}}._download-all-button-left_cfw4k_22:hover{background-color:rgba(0,0,0,.8)}._options_cfw4k_70{position:relative;color:#fff;font-size:1.2rem;max-width:400px;margin:0 auto;width:calc(100% - 60px);max-height:100%;overflow:hidden;grid-area:opts;display:grid;grid-template-rows:1fr max-content;align-content:end;align-self:end}@media (min-width:600px){._options_cfw4k_70{width:300px;margin:0}}._options-1-theme_cfw4k_91{--main-theme-color:var(--pink);--hot-theme-color:var(--hot-pink);--header-text-color:var(--white);--scroller-radius:var(--options-radius) var(--options-radius) 0 0;--rotate-copyoverbutton-angle:90deg}@media (min-width:600px){._options-1-theme_cfw4k_91{--scroller-radius:0 var(--options-radius) var(--options-radius) 0;--rotate-copyoverbutton-angle:0deg}}._options-2-theme_cfw4k_104{--main-theme-color:var(--blue);--hot-theme-color:var(--deep-blue);--header-text-color:var(--dark-text);--scroller-radius:var(--options-radius) var(--options-radius) 0 0;--rotate-copyoverbutton-angle:-90deg}@media (min-width:600px){._options-2-theme_cfw4k_104{--scroller-radius:var(--options-radius) 0 0 var(--options-radius);--rotate-copyoverbutton-angle:180deg}}._options-1_cfw4k_91{grid-area:optsLeft}._options-2_cfw4k_104{grid-area:optsRight}._multi-panel_cfw4k_129{position:relative;display:flex;flex-flow:column;overflow:hidden}._multi-panel_cfw4k_129>:first-child{order:2;margin-bottom:10px}._multi-panel_cfw4k_129>:nth-child(2){order:1}._multi-panel_cfw4k_129>:nth-child(3){order:4}._multi-panel_cfw4k_129>:nth-child(4){order:3}._back_cfw4k_154{margin:9px;justify-self:start;align-self:start}._back_cfw4k_154>svg{width:47px;overflow:visible}._back_cfw4k_154:focus ._back-blob_cfw4k_165{stroke:var(--deep-blue);stroke-width:5px;animation:_strokePulse_cfw4k_1 .5s ease forwards}@media (min-width:600px){._back_cfw4k_154{margin:14px}._back_cfw4k_154>svg{width:58px}}._top_cfw4k_180{grid-area:header;position:relative;display:flex;gap:16px;align-items:center}@media (max-width:599px){._top_cfw4k_180{align-items:start;flex-direction:column}}@keyframes _strokePulse_cfw4k_1{0%{stroke-width:8px}to{stroke-width:5px}}._back-blob_cfw4k_165{fill:var(--hot-pink);opacity:.77}._back-x_cfw4k_207{fill:var(--white)}');const n="_icon_75c6j_34";i.appendCss('._checkbox_75c6j_1{display:inline-block;position:relative;--size:17px}._checkbox_75c6j_1:before{content:"";position:absolute;top:50%;left:50%;width:200%;height:200%;background-color:var(--main-theme-color);border-radius:999px;opacity:.25;transform:translate(-50%,-50%) scale(0);transition-property:transform;transition-duration:.25s}._checkbox_75c6j_1:focus-within:before{transform:translate(-50%,-50%) scale(1)}._real-checkbox_75c6j_27{top:0;position:absolute;opacity:0;pointer-events:none}._icon_75c6j_34{display:block;width:var(--size);height:var(--size)}._checked_75c6j_40{fill:var(--main-theme-color)}._disabled_75c6j_44{fill:var(--dark-gray)}');const o=e=>i.h("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"currentColor",...e}),a=e=>i.h(o,{...e},i.h("circle",{cx:"12",cy:"12",r:"8",fill:"none",stroke:"currentColor","stroke-width":"2"})),r=e=>i.h(o,{...e},i.h("path",{d:"M12 3h5v2h2v2h2v5h-2V9h-2V7h-2V5h-3V3M21 12v5h-2v2h-2v2h-5v-2h3v-2h2v-2h2v-3h2M12 21H7v-2H5v-2H3v-5h2v3h2v2h2v2h3v2M3 12V7h2V5h2V3h5v2H9v2H7v2H5v3H3"})),l=e=>i.h(o,{...e},i.h("path",{d:"M3 13h2v-2H3v2zm0 4h2v-2H3v2zm2 4v-2H3c0 1.1.9 2 2 2zM3 9h2V7H3v2zm12 12h2v-2h-2v2zm4-18H9a2 2 0 0 0-2 2v10c0 1.1.9 2 2 2h10a2 2 0 0 0 2-2V5a2 2 0 0 0-2-2zm0 12H9V5h10v10zm-8 6h2v-2h-2v2zm-4 0h2v-2H7v2z"})),h=e=>i.h(o,{...e},i.h("path",{d:"M9 7H7v2h2V7zm0 4H7v2h2v-2zm0-8a2 2 0 0 0-2 2h2V3zm4 12h-2v2h2v-2zm6-12v2h2a2 2 0 0 0-2-2zm-6 0h-2v2h2V3zM9 17v-2H7c0 1.1.9 2 2 2zm10-4h2v-2h-2v2zm0-4h2V7h-2v2zm0 8a2 2 0 0 0 2-2h-2v2zM5 7H3v12c0 1.1.9 2 2 2h12v-2H5V7zm10-2h2V3h-2v2zm0 12h2v-2h-2v2z"})),c=e=>i.h(o,{...e},i.h("path",{d:"M15.6 5.5L11 1v3a8 8 0 0 0 0 16v-2a6 6 0 0 1 0-12v4l4.5-4.5zm4.3 5.5a8 8 0 0 0-1.6-3.9L17 8.5c.5.8.9 1.6 1 2.5h2zM13 17.9v2a8 8 0 0 0 3.9-1.6L15.5 17c-.8.5-1.6.9-2.5 1zm3.9-2.4l1.4 1.4A8 8 0 0 0 20 13h-2c-.1.9-.5 1.7-1 2.5z"})),p=e=>i.h(o,{...e},i.h("path",{d:"M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z"})),d=e=>i.h(o,{...e},i.h("path",{d:"M19 13H5v-2h14v2z"})),u=e=>i.h(o,{...e},i.h("path",{d:"M21.3 2.7v18.6H2.7V2.7h18.6m0-2.7H2.7A2.7 2.7 0 0 0 0 2.7v18.6A2.7 2.7 0 0 0 2.7 24h18.6a2.7 2.7 0 0 0 2.7-2.7V2.7A2.7 2.7 0 0 0 21.3 0z"})),g=e=>i.h(o,{...e},i.h("path",{d:"M21.3 0H2.7A2.7 2.7 0 0 0 0 2.7v18.6A2.7 2.7 0 0 0 2.7 24h18.6a2.7 2.7 0 0 0 2.7-2.7V2.7A2.7 2.7 0 0 0 21.3 0zm-12 18.7L2.7 12l1.8-1.9L9.3 15 19.5 4.8l1.8 1.9z"})),_=()=>i.h("svg",{viewBox:"0 -1.95 9.8 9.8"},i.h("path",{d:"M8.2.2a1 1 0 011.4 1.4l-4 4a1 1 0 01-1.4 0l-4-4A1 1 0 011.6.2l3.3 3.3L8.2.2z"})),m=()=>i.h("svg",{viewBox:"0 0 23.9 24.9"},i.h("path",{d:"M6.6 2.7h-4v13.2h2.7A2.7 2.7 0 018 18.6a2.7 2.7 0 002.6 2.6h2.7a2.7 2.7 0 002.6-2.6 2.7 2.7 0 012.7-2.7h2.6V2.7h-4a1.3 1.3 0 110-2.7h4A2.7 2.7 0 0124 2.7v18.5a2.7 2.7 0 01-2.7 2.7H2.7A2.7 2.7 0 010 21.2V2.7A2.7 2.7 0 012.7 0h4a1.3 1.3 0 010 2.7zm4 7.4V1.3a1.3 1.3 0 112.7 0v8.8L15 8.4a1.3 1.3 0 011.9 1.8l-4 4a1.3 1.3 0 01-1.9 0l-4-4A1.3 1.3 0 019 8.4z"})),f=()=>i.h("svg",{viewBox:"0 0 18 14"},i.h("path",{d:"M5.5 3.6v6.8L2.1 7l3.4-3.4M7 0L0 7l7 7V0zm4 0v14l7-7-7-7z"})),v=()=>i.h("svg",{viewBox:"0 0 24 24"},i.h("g",{fill:"none","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2"},i.h("path",{d:"M12.501 20.93c-.866.25-1.914-.166-2.176-1.247a1.724 1.724 0 0 0-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 0 0-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 0 0 1.066-2.573c-.94-1.543.826-3.31 2.37-2.37c1 .608 2.296.07 2.572-1.065c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 0 0 2.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 0 0 1.065 2.572c1.074.26 1.49 1.296 1.252 2.158M19 22v-6m3 3l-3-3l-3 3"}),i.h("path",{d:"M9 12a3 3 0 1 0 6 0a3 3 0 0 0-6 0"}))),b=()=>i.h("svg",{viewBox:"0 0 24 24"},i.h("g",{fill:"none","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2"},i.h("path",{d:"M12.52 20.924c-.87.262-1.93-.152-2.195-1.241a1.724 1.724 0 0 0-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 0 0-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 0 0 1.066-2.573c-.94-1.543.826-3.31 2.37-2.37c1 .608 2.296.07 2.572-1.065c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 0 0 2.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 0 0 1.065 2.572c1.088.264 1.502 1.323 1.242 2.192M19 16v6m3-3l-3 3l-3-3"}),i.h("path",{d:"M9 12a3 3 0 1 0 6 0a3 3 0 0 0-6 0"})));class w extends i.d{render(e){return i.h("div",{class:"_checkbox_75c6j_1"},e.checked?e.disabled?i.h(g,{class:`${n} _disabled_75c6j_44`}):i.h(g,{class:`${n} _checked_75c6j_40`}):i.h(u,{class:n}),i.h("input",{class:"_real-checkbox_75c6j_27",type:"checkbox",...e}))}}function A(e,t){const i=e.getContext("2d");if(!i)throw Error("Canvas not initialized");i.clearRect(0,0,e.width,e.height),i.putImageData(t,0,0)}async function x(e,t,i){const s=document.createElement("canvas");s.width=e.width,s.height=e.height;const n=s.getContext("2d");if(!n)throw Error("Canvas not initialized");let o;if(n.putImageData(e,0,0),"toBlob"in s)o=await new Promise((e=>s.toBlob(e,t,i)));else{const e=s.toDataURL(t,i),n=/data:([^;]+);base64,(.*)$/.exec(e);if(!n)throw Error("Data URL reading failed");const a=n[1],r=atob(n[2]),l=new Uint8Array(r.length);for(let e=0;e<l.length;e+=1)l[e]=r.charCodeAt(e);o=new Blob([l],{type:a})}if(!o)throw Error("Encoding failed");return o}function y(e,t={}){const{width:i=e.width,height:s=e.height,sx:n=0,sy:o=0,sw:a=e.width,sh:r=e.height}=t,l=document.createElement("canvas");l.width=i,l.height=s;const h=l.getContext("2d");if(!h)throw new Error("Could not create canvas context");return h.drawImage(e,n,o,a,r,0,0,i,s),h.getImageData(0,0,i,s)}i.appendCss("._children-exiting_1vpum_1>*{pointer-events:none}");const C=/Safari\//.test(navigator.userAgent)&&!/Chrom(e|ium)\//.test(navigator.userAgent);function k(e,t){for(const i in e)if(e[i]!==t[i])return!1;for(const i in t)if(!(i in e))return!1;return!0}const S=new Map;const z=new Map([[/^%PDF-/,"application/pdf"],[/^GIF87a/,"image/gif"],[/^GIF89a/,"image/gif"],[/^\x89PNG\x0D\x0A\x1A\x0A/,"image/png"],[/^\xFF\xD8\xFF/,"image/jpeg"],[/^BM/,"image/bmp"],[/^I I/,"image/tiff"],[/^II*/,"image/tiff"],[/^MM\x00*/,"image/tiff"],[/^RIFF....WEBPVP8[LX ]/s,"image/webp"],[/^\xF4\xFF\x6F/,"image/webp2"],[/^\x00\x00\x00 ftypavif\x00\x00\x00\x00/,"image/avif"],[/^\xff\x0a/,"image/jxl"],[/^\x00\x00\x00\x0cJXL \x0d\x0a\x87\x0a/,"image/jxl"],[/^qoif/,"image/qoi"]]);async function E(e){const t=await function(e){return new Response(e).arrayBuffer()}(e.slice(0,16)),i=Array.from(new Uint8Array(t)).map((e=>String.fromCodePoint(e))).join("");for(const[e,t]of z)if(e.test(i))return t;return""}async function I(e){const t=URL.createObjectURL(e);try{return await async function(e){const t=new Image;t.decoding="async",t.src=e;const i=new Promise(((e,i)=>{t.onload=()=>e(),t.onerror=()=>i(Error("Image loading error"))}));return t.decode&&await t.decode().catch((()=>null)),await i,t}(t)}finally{URL.revokeObjectURL(t)}}function P(e,t=0){return e?Number(M(e)):t}function q(e,t=0){return e?Number(L(e)):t}function L(e,t=!1){return e?e.checked:t}function M(e,t=""){return e?e.value:t}async function O(e,t){const{from:i=e.getBoundingClientRect().height,to:s=e.getBoundingClientRect().height,duration:n=1e3,easing:o="ease-in-out"}=t;if(i!==s&&0!==n)return e.style.height=i+"px",getComputedStyle(e).transform,e.style.transition=`height ${n}ms ${o}`,e.style.height=s+"px",new Promise((t=>{const i=s=>{s.target===e&&(e.style.transition="",e.removeEventListener("transitionend",i),e.removeEventListener("transitioncancel",i),t())};e.addEventListener("transitionend",i),e.addEventListener("transitioncancel",i)}));e.style.height=s+"px"}function B(e){e.preventDefault()}function j(e){if(e.aborted)throw new DOMException("AbortError","AbortError")}async function R(e,t){return j(e),Promise.race([t,new Promise(((t,i)=>{e.addEventListener("abort",(()=>i(new DOMException("AbortError","AbortError"))))}))])}class T extends i.d{static getDerivedStateFromProps(e,t){return!e.children&&t.children?{children:e.children,outgoingChildren:t.children}:e.children!==t.children?{children:e.children,outgoingChildren:void 0}:null}async componentDidUpdate(e,t){let i,s;if(t.children&&!this.state.children)i=this.base.getBoundingClientRect().height,s=0;else{if(t.children||!this.state.children)return;i=0,s=this.base.getBoundingClientRect().height}this.base.style.overflow="hidden",await O(this.base,{duration:300,from:i,to:s}),this.base.style.height="",this.base.style.overflow="",this.setState({outgoingChildren:void 0})}render({},{children:e,outgoingChildren:t}){return i.h("div",{class:t?"_children-exiting_1vpum_1":""},t||e)}}i.appendCss("._select_1onzk_1{position:relative}._builtin-select_1onzk_5{background:var(--black);border-radius:4px;font:inherit;padding:7px 25px 7px 10px;-webkit-appearance:none;-moz-appearance:none;border:none;color:#fff;width:100%}._arrow_1onzk_19{position:absolute;right:8px;top:50%;transform:translateY(-50%);fill:#fff;width:10px;pointer-events:none}._large_1onzk_29{padding:10px 35px 10px 10px;background:var(--dark-gray)}._large_1onzk_29 ._arrow_1onzk_19{right:13px}");class D extends i.d{render(e){const{large:t,...s}=e;return i.h("div",{class:"_select_1onzk_1"},i.h("select",{class:"_builtin-select_1onzk_5 "+(t?"_large_1onzk_29":""),...s}),i.h("div",{class:"_arrow_1onzk_19"},i.h(_,null)))}}const F="_options-title_1pwky_14",U="_option-text-first_1pwky_35",H="_option-toggle_1pwky_43",V="_option-reveal_1pwky_52 _option-toggle_1pwky_43",N="_option-one-cell_1pwky_68",Y="_section-enabler_1pwky_74 _option-toggle_1pwky_43",Q="_options-section_1pwky_81",G="_text-field_1pwky_85",W="_button-opacity_1pwky_152";var J,X=(function(e){e.exports=function(e,t,i,s,n){for(t=t.split?t.split("."):t,s=0;s<t.length;s++)e=e?e[t[s]]:n;return e===n?i:e}}(J={path:void 0,exports:{},require:function(e,t){return function(){throw new Error("Dynamic requires are not currently supported by @rollup/plugin-commonjs")}()}}),J.exports);function Z(e,t,i){var s=t.split("."),n=e.__lsc||(e.__lsc={});return n[t+i]||(n[t+i]=function(t){for(var n=t&&t.target||this,o={},a=o,r="string"==typeof i?X(t,i):n&&n.nodeName?n.type.match(/^che|rad/)?n.checked:n.value:t,l=0;l<s.length-1;l++)a=a[s[l]]||(a[s[l]]=!l&&e.state[s[l]]||{});a[s[l]]=r,e.setState(o)})}const K=["triangle","catrom","mitchell","lanczos3","hqx"];function $(e){return K.includes(e.method)}function ee(e,t){let i=0,n=0,o=e.width,a=e.height;return"contain"===t.fitMethod&&({sx:i,sy:n,sw:o,sh:a}=s.getContainOffsets(o,a,t.width,t.height)),function(e,t,i,s,n,o,a,r){const l=document.createElement("canvas");l.width=e.width,l.height=e.height,A(l,e);const h=document.createElement("canvas");h.width=o,h.height=a;const c=h.getContext("2d");if(!c)throw new Error("Could not create canvas context");return"pixelated"===r?c.imageSmoothingEnabled=!1:c.imageSmoothingQuality=r,c.drawImage(l,t,i,s,n,0,0,o,a),c.getImageData(0,0,o,a)}(e,i,n,o,a,t.width,t.height,t.method.slice(8))}async function te(e,t,i,n){if("vector"===i.method){if(!t.vectorImage)throw Error("No vector image available");return function(e,t){let i=0,n=0,o=e.width,a=e.height;return"contain"===t.fitMethod&&({sx:i,sy:n,sw:o,sh:a}=s.getContainOffsets(o,a,t.width,t.height)),y(e,{sx:i,sy:n,sw:o,sh:a,width:t.width,height:t.height})}(t.vectorImage,i)}return $(i)?n.resize(e,t.preprocessed,i):ee(t.preprocessed,i)}const ie=[.25,.3333,.5,1,2,3,4];class se extends i.d{constructor(e){super(e),this.state={maintainAspect:!0},this.presetWidths={},this.presetHeights={},this.onChange=()=>{this.reportOptions()},this.onWidthInput=()=>{if(this.state.maintainAspect){const e=P(this.form.width);this.form.height.value=Math.round(e/this.getAspect())}this.reportOptions()},this.onHeightInput=()=>{if(this.state.maintainAspect){const e=P(this.form.height);this.form.width.value=Math.round(e*this.getAspect())}this.reportOptions()},this.onPresetChange=e=>{const t=e.target;if("custom"===t.value)return;const i=Number(t.value);this.form.width.value=Math.round(this.props.inputWidth*i)+"",this.form.height.value=Math.round(this.props.inputHeight*i)+"",this.reportOptions()},this.generatePresetValues(e.inputWidth,e.inputHeight)}reportOptions(){const e=this.form,t=e.width,i=e.height,{options:s}=this.props;if(!t.checkValidity()||!i.checkValidity())return;const n={width:P(t),height:P(i),method:e.resizeMethod.value,premultiply:L(e.premultiply,!0),linearRGB:L(e.linearRGB,!0),fitMethod:M(e.fitMethod,s.fitMethod)};this.props.onChange(n)}getAspect(){return this.props.inputWidth/this.props.inputHeight}componentDidUpdate(e,t){!t.maintainAspect&&this.state.maintainAspect&&(this.form.height.value=Math.round(Number(this.form.width.value)/this.getAspect()),this.reportOptions())}componentWillReceiveProps(e){this.props.inputWidth===e.inputWidth&&this.props.inputHeight===e.inputHeight||this.generatePresetValues(e.inputWidth,e.inputHeight)}generatePresetValues(e,t){for(const i of ie)this.presetWidths[i]=Math.round(e*i),this.presetHeights[i]=Math.round(t*i)}getPreset(){const{width:e,height:t}=this.props.options;for(const i of ie)if(e===this.presetWidths[i]&&t===this.presetHeights[i])return i;return"custom"}render({options:e,isVector:t},{maintainAspect:s}){return i.h("form",{ref:i.linkRef(this,"form"),class:Q,onSubmit:B},i.h("label",{class:U},"Method:",i.h(D,{name:"resizeMethod",value:e.method,onChange:this.onChange},t&&i.h("option",{value:"vector"},"Vector"),i.h("option",{value:"lanczos3"},"Lanczos3"),i.h("option",{value:"mitchell"},"Mitchell"),i.h("option",{value:"catrom"},"Catmull-Rom"),i.h("option",{value:"triangle"},"Triangle (bilinear)"),i.h("option",{value:"hqx"},"hqx (pixel art)"),i.h("option",{value:"browser-pixelated"},"Browser pixelated"),i.h("option",{value:"browser-low"},"Browser low quality"),i.h("option",{value:"browser-medium"},"Browser medium quality"),i.h("option",{value:"browser-high"},"Browser high quality"))),i.h("label",{class:U},"Preset:",i.h(D,{value:this.getPreset(),onChange:this.onPresetChange},ie.map((e=>i.h("option",{value:e},100*e,"%"))),i.h("option",{value:"custom"},"Custom"))),i.h("label",{class:U},"Width:",i.h("input",{required:!0,class:G,name:"width",type:"number",min:"1",value:""+e.width,onInput:this.onWidthInput})),i.h("label",{class:U},"Height:",i.h("input",{required:!0,class:G,name:"height",type:"number",min:"1",value:""+e.height,onInput:this.onHeightInput})),i.h(T,null,$(e)?i.h("label",{class:H},"Premultiply alpha channel",i.h(w,{name:"premultiply",checked:e.premultiply,onChange:this.onChange})):null,$(e)?i.h("label",{class:H},"Linear RGB",i.h(w,{name:"linearRGB",checked:e.linearRGB,onChange:this.onChange})):null),i.h("label",{class:H},"Maintain aspect ratio",i.h(w,{name:"maintainAspect",checked:s,onChange:Z(this,"maintainAspect")})),i.h(T,null,s?null:i.h("label",{class:U},"Fit method:",i.h(D,{name:"fitMethod",value:e.fitMethod,onChange:this.onChange},i.h("option",{value:"stretch"},"Stretch"),i.h("option",{value:"contain"},"Contain")))))}}const ne={quality:50,qualityAlpha:-1,denoiseLevel:0,tileColsLog2:0,tileRowsLog2:0,speed:6,subsample:1,chromaDeltaQ:!1,sharpness:0,tune:0,enableSharpYUV:!1};var oe=Object.freeze({__proto__:null,label:"AVIF",mimeType:"image/avif",extension:"avif",defaultOptions:ne});const ae="image/gif";var re=Object.freeze({__proto__:null,label:"Browser GIF",mimeType:ae,extension:"gif",defaultOptions:{}});const le="image/jpeg";var he=Object.freeze({__proto__:null,label:"Browser JPEG",mimeType:le,extension:"jpg",defaultOptions:{quality:.75}});const ce="image/png";var pe=Object.freeze({__proto__:null,label:"Browser PNG",mimeType:ce,extension:"png",defaultOptions:{}});var de=Object.freeze({__proto__:null,label:"JPEG XL (beta)",mimeType:"image/jxl",extension:"jxl",defaultOptions:{effort:7,quality:75,progressive:!1,epf:-1,lossyPalette:!1,decodingSpeedTier:0,photonNoiseIso:0,lossyModular:!1}});var ue=Object.freeze({__proto__:null,label:"MozJPEG",mimeType:"image/jpeg",extension:"jpg",defaultOptions:{quality:75,baseline:!1,arithmetic:!1,progressive:!0,optimize_coding:!0,smoothing:0,color_space:3,quant_table:3,trellis_multipass:!1,trellis_opt_zero:!1,trellis_opt_table:!1,trellis_loops:1,auto_subsample:!0,chroma_subsample:2,separate_chroma_quality:!1,chroma_quality:75}});var ge=Object.freeze({__proto__:null,label:"OxiPNG",mimeType:"image/png",extension:"png",defaultOptions:{level:2,interlace:!1}});var _e=Object.freeze({__proto__:null,label:"QOI",mimeType:"image/qoi",extension:"qoi",defaultOptions:{}});var me=Object.freeze({__proto__:null,label:"WebP",mimeType:"image/webp",extension:"webp",defaultOptions:{quality:75,target_size:0,target_PSNR:0,method:4,sns_strength:50,filter_strength:60,filter_sharpness:0,filter_type:1,partitions:0,segments:4,pass:1,show_compressed:0,preprocessing:0,autofilter:0,partition_limit:0,alpha_compression:1,alpha_filtering:1,alpha_quality:100,lossless:0,exact:0,image_hint:0,emulate_jpeg_size:0,thread_level:0,low_memory:0,near_lossless:100,use_delta_palette:0,use_sharp_yuv:0}});const fe={quality:75,alpha_quality:75,effort:5,pass:1,sns:50,uv_mode:3,csp_type:0,error_diffusion:0,use_random_matrix:!1};var ve=Object.freeze({__proto__:null,label:"WebP v2 (unstable)",mimeType:"image/webp2",extension:"wp2",defaultOptions:fe});i.appendCss("._range_i4fg8_1{position:relative;z-index:0;display:grid;grid-template-columns:1fr auto}._label-text_i4fg8_8{color:#fff}._range-wc-container_i4fg8_12{position:relative;z-index:1;grid-row:2/3;grid-column:1/3}._range-wc_i4fg8_12{width:100%}._text-input_i4fg8_23{grid-row:1/2;grid-column:2/3;text-align:right;background:transparent;color:inherit;font:inherit;border:none;padding:2px 5px;box-sizing:border-box;text-decoration:underline;text-decoration-style:dotted;text-decoration-color:var(--main-theme-color);text-underline-position:under;width:54px;position:relative;left:5px}._text-input_i4fg8_23:focus{background:#fff;color:#000}._text-input_i4fg8_23{-moz-appearance:textfield}._text-input_i4fg8_23::-webkit-inner-spin-button,._text-input_i4fg8_23::-webkit-outer-spin-button{-moz-appearance:none;-webkit-appearance:none;margin:0}");const be="_value-display_1fqga_64",we="_touch-active_1fqga_102";i.appendCss('range-input{position:relative;display:flex;height:18px;width:130px;margin:2px;font:inherit;line-height:16px;overflow:visible}range-input[disabled]{filter:grayscale(1)}range-input:before{content:"";display:block;position:absolute;top:8px;left:0;width:100%;height:2px;border-radius:1px;background:linear-gradient(var(--main-theme-color),var(--main-theme-color)) 0/var(--value-percent,0) 100% no-repeat var(--medium-light-gray)}._input_1fqga_30{position:relative;width:100%;padding:0;margin:0;opacity:0}._thumb_1fqga_38{pointer-events:none;position:absolute;bottom:3px;left:0;margin-left:-6px;background:var(--main-theme-color);border-radius:50%;width:12px;height:12px}range-input:focus-within ._thumb_1fqga_38{outline:2px solid #fff}._thumb-wrapper_1fqga_54{position:absolute;left:6px;right:6px;bottom:0;height:0;overflow:visible;transform:translate(var(--value-percent,0))}._value-display_1fqga_64{position:absolute;box-sizing:border-box;left:0;bottom:3px;width:32px;height:62px;text-align:center;padding:8px 3px 0;margin:0 0 0 -16px;transform-origin:50% 90%;opacity:.0001;transform:scale(.2);color:#fff;font:inherit;font-size:calc(100% - var(--value-width, 3)/5*0.2em);text-overflow:clip;text-shadow:0 -.5px 0 rgba(0,0,0,.4);transition:all .2s ease;transition-property:opacity,transform;will-change:transform;pointer-events:none;overflow:hidden}._value-display_1fqga_64>svg{position:absolute;top:0;left:0;width:100%;height:100%;fill:var(--main-theme-color)}._value-display_1fqga_64>span{position:relative}._touch-active_1fqga_102+._thumb-wrapper_1fqga_54 ._value-display_1fqga_64{opacity:1;transform:scale(1)}._touch-active_1fqga_102+._thumb-wrapper_1fqga_54 ._thumb_1fqga_38{box-shadow:0 1px 3px rgba(0,0,0,.5)}');const Ae=["focus","blur"],xe=["input","change"],ye=["name","min","max","step","value","disabled"],Ce=["name","min","max","step","value","disabled"];class ke extends HTMLElement{constructor(){let e;super(),this._ignoreChange=!1,this._retargetEvent=e=>{e.stopImmediatePropagation();const t=new Event(e.type,e);this.dispatchEvent(t)},this._update=()=>{if(!this._valueDisplay)return;const e=Number(this.value)||0,t=Number(this.min)||0,i=100*(e-t)/((Number(this.max)||100)-t),s=this._getDisplayValue(e);this._valueDisplay.textContent=s,this.style.setProperty("--value-percent",i+"%"),this.style.setProperty("--value-width",""+s.length)},this._input=document.createElement("input"),this._input.type="range",this._input.className="_input_1fqga_30",this.addEventListener("pointerdown",(t=>{if(e)return;e=t.pointerId,this._input.classList.add(we);const i=t=>{t.pointerId===e&&(e=void 0,this._input.classList.remove(we),window.removeEventListener("pointerup",i),window.removeEventListener("pointercancel",i))};window.addEventListener("pointerup",i),window.addEventListener("pointercancel",i)}));for(const e of Ae)this._input.addEventListener(e,this._retargetEvent,!0);for(const e of xe)this._input.addEventListener(e,this._update,!0)}static get observedAttributes(){return Ce}connectedCallback(){this.contains(this._input)||(this.innerHTML=`<div class="_thumb-wrapper_1fqga_54"><div class="_thumb_1fqga_38"></div><div class="${be}"><svg width="32" height="62"><path d="M27.3 27.3C25 29.6 17 35.8 17 43v3c0 3 2.5 5 3.2 5.8a6 6 0 1 1-8.5 0C12.6 51 15 49 15 46v-3c0-7.2-8-13.4-10.3-15.7A16 16 0 0 1 16 0a16 16 0 0 1 11.3 27.3z"/></svg><span></span></div></div>`,this.insertBefore(this._input,this.firstChild),this._valueDisplay=this.querySelector("."+be+" > span"),this._update())}get labelPrecision(){return this.getAttribute("label-precision")||""}set labelPrecision(e){this.setAttribute("label-precision",e)}attributeChangedCallback(e,t,i){this._ignoreChange||(null===i?this._input.removeAttribute(e):this._input.setAttribute(e,i),this._reflectAttributes(),this._update())}_getDisplayValue(e){if(e>=1e4)return(e/1e3).toFixed(1)+"k";const t=Number(this.labelPrecision)||function(e){const t=e.split(".")[1];return t?t.length:0}(this.step)||0;return t?e.toFixed(t):Math.round(e).toString()}_reflectAttributes(){this._ignoreChange=!0;for(const e of Ce)this._input.hasAttribute(e)?this.setAttribute(e,this._input.getAttribute(e)):this.removeAttribute(e);this._ignoreChange=!1}}for(const e of ye)Object.defineProperty(ke.prototype,e,{get(){return this._input[e]},set(t){this._input[e]=t,this._reflectAttributes(),this._update()}});customElements.define("range-input",ke);class Se extends i.d{constructor(){super(...arguments),this.onTextInput=e=>{const t=e.target;t.value.trim()&&(this.rangeWc.value=t.value,this.rangeWc.dispatchEvent(new InputEvent("input",{bubbles:e.bubbles})))},this.onTextFocus=()=>{this.setState({textFocused:!0})},this.onTextBlur=()=>{this.setState({textFocused:!1})}}render(e,t){const{children:s,...n}=e,{value:o,min:a,max:r,step:l}=e,h=t.textFocused?this.inputEl.value:o;return i.h("label",{class:"_range_i4fg8_1"},i.h("span",{class:"_label-text_i4fg8_8"},s),i.h("div",{class:"_range-wc-container_i4fg8_12"},i.h("range-input",{ref:i.linkRef(this,"rangeWc"),class:"_range-wc_i4fg8_12",...n})),i.h("input",{ref:i.linkRef(this,"inputEl"),type:"number",class:"_text-input_i4fg8_23",value:h,min:a,max:r,step:l,onInput:this.onTextInput,onFocus:this.onTextFocus,onBlur:this.onTextBlur}))}}i.appendCss("._checkbox_uhmq6_1{display:inline-block;position:relative}._arrow_uhmq6_6{width:10px;height:10px;fill:var(--white);transition:transform .2s ease;transform:rotate(-90deg)}._arrow_uhmq6_6 svg{width:100%;height:100%;display:block}._real-checkbox_uhmq6_20{top:0;position:absolute;opacity:0;pointer-events:none}._real-checkbox_uhmq6_20:checked+._arrow_uhmq6_6{transform:none}");class ze extends i.d{render(e){return i.h("div",{class:"_checkbox_uhmq6_1"},i.h("input",{class:"_real-checkbox_uhmq6_20",type:"checkbox",...e}),i.h("div",{class:"_arrow_uhmq6_6"},i.h(_,null)))}}const Ee=100;class Ie extends i.d{constructor(){super(...arguments),this.state={showAdvanced:!1},this._inputChangeCallbacks=new Map,this._inputChange=(e,t)=>(this._inputChangeCallbacks.has(e)||this._inputChangeCallbacks.set(e,(i=>{const s=i.target,n="boolean"===t?"checked"in s?s.checked:!!s.value:"number"===t?Number(s.value):s.value,o={[e]:n},a={...this.state,...o},r={quality:a.lossless?Ee:a.quality,qualityAlpha:a.lossless||!a.separateAlpha?-1:a.alphaQuality,subsample:a.lossless?3:a.subsample,tileColsLog2:a.tileCols,tileRowsLog2:a.tileRows,speed:10-a.effort,chromaDeltaQ:a.chromaDeltaQ,sharpness:a.sharpness,denoiseLevel:a.denoiseLevel,tune:a.tune,enableSharpYUV:a.enableSharpYUV};o.options=r,this.setState(o),this.props.onChange(r)})),this._inputChangeCallbacks.get(e))}static getDerivedStateFromProps(e,t){if(t.options&&k(t.options,e.options))return null;const{options:i}=e,s=i.quality===Ee&&(-1==i.qualityAlpha||i.qualityAlpha==Ee)&&3==i.subsample,n=-1!==i.qualityAlpha;return{options:i,lossless:s,quality:s?ne.quality:i.quality,separateAlpha:n,alphaQuality:n?i.qualityAlpha:i.quality,subsample:i.subsample,tileRows:i.tileRowsLog2,tileCols:i.tileColsLog2,effort:10-i.speed,chromaDeltaQ:i.chromaDeltaQ,sharpness:i.sharpness,denoiseLevel:i.denoiseLevel,tune:i.tune,enableSharpYUV:i.enableSharpYUV}}render(e,{effort:t,lossless:s,alphaQuality:n,separateAlpha:o,quality:a,showAdvanced:r,subsample:l,tileCols:h,tileRows:c,chromaDeltaQ:p,sharpness:d,denoiseLevel:u,tune:g,enableSharpYUV:_}){return i.h("form",{class:Q,onSubmit:B},i.h("label",{class:H},"Lossless",i.h(w,{checked:s,onChange:this._inputChange("lossless","boolean")})),i.h(T,null,!s&&i.h("div",{class:N},i.h(Se,{min:"0",max:99,value:a,onInput:this._inputChange("quality","number")},"Quality:"))),i.h("label",{class:V},i.h(ze,{checked:r,onChange:Z(this,"showAdvanced")}),"Advanced settings"),i.h(T,null,r&&i.h("div",null,i.h(T,null,!s&&i.h("div",null,i.h("label",{class:U},"Subsample chroma:",i.h(D,{value:l,onChange:this._inputChange("subsample","number")},i.h("option",{value:"0"},"4:0:0"),i.h("option",{value:"1"},"4:2:0"),i.h("option",{value:"2"},"4:2:2"),i.h("option",{value:"3"},"4:4:4"))),i.h(T,null,1===l&&i.h("label",{class:H},"Sharp YUV Downsampling",i.h(w,{checked:_,onChange:this._inputChange("enableSharpYUV","boolean")}))),i.h("label",{class:H},"Separate alpha quality",i.h(w,{checked:o,onChange:this._inputChange("separateAlpha","boolean")})),i.h(T,null,o&&i.h("div",{class:N},i.h(Se,{min:"0",max:99,value:n,onInput:this._inputChange("alphaQuality","number")},"Alpha quality:"))),i.h("label",{class:H},"Extra chroma compression",i.h(w,{checked:p,onChange:this._inputChange("chromaDeltaQ","boolean")})),i.h("div",{class:N},i.h(Se,{min:"0",max:"7",value:d,onInput:this._inputChange("sharpness","number")},"Sharpness:")),i.h("div",{class:N},i.h(Se,{min:"0",max:"50",value:u,onInput:this._inputChange("denoiseLevel","number")},"Noise synthesis:")),i.h("label",{class:U},"Tuning:",i.h(D,{value:g,onChange:this._inputChange("tune","number")},i.h("option",{value:0},"Auto"),i.h("option",{value:1},"PSNR"),i.h("option",{value:2},"SSIM"))))),i.h("div",{class:N},i.h(Se,{min:"0",max:"6",value:c,onInput:this._inputChange("tileRows","number")},"Log2 of tile rows:")),i.h("div",{class:N},i.h(Se,{min:"0",max:"6",value:h,onInput:this._inputChange("tileCols","number")},"Log2 of tile cols:")))),i.h("div",{class:N},i.h(Se,{min:"0",max:10,value:t,onInput:this._inputChange("effort","number")},"Effort:")))}}var Pe=Object.freeze({__proto__:null,encode:(e,t,i,s)=>t.avifEncode(e,i,s),Options:Ie});var qe=Object.freeze({__proto__:null,featureTest:()=>async function(e){try{const t=await x(new ImageData(1,1),e);return!!t&&t.type===e}catch(e){return!1}}(ae),encode:(e,t,i,s)=>x(i,ae)});const Le=function(e={}){const{min:t=0,max:s=100,step:n=1}=e;class o extends i.d{constructor(){super(...arguments),this.onChange=e=>{const t=e.currentTarget;this.props.onChange({quality:Number(t.value)})}}render({options:e}){return i.h("div",{class:Q},i.h("div",{class:N},i.h(Se,{name:"quality",min:t,max:s,step:n||"any",value:e.quality,onInput:this.onChange},"Quality:")))}}return o}({min:0,max:1,step:.01});var Me=Object.freeze({__proto__:null,encode:(e,t,i,s)=>x(i,le,s.quality),Options:Le});var Oe=Object.freeze({__proto__:null,encode:(e,t,i,s)=>x(i,ce)});class Be extends i.d{constructor(){super(...arguments),this.state={lossless:!1},this._inputChangeCallbacks=new Map,this._inputChange=(e,t)=>(this._inputChangeCallbacks.has(e)||this._inputChangeCallbacks.set(e,(i=>{const s=i.target,n="boolean"===t?"checked"in s?s.checked:!!s.value:Number(s.value),o={[e]:n},a={...this.state,...o},r={effort:a.effort,quality:a.lossless?100:a.quality,progressive:a.progressive,epf:a.autoEdgePreservingFilter?-1:a.edgePreservingFilter,lossyPalette:!!a.lossless&&a.slightLoss,decodingSpeedTier:a.decodingSpeedTier,photonNoiseIso:a.photonNoiseIso,lossyModular:a.quality<7||a.alternativeLossy};o.options=r,this.setState(o),this.props.onChange(r)})),this._inputChangeCallbacks.get(e))}static getDerivedStateFromProps(e,t){if(t.options&&k(t.options,e.options))return null;const{options:i}=e;return{options:i,effort:i.effort,quality:i.quality,progressive:i.progressive,edgePreservingFilter:-1===i.epf?2:i.epf,lossless:100===i.quality,slightLoss:i.lossyPalette,autoEdgePreservingFilter:-1===i.epf,decodingSpeedTier:i.decodingSpeedTier,photonNoiseIso:i.photonNoiseIso,alternativeLossy:i.lossyModular}}render({},{effort:e,quality:t,progressive:s,edgePreservingFilter:n,lossless:o,slightLoss:a,autoEdgePreservingFilter:r,decodingSpeedTier:l,photonNoiseIso:h,alternativeLossy:c}){return i.h("form",{class:Q,onSubmit:B},i.h("label",{class:H},"Lossless",i.h(w,{name:"lossless",checked:o,onChange:this._inputChange("lossless","boolean")})),i.h(T,null,o&&i.h("label",{class:H},"Slight loss",i.h(w,{name:"slightLoss",checked:a,onChange:this._inputChange("slightLoss","boolean")}))),i.h(T,null,!o&&i.h("div",null,i.h("div",{class:N},i.h(Se,{min:"0",max:"99.9",step:"0.1",value:t,onInput:this._inputChange("quality","number")},"Quality:")),i.h("label",{class:H},"Alternative lossy mode",i.h(w,{checked:t<7||c,disabled:t<7,onChange:this._inputChange("alternativeLossy","boolean")})),i.h("label",{class:H},"Auto edge filter",i.h(w,{checked:r,onChange:this._inputChange("autoEdgePreservingFilter","boolean")})),i.h(T,null,!r&&i.h("div",{class:N},i.h(Se,{min:"0",max:"3",value:n,onInput:this._inputChange("edgePreservingFilter","number")},"Edge preserving filter:"))),i.h("div",{class:N},i.h(Se,{min:"0",max:"4",value:l,onInput:this._inputChange("decodingSpeedTier","number")},"Optimise for decoding speed (worse compression):")),i.h("div",{class:N},i.h(Se,{min:"0",max:"50000",step:"100",value:h,onInput:this._inputChange("photonNoiseIso","number")},"Noise equivalent to ISO:")))),i.h("label",{class:H},"Progressive rendering",i.h(w,{name:"progressive",checked:s,onChange:this._inputChange("progressive","boolean")})),i.h("div",{class:N},i.h(Se,{min:"1",max:"9",value:e,onInput:this._inputChange("effort","number")},"Effort:")))}}var je=Object.freeze({__proto__:null,encode:(e,t,i,s)=>t.jxlEncode(e,i,s),Options:Be});class Re extends i.d{constructor(){super(...arguments),this.state={showAdvanced:!1},this.onChange=e=>{const t=e.currentTarget.closest("form"),{options:i}=this.props,s={...this.props.options,baseline:L(t.baseline,i.baseline),progressive:L(t.progressive,i.progressive),optimize_coding:L(t.optimize_coding,i.optimize_coding),trellis_multipass:L(t.trellis_multipass,i.trellis_multipass),trellis_opt_zero:L(t.trellis_opt_zero,i.trellis_opt_zero),trellis_opt_table:L(t.trellis_opt_table,i.trellis_opt_table),auto_subsample:L(t.auto_subsample,i.auto_subsample),separate_chroma_quality:L(t.separate_chroma_quality,i.separate_chroma_quality),quality:P(t.quality,i.quality),chroma_quality:P(t.chroma_quality,i.chroma_quality),chroma_subsample:P(t.chroma_subsample,i.chroma_subsample),smoothing:P(t.smoothing,i.smoothing),color_space:P(t.color_space,i.color_space),quant_table:P(t.quant_table,i.quant_table),trellis_loops:P(t.trellis_loops,i.trellis_loops)};this.props.onChange(s)}}render({options:e},{showAdvanced:t}){return i.h("form",{class:Q,onSubmit:B},i.h("div",{class:N},i.h(Se,{name:"quality",min:"0",max:"100",value:e.quality,onInput:this.onChange},"Quality:")),i.h("label",{class:V},i.h(ze,{checked:t,onChange:Z(this,"showAdvanced")}),"Advanced settings"),i.h(T,null,t?i.h("div",null,i.h("label",{class:U},"Channels:",i.h(D,{name:"color_space",value:e.color_space,onChange:this.onChange},i.h("option",{value:1},"Grayscale"),i.h("option",{value:2},"RGB"),i.h("option",{value:3},"YCbCr"))),i.h(T,null,3===e.color_space?i.h("div",null,i.h("label",{class:H},"Auto subsample chroma",i.h(w,{name:"auto_subsample",checked:e.auto_subsample,onChange:this.onChange})),i.h(T,null,e.auto_subsample?null:i.h("div",{class:N},i.h(Se,{name:"chroma_subsample",min:"1",max:"4",value:e.chroma_subsample,onInput:this.onChange},"Subsample chroma by:"))),i.h("label",{class:H},"Separate chroma quality",i.h(w,{name:"separate_chroma_quality",checked:e.separate_chroma_quality,onChange:this.onChange})),i.h(T,null,e.separate_chroma_quality?i.h("div",{class:N},i.h(Se,{name:"chroma_quality",min:"0",max:"100",value:e.chroma_quality,onInput:this.onChange},"Chroma quality:")):null)):null),i.h("label",{class:H},"Pointless spec compliance",i.h(w,{name:"baseline",checked:e.baseline,onChange:this.onChange})),i.h(T,null,e.baseline?null:i.h("label",{class:H},"Progressive rendering",i.h(w,{name:"progressive",checked:e.progressive,onChange:this.onChange}))),i.h(T,null,e.baseline?i.h("label",{class:H},"Optimize Huffman table",i.h(w,{name:"optimize_coding",checked:e.optimize_coding,onChange:this.onChange})):null),i.h("div",{class:N},i.h(Se,{name:"smoothing",min:"0",max:"100",value:e.smoothing,onInput:this.onChange},"Smoothing:")),i.h("label",{class:U},"Quantization:",i.h(D,{name:"quant_table",value:e.quant_table,onChange:this.onChange},i.h("option",{value:"0"},"JPEG Annex K"),i.h("option",{value:"1"},"Flat"),i.h("option",{value:"2"},"MSSIM-tuned Kodak"),i.h("option",{value:"3"},"ImageMagick"),i.h("option",{value:"4"},"PSNR-HVS-M-tuned Kodak"),i.h("option",{value:"5"},"Klein et al"),i.h("option",{value:"6"},"Watson et al"),i.h("option",{value:"7"},"Ahumada et al"),i.h("option",{value:"8"},"Peterson et al"))),i.h("label",{class:H},"Trellis multipass",i.h(w,{name:"trellis_multipass",checked:e.trellis_multipass,onChange:this.onChange})),i.h(T,null,e.trellis_multipass?i.h("label",{class:H},"Optimize zero block runs",i.h(w,{name:"trellis_opt_zero",checked:e.trellis_opt_zero,onChange:this.onChange})):null),i.h("label",{class:H},"Optimize after trellis quantization",i.h(w,{name:"trellis_opt_table",checked:e.trellis_opt_table,onChange:this.onChange})),i.h("div",{class:N},i.h(Se,{name:"trellis_loops",min:"1",max:"50",value:e.trellis_loops,onInput:this.onChange},"Trellis quantization passes:"))):null))}}var Te=Object.freeze({__proto__:null,encode:function(e,t,i,s){return t.mozjpegEncode(e,i,s)},Options:Re});class De extends i.d{constructor(){super(...arguments),this.onChange=e=>{const t=e.currentTarget.closest("form"),i={level:P(t.level),interlace:L(t.interlace)};this.props.onChange(i)}}render({options:e}){return i.h("form",{class:Q,onSubmit:B},i.h("label",{class:H},"Interlace",i.h(w,{name:"interlace",checked:e.interlace,onChange:this.onChange})),i.h("div",{class:N},i.h(Se,{name:"level",min:"0",max:"6",step:"1",value:e.level,onInput:this.onChange},"Effort:")))}}var Fe=Object.freeze({__proto__:null,encode:async function(e,t,i,s){return t.oxipngEncode(e,i,s)},Options:De});var Ue=Object.freeze({__proto__:null,encode:function(e,t,i,s){return t.qoiEncode(e,i,s)}});const He=[[0,0],[1,20],[2,25],[3,30],[3,50],[4,50],[4,75],[4,90],[5,90],[6,100]];function Ve(e,t){const i=He.findIndex((([i,s])=>i===t&&s===e));return-1!==i?i:6}class Ne extends i.d{constructor(){super(...arguments),this.state={showAdvanced:!1},this.onChange=e=>{const t=e.currentTarget.closest("form"),i=q(t.lossless),{options:s}=this.props,n=P(t.lossless_preset,Ve(s.quality,s.method)),o={...s,lossless:i,quality:i?He[n][1]:P(t.quality,s.quality),method:i?He[n][0]:P(t.method_input,s.method),image_hint:q(t.image_hint,s.image_hint)?3:0,exact:q(t.exact,s.exact),alpha_compression:q(t.alpha_compression,s.alpha_compression),autofilter:q(t.autofilter,s.autofilter),filter_type:q(t.filter_type,s.filter_type),use_sharp_yuv:q(t.use_sharp_yuv,s.use_sharp_yuv),near_lossless:100-P(t.near_lossless,100-s.near_lossless),alpha_quality:P(t.alpha_quality,s.alpha_quality),alpha_filtering:P(t.alpha_filtering,s.alpha_filtering),sns_strength:P(t.sns_strength,s.sns_strength),filter_strength:P(t.filter_strength,s.filter_strength),filter_sharpness:7-P(t.filter_sharpness,7-s.filter_sharpness),pass:P(t.pass,s.pass),preprocessing:P(t.preprocessing,s.preprocessing),segments:P(t.segments,s.segments),partitions:P(t.partitions,s.partitions)};this.props.onChange(o)}}_losslessSpecificOptions(e){return i.h("div",{key:"lossless"},i.h("div",{class:N},i.h(Se,{name:"lossless_preset",min:"0",max:"9",value:Ve(e.quality,e.method),onInput:this.onChange},"Effort:")),i.h("div",{class:N},i.h(Se,{name:"near_lossless",min:"0",max:"100",value:""+(100-e.near_lossless),onInput:this.onChange},"Slight loss:")),i.h("label",{class:H},"Discrete tone image",i.h(w,{name:"image_hint",checked:3===e.image_hint,onChange:this.onChange})))}_lossySpecificOptions(e){const{showAdvanced:t}=this.state;return i.h("div",{key:"lossy"},i.h("div",{class:N},i.h(Se,{name:"method_input",min:"0",max:"6",value:e.method,onInput:this.onChange},"Effort:")),i.h("div",{class:N},i.h(Se,{name:"quality",min:"0",max:"100",step:"0.1",value:e.quality,onInput:this.onChange},"Quality:")),i.h("label",{class:V},i.h(ze,{checked:t,onChange:Z(this,"showAdvanced")}),"Advanced settings"),i.h(T,null,t?i.h("div",null,i.h("label",{class:H},"Compress alpha",i.h(w,{name:"alpha_compression",checked:!!e.alpha_compression,onChange:this.onChange})),i.h("div",{class:N},i.h(Se,{name:"alpha_quality",min:"0",max:"100",value:e.alpha_quality,onInput:this.onChange},"Alpha quality:")),i.h("div",{class:N},i.h(Se,{name:"alpha_filtering",min:"0",max:"2",value:e.alpha_filtering,onInput:this.onChange},"Alpha filter quality:")),i.h("label",{class:H},"Auto adjust filter strength",i.h(w,{name:"autofilter",checked:!!e.autofilter,onChange:this.onChange})),i.h(T,null,e.autofilter?null:i.h("div",{class:N},i.h(Se,{name:"filter_strength",min:"0",max:"100",value:e.filter_strength,onInput:this.onChange},"Filter strength:"))),i.h("label",{class:H},"Strong filter",i.h(w,{name:"filter_type",checked:!!e.filter_type,onChange:this.onChange})),i.h("div",{class:N},i.h(Se,{name:"filter_sharpness",min:"0",max:"7",value:7-e.filter_sharpness,onInput:this.onChange},"Filter sharpness:")),i.h("label",{class:H},"Sharp RGB→YUV conversion",i.h(w,{name:"use_sharp_yuv",checked:!!e.use_sharp_yuv,onChange:this.onChange})),i.h("div",{class:N},i.h(Se,{name:"pass",min:"1",max:"10",value:e.pass,onInput:this.onChange},"Passes:")),i.h("div",{class:N},i.h(Se,{name:"sns_strength",min:"0",max:"100",value:e.sns_strength,onInput:this.onChange},"Spatial noise shaping:")),i.h("label",{class:U},"Preprocess:",i.h(D,{name:"preprocessing",value:e.preprocessing,onChange:this.onChange},i.h("option",{value:"0"},"None"),i.h("option",{value:"1"},"Segment smooth"),i.h("option",{value:"2"},"Pseudo-random dithering"))),i.h("div",{class:N},i.h(Se,{name:"segments",min:"1",max:"4",value:e.segments,onInput:this.onChange},"Segments:")),i.h("div",{class:N},i.h(Se,{name:"partitions",min:"0",max:"3",value:e.partitions,onInput:this.onChange},"Partitions:"))):null))}render({options:e}){return i.h("form",{class:Q,onSubmit:B},i.h("label",{class:H},"Lossless",i.h(w,{name:"lossless",checked:!!e.lossless,onChange:this.onChange})),e.lossless?this._losslessSpecificOptions(e):this._lossySpecificOptions(e),i.h("label",{class:H},"Preserve transparent data",i.h(w,{name:"exact",checked:!!e.exact,onChange:this.onChange})))}}var Ye=Object.freeze({__proto__:null,encode:(e,t,i,s)=>t.webpEncode(e,i,s),Options:Ne});class Qe extends i.d{constructor(){super(...arguments),this.state={lossless:!1,slightLoss:0,quality:fe.quality,showAdvanced:!1},this._inputChangeCallbacks=new Map,this._inputChange=(e,t)=>(this._inputChangeCallbacks.has(e)||this._inputChangeCallbacks.set(e,(i=>{const s=i.target,n="boolean"===t?"checked"in s?s.checked:!!s.value:Number(s.value),o={[e]:n},a={...this.state,...o},r={effort:a.effort,quality:a.lossless?100-a.slightLoss:a.quality,alpha_quality:a.separateAlpha?a.alphaQuality:a.quality,pass:a.passes,sns:a.sns,uv_mode:a.uvMode,csp_type:a.colorSpace,error_diffusion:a.errorDiffusion,use_random_matrix:a.useRandomMatrix};o.options=r,this.setState(o),this.props.onChange(r)})),this._inputChangeCallbacks.get(e))}static getDerivedStateFromProps(e,t){if(t.options&&k(t.options,e.options))return null;const{options:i}=e,s={options:i,effort:i.effort,alphaQuality:i.alpha_quality,passes:i.pass,sns:i.sns,uvMode:i.uv_mode,colorSpace:i.csp_type,errorDiffusion:i.error_diffusion,useRandomMatrix:i.use_random_matrix,separateAlpha:i.quality!==i.alpha_quality};return i.quality>95?(s.lossless=!0,s.slightLoss=100-i.quality):(s.quality=i.quality,s.lossless=!1),s}render({},{effort:e,alphaQuality:t,passes:s,quality:n,sns:o,uvMode:a,lossless:r,slightLoss:l,colorSpace:h,errorDiffusion:c,useRandomMatrix:p,separateAlpha:d,showAdvanced:u}){return i.h("form",{class:Q,onSubmit:B},i.h("label",{class:H},"Lossless",i.h(w,{checked:r,onChange:this._inputChange("lossless","boolean")})),i.h(T,null,r&&i.h("div",{class:N},i.h(Se,{min:"0",max:"5",step:"0.1",value:l,onInput:this._inputChange("slightLoss","number")},"Slight loss:"))),i.h(T,null,!r&&i.h("div",null,i.h("div",{class:N},i.h(Se,{min:"0",max:"95",step:"0.1",value:n,onInput:this._inputChange("quality","number")},"Quality:")),i.h("label",{class:H},"Separate alpha quality",i.h(w,{checked:d,onChange:this._inputChange("separateAlpha","boolean")})),i.h(T,null,d&&i.h("div",{class:N},i.h(Se,{min:"0",max:"100",step:"1",value:t,onInput:this._inputChange("alphaQuality","number")},"Alpha Quality:"))),i.h("label",{class:V},i.h(ze,{checked:u,onChange:Z(this,"showAdvanced")}),"Advanced settings"),i.h(T,null,u&&i.h("div",null,i.h("div",{class:N},i.h(Se,{min:"1",max:"10",step:"1",value:s,onInput:this._inputChange("passes","number")},"Passes:")),i.h("div",{class:N},i.h(Se,{min:"0",max:"100",step:"1",value:o,onInput:this._inputChange("sns","number")},"Spatial noise shaping:")),i.h("div",{class:N},i.h(Se,{min:"0",max:"100",step:"1",value:c,onInput:this._inputChange("errorDiffusion","number")},"Error diffusion:")),i.h("label",{class:U},"Subsample chroma:",i.h(D,{value:a,onInput:this._inputChange("uvMode","number")},i.h("option",{value:3},"Auto"),i.h("option",{value:0},"Vary"),i.h("option",{value:1},"Half"),i.h("option",{value:2},"Off"))),i.h("label",{class:U},"Color space:",i.h(D,{value:h,onInput:this._inputChange("colorSpace","number")},i.h("option",{value:0},"YCoCg"),i.h("option",{value:1},"YCbCr"),i.h("option",{value:3},"YIQ"))),i.h("label",{class:H},"Random matrix",i.h(w,{checked:p,onChange:this._inputChange("useRandomMatrix","boolean")})))))),i.h("div",{class:N},i.h(Se,{min:"0",max:"9",step:"1",value:e,onInput:this._inputChange("effort","number")},"Effort:")))}}var Ge=Object.freeze({__proto__:null,encode:(e,t,i,s)=>t.wp2Encode(e,i,s),Options:Qe});const We={avif:{meta:oe,...Pe},browserGIF:{meta:re,...qe},browserJPEG:{meta:he,...Me},browserPNG:{meta:pe,...Oe},jxl:{meta:de,...je},mozJPEG:{meta:ue,...Te},oxiPNG:{meta:ge,...Fe},qoi:{meta:_e,...Ue},webP:{meta:me,...Ye},wp2:{meta:ve,...Ge}},Je={quantize:{enabled:!1,zx:0,maxNumColors:256,dither:1},resize:{enabled:!1,width:1,height:1,method:"lanczos3",fitMethod:"stretch",premultiply:!0,linearRGB:!0}},Xe={rotate:{rotate:0}};function Ze(e,t,i,s){const n=Array.isArray(t)?t:(""+t).split(".");let o=Ke(e);const a=o,r=n.length-1;for(const[e,t]of n.entries())e!==r?o=o[t]=Ke(o[t]):o[t]=s?Object.assign(Ke(o[t]),i):i;return a}function Ke(e){return Array.isArray(e)?[...e]:{...e}}function $e(e,t,i){return Ze(e,t,i,!0)}function et(e,t,i){return Ze(e,t,i,!1)}function tt(e){return e instanceof Error&&"AbortError"===e.name}const it=["avifDecode","jxlDecode","qoiDecode","webpDecode","wp2Decode","avifEncode","jxlEncode","mozjpegEncode","oxipngEncode","qoiEncode","webpEncode","wp2Encode","rotate","quantize","resize"];class st{constructor(){this._queue=Promise.resolve()}_terminateWorker(){this._worker&&(this._worker.terminate(),this._worker=void 0,this._workerApi=void 0)}_startWorker(){this._worker=new Worker("/c/features-worker-81fc6e6e.js"),this._workerApi=s.wrap(this._worker)}}for(const e of it)st.prototype[e]=function(t,...i){return this._queue=this._queue.catch((()=>{})).then((async()=>{if(t.aborted)throw new DOMException("AbortError","AbortError");clearTimeout(this._workerTimeout),this._worker||this._startWorker();const s=()=>this._terminateWorker();return t.addEventListener("abort",s),R(t,this._workerApi[e](...i)).finally((()=>{t.removeEventListener("abort",s),this._workerTimeout=setTimeout((()=>{this._terminateWorker()}),1e4)}))})),this._queue};const nt="_panel-heading_18nlm_1",ot="_panel-content_18nlm_4";i.appendCss("._panel-content_18nlm_4{height:0;overflow:auto}._panel-content_18nlm_4[aria-expanded=true]{height:auto}");const at="open-one-only";function rt(e){const t=e.closest("multi-panel > *, a, button");if(t&&t.classList.contains(nt))return t}async function lt(e){const t=e.nextElementSibling;if(!t)return;const i=t.getBoundingClientRect().height;e.removeAttribute("content-expanded"),t.setAttribute("aria-expanded","false"),await null,await O(t,{from:i,to:0,duration:300}),t.style.height=""}class ht extends HTMLElement{static get observedAttributes(){return[at]}constructor(){super(),this.addEventListener("click",this._onClick),this.addEventListener("keydown",this._onKeyDown),new MutationObserver((()=>this._childrenChange())).observe(this,{childList:!0})}connectedCallback(){this._childrenChange()}attributeChangedCallback(e,t,i){e===at&&null===i&&this._closeAll({exceptFirst:!0})}_onClick(e){const t=rt(e.target);t&&this._toggle(t)}_onKeyDown(e){const t=document.activeElement,i=rt(t);if(!i)return;if(t!==i)return;if(e.altKey)return;let s;switch(e.key){case"ArrowLeft":case"ArrowUp":s=this._prevHeading();break;case"ArrowRight":case"ArrowDown":s=this._nextHeading();break;case"Home":s=this._firstHeading();break;case"End":s=this._lastHeading();break;case"Enter":case" ":case"Spacebar":this._toggle(i);break;default:return}e.preventDefault(),s&&(t.setAttribute("tabindex","-1"),s.setAttribute("tabindex","0"),s.focus())}_toggle(e){e&&(e.hasAttribute("content-expanded")?lt(e):(this.openOneOnly&&this._closeAll(),async function(e){const t=e.nextElementSibling;if(!t)return;const i=t.getBoundingClientRect().height;e.setAttribute("content-expanded",""),t.setAttribute("aria-expanded","true");const s=t.getBoundingClientRect().height;await null,await O(t,{from:i,to:s,duration:300}),t.style.height=""}(e)))}_closeAll(e={}){const{exceptFirst:t=!1}=e;let i=[...this.children].filter((e=>e.matches("[content-expanded]")));t&&(i=i.slice(1));for(const e of i)lt(e)}_childrenChange(){let e=!1,t=this.firstElementChild;for(;t;){const i=t.nextElementSibling,s=Math.random().toString(36).substr(2,9);if(!i){console.error("<multi-panel> requires an even number of element children.");break}t.classList.remove(ot),i.classList.remove(nt),t.removeAttribute("aria-expanded"),t.removeAttribute("content-expanded"),i.removeAttribute("tabindex"),t.classList.add(nt),i.classList.add(ot),t.id=`panel-heading-${s}`,t.setAttribute("aria-controls",`panel-content-${s}`),i.id=`panel-content-${s}`,i.setAttribute("aria-labelledby",`panel-heading-${s}`),"0"===t.getAttribute("tabindex")?e=!0:t.setAttribute("tabindex","-1"),i.setAttribute("aria-expanded",t.hasAttribute("content-expanded")?"true":"false"),t=i.nextElementSibling}!e&&this.firstElementChild&&this.firstElementChild.setAttribute("tabindex","0"),this.openOneOnly&&this._closeAll({exceptFirst:!0})}_prevHeading(){if(this.firstElementChild===document.activeElement)return this.firstElementChild;const e=document.activeElement.previousElementSibling;return e?e.previousElementSibling:void 0}_nextHeading(){const e=document.activeElement.nextElementSibling;if(e)return e.nextElementSibling}_firstHeading(){return this.firstElementChild}_lastHeading(){const e=this.lastElementChild;if(e&&e.classList.contains(nt))return e;const t=this.lastElementChild;return t?t.previousElementSibling:void 0}get openOneOnly(){return this.hasAttribute(at)}set openOneOnly(e){e?this.setAttribute(at,""):this.removeAttribute(at)}}customElements.define("multi-panel",ht);i.appendCss("._options-scroller_1pwky_1{--horizontal-padding:15px;border-radius:var(--scroller-radius);overflow:hidden}@media (min-width:600px){._options-scroller_1pwky_1{overflow-x:hidden;overflow-y:auto;-webkit-overflow-scrolling:touch}}._options-title_1pwky_14{background-color:var(--main-theme-color);color:var(--header-text-color);margin:0;padding:10px var(--horizontal-padding);font-weight:700;font-size:1.4rem;border-bottom:1px solid var(--off-black);transition:all .3s ease-in-out;transition-property:background-color,color;position:sticky;top:0;z-index:1}._original-image_1pwky_30 ._options-title_1pwky_14{background-color:var(--black);color:var(--white)}._option-text-first_1pwky_35{grid-template-columns:87px 1fr}._option-text-first_1pwky_35,._option-toggle_1pwky_43{display:grid;align-items:center;gap:.7em;padding:10px var(--horizontal-padding)}._option-toggle_1pwky_43{cursor:pointer;grid-template-columns:1fr auto}._option-reveal_1pwky_52{grid-template-columns:auto 1fr;gap:1em;border-top:1px solid hsla(0,0%,100%,.27);transition-property:background-color;transition-duration:.25s}._option-reveal_1pwky_52:focus-within,._option-reveal_1pwky_52:hover{background-color:hsla(0,0%,100%,.13)}._option-one-cell_1pwky_68{display:grid;grid-template-columns:1fr;padding:10px var(--horizontal-padding)}._section-enabler_1pwky_74{background:var(--dark-gray);padding:15px var(--horizontal-padding);border-bottom:1px solid var(--off-black)}._options-section_1pwky_81{background:var(--off-black)}._text-field_1pwky_85{background-color:var(--black);color:var(--white);font:inherit;border:none;padding:6px 6px 6px 10px;width:100%;box-sizing:border-box;border-radius:4px}._title-and-buttons_1pwky_96{grid-template-columns:1fr;grid-auto-columns:max-content;grid-auto-flow:column;display:grid;gap:.8rem}._title-button_1pwky_104 svg{--size:20px;display:block;width:var(--size);height:var(--size)}._cli-button_1pwky_114 svg{stroke:var(--header-text-color)}._copy-over-button_1pwky_122{transform:rotate(var(--rotate-copyoverbutton-angle))}._copy-over-button_1pwky_122 svg{fill:var(--header-text-color)}._copy-over-button_1pwky_122:focus{outline:var(--header-text-color) solid 2px;outline-offset:.25em}._import-button_1pwky_139 svg,._save-button_1pwky_138 svg{stroke:var(--header-text-color)}._import-button_1pwky_139:focus,._save-button_1pwky_138:focus{outline:var(--header-text-color) solid 2px;outline-offset:.25em}._button-opacity_1pwky_152{pointer-events:none;cursor:not-allowed}._button-opacity_1pwky_152 svg{opacity:.5}");i.appendCss('._checkbox_10azs_1{display:inline-block;position:relative}._track_10azs_6{--thumb-size:14px;background:var(--black);border-radius:1000px;width:24px;padding:3px calc(var(--thumb-size)/2 + 3px)}._checkbox_10azs_1:focus-within ._track_10azs_6{outline:2px solid #fff}._thumb_10azs_18{position:relative;width:var(--thumb-size);height:var(--thumb-size);background:var(--less-light-gray);border-radius:100%;transform:translateX(calc(var(--thumb-size)/-2));overflow:hidden}._thumb_10azs_18:before{content:"";position:absolute;top:0;left:0;right:0;bottom:0;background:var(--main-theme-color);opacity:0;transition:opacity .2s ease}._thumb-track_10azs_40{transition:transform .2s ease}._real-checkbox_10azs_44{top:0;position:absolute;opacity:0;pointer-events:none}._real-checkbox_10azs_44:checked+._track_10azs_6 ._thumb-track_10azs_40{transform:translateX(100%)}._real-checkbox_10azs_44:checked+._track_10azs_6 ._thumb_10azs_18:before{opacity:1}');class ct extends i.d{render(e){return i.h("div",{class:"_checkbox_10azs_1"},i.h("input",{class:"_real-checkbox_10azs_44",type:"checkbox",...e}),i.h("div",{class:"_track_10azs_6"},i.h("div",{class:"_thumb-track_10azs_40"},i.h("div",{class:"_thumb_10azs_18"}))))}}const pt=new Promise((e=>{const t="38384040373937396665";let i="";const s=n=>{i+=n.keyCode,i=i.slice(-20),i===t&&(window.removeEventListener("keydown",s),e())};window.addEventListener("keydown",s)}));class dt extends i.d{constructor(){super(...arguments),this.state={extendedSettings:!1},this.onChange=e=>{const t=e.currentTarget.closest("form"),{options:i}=this.props,s={zx:P(t.zx,i.zx),maxNumColors:P(t.maxNumColors,i.maxNumColors),dither:P(t.dither)};this.props.onChange(s)}}componentDidMount(){pt.then((()=>{this.setState({extendedSettings:!0})}))}render({options:e},{extendedSettings:t}){return i.h("form",{class:Q,onSubmit:B},i.h(T,null,t?i.h("label",{class:U},"Type:",i.h(D,{name:"zx",value:""+e.zx,onChange:this.onChange},i.h("option",{value:"0"},"Standard"),i.h("option",{value:"1"},"ZX"))):null),i.h(T,null,e.zx?null:i.h("div",{class:N},i.h(Se,{name:"maxNumColors",min:"2",max:"256",value:e.maxNumColors,onInput:this.onChange},"Colors:"))),i.h("div",{class:N},i.h(Se,{name:"dither",min:"0",max:"1",step:"0.01",value:e.dither,onInput:this.onChange},"Dithering:")))}}const ut=(async()=>{const e={...We};return await Promise.all(Object.entries(We).map((async([t,i])=>{"featureTest"in i&&!await i.featureTest()&&delete e[t]}))),e})();class gt extends i.d{constructor(){super(),this.state={supportedEncoderMap:void 0,leftSideSettings:localStorage.getItem("leftSideSettings"),rightSideSettings:localStorage.getItem("rightSideSettings")},this.setLeftSideSettings=()=>{this.setState({leftSideSettings:localStorage.getItem("leftSideSettings")})},this.setRightSideSettings=()=>{this.setState({rightSideSettings:localStorage.getItem("rightSideSettings")})},this.onEncoderTypeChange=e=>{const t=e.currentTarget.value;this.props.onEncoderTypeChange(this.props.index,t)},this.onProcessorEnabledChange=e=>{const t=e.currentTarget,i=t.name.split(".")[0];this.props.onProcessorOptionsChange(this.props.index,et(this.props.processorState,`${i}.enabled`,t.checked))},this.onQuantizerOptionsChange=e=>{this.props.onProcessorOptionsChange(this.props.index,$e(this.props.processorState,"quantize",e))},this.onResizeOptionsChange=e=>{this.props.onProcessorOptionsChange(this.props.index,$e(this.props.processorState,"resize",e))},this.onEncoderOptionsChange=e=>{this.props.onEncoderOptionsChange(this.props.index,e)},this.onCopyToOtherSideClick=()=>{this.props.onCopyToOtherSideClick(this.props.index)},this.onSaveSideSettingClick=()=>{this.props.onSaveSideSettingsClick(this.props.index)},this.onImportSideSettingsClick=()=>{this.props.onImportSideSettingsClick(this.props.index)},ut.then((e=>this.setState({supportedEncoderMap:e})))}componentDidMount(){window.addEventListener("leftSideSettings",this.setLeftSideSettings),window.addEventListener("rightSideSettings",this.setRightSideSettings)}componentWillUnmount(){window.removeEventListener("leftSideSettings",this.setLeftSideSettings),window.removeEventListener("removeSideSettings",this.setRightSideSettings)}render({source:e,encoderState:t,processorState:s},{supportedEncoderMap:n}){const o=t&&We[t.type],a=o&&"Options"in o?o.Options:void 0;return i.h("div",{class:"_options-scroller_1pwky_1 "+(t?"":"_original-image_1pwky_30")},i.h(T,null,t?i.h("div",null,i.h("h3",{class:F},i.h("div",{class:"_title-and-buttons_1pwky_96"},"Edit",i.h("button",{class:"_copy-over-button_1pwky_122 _title-button_1pwky_104 unbutton",title:"Copy settings to other side",onClick:this.onCopyToOtherSideClick},i.h(f,null)),i.h("button",{class:"_save-button_1pwky_138 _title-button_1pwky_104 unbutton",title:"Save side settings",onClick:this.onSaveSideSettingClick},i.h(v,null)),i.h("button",{class:"_import-button_1pwky_139 _title-button_1pwky_104 unbutton "+(this.state.leftSideSettings||0!==this.props.index?"":W)+" "+(this.state.rightSideSettings||1!==this.props.index?"":W),title:"Import saved side settings",onClick:this.onImportSideSettingsClick,disabled:!this.state.leftSideSettings&&0===this.props.index||!this.state.rightSideSettings&&1===this.props.index},i.h(b,null)))),i.h("label",{class:Y},"Resize",i.h(ct,{name:"resize.enable",checked:!!s.resize.enabled,onChange:this.onProcessorEnabledChange})),i.h(T,null,s.resize.enabled?i.h(se,{isVector:Boolean(e&&e.vectorImage),inputWidth:e?e.preprocessed.width:1,inputHeight:e?e.preprocessed.height:1,options:s.resize,onChange:this.onResizeOptionsChange}):null),i.h("label",{class:Y},"Reduce palette",i.h(ct,{name:"quantize.enable",checked:!!s.quantize.enabled,onChange:this.onProcessorEnabledChange})),i.h(T,null,s.quantize.enabled?i.h(dt,{options:s.quantize,onChange:this.onQuantizerOptionsChange}):null)):null),i.h("h3",{class:F},"Compress"),i.h("section",{class:`${N} ${Q}`},n?i.h(D,{value:t?t.type:"identity",onChange:this.onEncoderTypeChange,large:!0},i.h("option",{value:"identity"},"Original Image "+(this.props.source?`(${this.props.source.file.name})`:"")),Object.entries(n).map((([e,t])=>i.h("option",{value:e},t.meta.label)))):i.h(D,{large:!0},i.h("option",null,"Loading…"))),i.h(T,null,a&&i.h(a,{options:t.options,onChange:this.onEncoderOptionsChange})))}}i.appendCss('._output_ljgnb_1{display:contents}._output_ljgnb_1:before{content:"";position:absolute;top:0;left:0;right:0;bottom:0;background:#000;opacity:.8;transition:opacity .5s ease}._output_ljgnb_1._alt-background_ljgnb_16:before{opacity:0}._pinch-zoom_ljgnb_25{outline:none;display:flex;justify-content:center;align-items:center}._pinch-target_ljgnb_33{will-change:auto;flex-shrink:0}._controls_ljgnb_42{display:flex;justify-content:center;overflow:hidden;flex-wrap:wrap;contain:content;grid-area:header;align-self:center;padding:9px 66px;position:relative;gap:6px;pointer-events:none}._controls_ljgnb_42>*{pointer-events:auto}@media (min-width:860px){._controls_ljgnb_42{padding:9px;flex-wrap:wrap-reverse;grid-area:viewportOpts;align-self:end}}._button-group_ljgnb_68{display:flex;position:relative;z-index:100}._button_ljgnb_68,._zoom_ljgnb_75{display:flex;align-items:center;box-sizing:border-box;background-color:rgba(29,29,29,.92);border:1px solid rgba(0,0,0,.67);border-right-width:0;line-height:1.1;white-space:nowrap;height:39px;padding:0 8px;font-size:1.2rem;cursor:pointer}._button_ljgnb_68:focus-visible,._zoom_ljgnb_75:focus-visible{box-shadow:0 0 0 2px #fff;outline:none;z-index:1}._button_ljgnb_68{color:#fff;margin:0}._button_ljgnb_68:hover{background:rgba(50,50,50,.92)}._button_ljgnb_68._active_ljgnb_104{background:rgba(72,72,72,.92);color:#fff}._first-button_ljgnb_110{border-radius:6px 0 0 6px}._last-button_ljgnb_115{border-radius:0 6px 6px 0;border-right-width:1px}._zoom_ljgnb_75{cursor:text;width:7rem;font:inherit;text-align:center;justify-content:center}._zoom_ljgnb_75:focus{box-shadow:inset 0 1px 4px rgba(0,0,0,.2),0 0 0 2px #fff}span._zoom_ljgnb_75{color:#939393;font-size:.8rem;line-height:1.2;font-weight:100}input._zoom_ljgnb_75{text-indent:3px}._zoom-value_ljgnb_146,input._zoom_ljgnb_75{font-size:1.2rem;letter-spacing:.05rem;font-weight:700;color:#fff}._zoom-value_ljgnb_146{margin:0 3px 0 0;padding:0 2px;border-bottom:1px dashed #999}._buttons-no-wrap_ljgnb_156{display:flex;pointer-events:none}._buttons-no-wrap_ljgnb_156>*{pointer-events:auto}._pixelated_ljgnb_165{image-rendering:crisp-edges;image-rendering:pixelated}');class _t{constructor(e){this.id=-1,this.nativePointer=e,this.pageX=e.pageX,this.pageY=e.pageY,this.clientX=e.clientX,this.clientY=e.clientY,self.Touch&&e instanceof Touch?this.id=e.identifier:mt(e)&&(this.id=e.pointerId)}getCoalesced(){if("getCoalescedEvents"in this.nativePointer){const e=this.nativePointer.getCoalescedEvents().map((e=>new _t(e)));if(e.length>0)return e}return[this]}}const mt=e=>"pointerId"in e,ft=e=>"changedTouches"in e,vt=()=>{};class bt{constructor(e,{start:t=()=>!0,move:i=vt,end:s=vt,rawUpdates:n=!1,avoidPointerEvents:o=!1}={}){this._element=e,this.startPointers=[],this.currentPointers=[],this._excludeFromButtonsCheck=new Set,this._pointerStart=e=>{if(mt(e)&&0===e.buttons)this._excludeFromButtonsCheck.add(e.pointerId);else if(!(1&e.buttons))return;const t=new _t(e);if(!this.currentPointers.some((e=>e.id===t.id))&&this._triggerPointerStart(t,e))if(mt(e)){(e.target&&"setPointerCapture"in e.target?e.target:this._element).setPointerCapture(e.pointerId),this._element.addEventListener(this._rawUpdates?"pointerrawupdate":"pointermove",this._move),this._element.addEventListener("pointerup",this._pointerEnd),this._element.addEventListener("pointercancel",this._pointerEnd)}else window.addEventListener("mousemove",this._move),window.addEventListener("mouseup",this._pointerEnd)},this._touchStart=e=>{for(const t of Array.from(e.changedTouches))this._triggerPointerStart(new _t(t),e)},this._move=e=>{if(!(ft(e)||mt(e)&&this._excludeFromButtonsCheck.has(e.pointerId)||0!==e.buttons))return void this._pointerEnd(e);const t=this.currentPointers.slice(),i=ft(e)?Array.from(e.changedTouches).map((e=>new _t(e))):[new _t(e)],s=[];for(const e of i){const t=this.currentPointers.findIndex((t=>t.id===e.id));-1!==t&&(s.push(e),this.currentPointers[t]=e)}0!==s.length&&this._moveCallback(t,s,e)},this._triggerPointerEnd=(e,t)=>{if(!ft(t)&&1&t.buttons)return!1;const i=this.currentPointers.findIndex((t=>t.id===e.id));if(-1===i)return!1;this.currentPointers.splice(i,1),this.startPointers.splice(i,1),this._excludeFromButtonsCheck.delete(e.id);const s=!("mouseup"===t.type||"touchend"===t.type||"pointerup"===t.type);return this._endCallback(e,t,s),!0},this._pointerEnd=e=>{if(this._triggerPointerEnd(new _t(e),e))if(mt(e)){if(this.currentPointers.length)return;this._element.removeEventListener(this._rawUpdates?"pointerrawupdate":"pointermove",this._move),this._element.removeEventListener("pointerup",this._pointerEnd),this._element.removeEventListener("pointercancel",this._pointerEnd)}else window.removeEventListener("mousemove",this._move),window.removeEventListener("mouseup",this._pointerEnd)},this._touchEnd=e=>{for(const t of Array.from(e.changedTouches))this._triggerPointerEnd(new _t(t),e)},this._startCallback=t,this._moveCallback=i,this._endCallback=s,this._rawUpdates=n&&"onpointerrawupdate"in window,self.PointerEvent&&!o?this._element.addEventListener("pointerdown",this._pointerStart):(this._element.addEventListener("mousedown",this._pointerStart),this._element.addEventListener("touchstart",this._touchStart),this._element.addEventListener("touchmove",this._move),this._element.addEventListener("touchend",this._touchEnd),this._element.addEventListener("touchcancel",this._touchEnd))}stop(){this._element.removeEventListener("pointerdown",this._pointerStart),this._element.removeEventListener("mousedown",this._pointerStart),this._element.removeEventListener("touchstart",this._touchStart),this._element.removeEventListener("touchmove",this._move),this._element.removeEventListener("touchend",this._touchEnd),this._element.removeEventListener("touchcancel",this._touchEnd),this._element.removeEventListener(this._rawUpdates?"pointerrawupdate":"pointermove",this._move),this._element.removeEventListener("pointerup",this._pointerEnd),this._element.removeEventListener("pointercancel",this._pointerEnd),window.removeEventListener("mousemove",this._move),window.removeEventListener("mouseup",this._pointerEnd)}_triggerPointerStart(e,t){return!!this._startCallback(e,t)&&(this.currentPointers.push(e),this.startPointers.push(e),!0)}}function wt(e,t){return t?Math.sqrt((t.clientX-e.clientX)**2+(t.clientY-e.clientY)**2):0}function At(e,t){return t?{clientX:(e.clientX+t.clientX)/2,clientY:(e.clientY+t.clientY)/2}:e}function xt(e,t){return"number"==typeof e?e:e.trimRight().endsWith("%")?t*parseFloat(e)/100:parseFloat(e)}let yt;function Ct(){return yt||(yt=document.createElementNS("http://www.w3.org/2000/svg","svg"))}function kt(){return Ct().createSVGMatrix()}function St(){return Ct().createSVGPoint()}i.appendCss("pinch-zoom{display:block;overflow:hidden;touch-action:none;--scale:1;--x:0;--y:0}pinch-zoom>*{transform:translate(var(--x),var(--y)) scale(var(--scale));transform-origin:0 0;will-change:transform}");class zt extends HTMLElement{constructor(){super(),this._transform=kt(),new MutationObserver((()=>this._stageElChange())).observe(this,{childList:!0});const e=new bt(this,{start:(t,i)=>!(2===e.currentPointers.length||!this._positioningEl)&&(i.preventDefault(),!0),move:t=>{this._onPointerMove(t,e.currentPointers)},avoidPointerEvents:C});this.addEventListener("wheel",(e=>this._onWheel(e)))}connectedCallback(){this._stageElChange()}get x(){return this._transform.e}get y(){return this._transform.f}get scale(){return this._transform.a}scaleTo(e,t={}){let{originX:i=0,originY:s=0}=t;const{relativeTo:n="content",allowChangeEvent:o=!1}=t,a="content"===n?this._positioningEl:this;if(!a||!this._positioningEl)return void this.setTransform({scale:e,allowChangeEvent:o});const r=a.getBoundingClientRect();if(i=xt(i,r.width),s=xt(s,r.height),"content"===n)i+=this.x,s+=this.y;else{const e=this._positioningEl.getBoundingClientRect();i-=e.left,s-=e.top}this._applyChange({allowChangeEvent:o,originX:i,originY:s,scaleDiff:e/this.scale})}setTransform(e={}){const{scale:t=this.scale,allowChangeEvent:i=!1}=e;let{x:s=this.x,y:n=this.y}=e;if(!this._positioningEl)return void this._updateTransform(t,s,n,i);const o=this.getBoundingClientRect(),a=this._positioningEl.getBoundingClientRect();if(!o.width||!o.height)return void this._updateTransform(t,s,n,i);let r=St();r.x=a.left-o.left,r.y=a.top-o.top;let l=St();l.x=a.width+r.x,l.y=a.height+r.y;const h=kt().translate(s,n).scale(t).multiply(this._transform.inverse());r=r.matrixTransform(h),l=l.matrixTransform(h),r.x>o.width?s+=o.width-r.x:l.x<0&&(s+=-l.x),r.y>o.height?n+=o.height-r.y:l.y<0&&(n+=-l.y),this._updateTransform(t,s,n,i)}_updateTransform(e,t,i,s){if(!(e<.01)&&!(e>1e5)&&(e!==this.scale||t!==this.x||i!==this.y)&&(this._transform.e=t,this._transform.f=i,this._transform.d=this._transform.a=e,this.style.setProperty("--x",this.x+"px"),this.style.setProperty("--y",this.y+"px"),this.style.setProperty("--scale",this.scale+""),s)){const e=new Event("change",{bubbles:!0});this.dispatchEvent(e)}}_stageElChange(){this._positioningEl=void 0,0!==this.children.length&&(this._positioningEl=this.children[0],this.children.length>1&&console.warn("<pinch-zoom> must not have more than one child."),this.setTransform({allowChangeEvent:!0}))}_onWheel(e){if(!this._positioningEl)return;e.preventDefault();const t=this._positioningEl.getBoundingClientRect();let{deltaY:i}=e;const{ctrlKey:s,deltaMode:n}=e;1===n&&(i*=15);const o=i>0,a=1-(o?-i:i)/(s?100:300),r=o?1/a:a;this._applyChange({scaleDiff:r,originX:e.clientX-t.left,originY:e.clientY-t.top,allowChangeEvent:!0})}_onPointerMove(e,t){if(!this._positioningEl)return;const i=this._positioningEl.getBoundingClientRect(),s=At(e[0],e[1]),n=At(t[0],t[1]),o=s.clientX-i.left,a=s.clientY-i.top,r=wt(e[0],e[1]),l=wt(t[0],t[1]),h=r?l/r:1;this._applyChange({originX:o,originY:a,scaleDiff:h,panX:n.clientX-s.clientX,panY:n.clientY-s.clientY,allowChangeEvent:!0})}_applyChange(e={}){const{panX:t=0,panY:i=0,originX:s=0,originY:n=0,scaleDiff:o=1,allowChangeEvent:a=!1}=e,r=kt().translate(t,i).translate(s,n).translate(this.x,this.y).scale(o).translate(-s,-n).scale(this.scale);this.setTransform({allowChangeEvent:a,scale:r.a,x:r.e,y:r.f})}}customElements.define("pinch-zoom",zt);const Et="_two-up-handle_mdt25_18";i.appendCss('two-up{display:grid;position:relative;--split-point:0;--track-color:rgb(0 0 0/0.6);--thumb-background:var(--black);--thumb-color:var(--accent-color);--thumb-size:62px;--bar-size:9px;--bar-touch-size:30px}two-up>*{grid-area:1/1}two-up[legacy-clip-compat]>:not(._two-up-handle_mdt25_18){position:absolute}._two-up-handle_mdt25_18{touch-action:none;position:relative;width:var(--bar-touch-size);transform:translateX(var(--split-point)) translateX(-50%);will-change:transform;cursor:ew-resize}._two-up-handle_mdt25_18:before{content:"";display:block;height:100%;width:var(--bar-size);margin:0 auto;background:var(--track-color)}._scrubber_mdt25_42{display:grid;align-content:center;position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);width:var(--thumb-size);height:var(--thumb-size);background:var(--thumb-background);border-radius:var(--thumb-size);color:var(--thumb-color);box-sizing:border-box;padding:0 calc(var(--thumb-size)*0.24)}._arrow-left_mdt25_58{fill:var(--pink)}._arrow-right_mdt25_62{fill:var(--blue)}two-up[orientation=vertical] ._two-up-handle_mdt25_18{width:auto;height:var(--bar-touch-size);transform:translateY(var(--split-point)) translateY(-50%);cursor:ns-resize}two-up[orientation=vertical] ._two-up-handle_mdt25_18:before{width:auto;height:var(--bar-size);box-shadow:inset 0 calc(var(--bar-size)/2) 0 rgba(0,0,0,.1),0 1px 4px rgba(0,0,0,.4);margin:calc((var(--bar-touch-size) - var(--bar-size))/2) 0 0 0}two-up[orientation=vertical] ._scrubber_mdt25_42{box-shadow:1px 0 4px rgba(0,0,0,.1);transform:translate(-50%,-50%) rotate(-90deg)}two-up>:first-child:not(._two-up-handle_mdt25_18){-webkit-clip-path:inset(0 calc(100% - var(--split-point)) 0 0);clip-path:inset(0 calc(100% - var(--split-point)) 0 0)}two-up>:nth-child(2):not(._two-up-handle_mdt25_18){-webkit-clip-path:inset(0 0 0 var(--split-point));clip-path:inset(0 0 0 var(--split-point))}two-up[orientation=vertical]>:first-child:not(._two-up-handle_mdt25_18){-webkit-clip-path:inset(0 0 calc(100% - var(--split-point)) 0);clip-path:inset(0 0 calc(100% - var(--split-point)) 0)}two-up[orientation=vertical]>:nth-child(2):not(._two-up-handle_mdt25_18){-webkit-clip-path:inset(var(--split-point) 0 0 0);clip-path:inset(var(--split-point) 0 0 0)}@supports not ((clip-path:inset(0 0 0 0)) or (-webkit-clip-path:inset(0 0 0 0))){two-up[legacy-clip-compat]>:first-child:not(._two-up-handle_mdt25_18){clip:rect(auto var(--split-point) auto auto)}two-up[legacy-clip-compat]>:nth-child(2):not(._two-up-handle_mdt25_18){clip:rect(auto auto auto var(--split-point))}two-up[orientation=vertical][legacy-clip-compat]>:first-child:not(._two-up-handle_mdt25_18){clip:rect(auto auto var(--split-point) auto)}two-up[orientation=vertical][legacy-clip-compat]>:nth-child(2):not(._two-up-handle_mdt25_18){clip:rect(var(--split-point) auto auto auto)}}');const It="legacy-clip-compat",Pt="orientation";class qt extends HTMLElement{constructor(){super(),this._handle=document.createElement("div"),this._position=0,this._relativePosition=.5,this._positionOnPointerStart=0,this._everConnected=!1,this._onKeyDown=e=>{const t=e.target;if(!(t instanceof HTMLElement&&t.closest("input")))if("Digit1"===e.code||"Numpad1"===e.code)this._position=0,this._relativePosition=0,this._setPosition();else if("Digit2"===e.code||"Numpad2"===e.code){const e="vertical"===this.orientation?"height":"width",t=this.getBoundingClientRect();this._position=t[e]/2,this._relativePosition=this._position/t[e]/2,this._setPosition()}else if("Digit3"===e.code||"Numpad3"===e.code){const e="vertical"===this.orientation?"height":"width",t=this.getBoundingClientRect();this._position=t[e],this._relativePosition=this._position/t[e],this._setPosition()}},this._handle.className=Et,new MutationObserver((()=>this._childrenChange())).observe(this,{childList:!0});const e=new bt(this._handle,{start:(t,i)=>1!==e.currentPointers.length&&(i.preventDefault(),this._positionOnPointerStart=this._position,!0),move:()=>{this._pointerChange(e.startPointers[0],e.currentPointers[0])}})}static get observedAttributes(){return[Pt]}connectedCallback(){this._childrenChange(),this._handle.innerHTML='<div class="_scrubber_mdt25_42"><svg viewBox="0 0 27 20"><path class="_arrow-left_mdt25_58" d="M9.6 0L0 9.6l9.6 9.6z"/><path class="_arrow-right_mdt25_62" d="M17 19.2l9.5-9.6L16.9 0z"/></svg>\n      </div>',this._resizeObserver=new ResizeObserver((()=>this._resetPosition())),this._resizeObserver.observe(this),window.addEventListener("keydown",this._onKeyDown),this._everConnected||(this._resetPosition(),this._everConnected=!0)}disconnectedCallback(){window.removeEventListener("keydown",this._onKeyDown),this._resizeObserver&&this._resizeObserver.disconnect()}attributeChangedCallback(e){e===Pt&&this._resetPosition()}_resetPosition(){requestAnimationFrame((()=>{const e=this.getBoundingClientRect(),t="vertical"===this.orientation?"height":"width";this._position=e[t]*this._relativePosition,this._setPosition()}))}get legacyClipCompat(){return this.hasAttribute(It)}set legacyClipCompat(e){e?this.setAttribute(It,""):this.removeAttribute(It)}get orientation(){const e=this.getAttribute(Pt);return e&&"vertical"===e.toLowerCase()?"vertical":"horizontal"}set orientation(e){this.setAttribute(Pt,e)}_childrenChange(){this.lastElementChild!==this._handle&&this.appendChild(this._handle)}_pointerChange(e,t){const i="vertical"===this.orientation?"clientY":"clientX",s="vertical"===this.orientation?"height":"width",n=this.getBoundingClientRect();this._position=this._positionOnPointerStart+(t[i]-e[i]),this._position=Math.max(0,Math.min(this._position,n[s])),this._relativePosition=this._position/n[s],this._setPosition()}_setPosition(){this.style.setProperty("--split-point",`${this._position}px`)}}customElements.define("two-up",qt);const Lt="_pinch-zoom_ljgnb_25 abs-fill",Mt="_pinch-target_ljgnb_33",Ot="_button-group_ljgnb_68",Bt="_zoom_ljgnb_75",jt="_first-button_ljgnb_110 _button_ljgnb_68",Rt="_last-button_ljgnb_115 _button_ljgnb_68",Tt="_pixelated_ljgnb_165",Dt={originX:"50%",originY:"50%",relativeTo:"container",allowChangeEvent:!0};class Ft extends i.d{constructor(){super(...arguments),this.state={scale:1,editingScale:!1,altBackground:!1,aliasing:!1},this.retargetedEvents=new WeakSet,this.toggleAliasing=()=>{this.setState((e=>({aliasing:!e.aliasing})))},this.toggleBackground=()=>{this.setState({altBackground:!this.state.altBackground})},this.zoomIn=()=>{if(!this.pinchZoomLeft)throw Error("Missing pinch-zoom element");this.pinchZoomLeft.scaleTo(1.25*this.state.scale,Dt)},this.zoomOut=()=>{if(!this.pinchZoomLeft)throw Error("Missing pinch-zoom element");this.pinchZoomLeft.scaleTo(this.state.scale/1.25,Dt)},this.onRotateClick=()=>{const{preprocessorState:e}=this.props;if(!e)return;const t=et(e,"rotate.rotate",(e.rotate.rotate+90)%360);this.props.onPreprocessorChange(t)},this.onScaleValueFocus=()=>{this.setState({editingScale:!0},(()=>{this.scaleInput&&(getComputedStyle(this.scaleInput).transform,this.scaleInput.focus())}))},this.onScaleInputBlur=()=>{this.setState({editingScale:!1})},this.onScaleInputChanged=e=>{const t=e.target,i=parseFloat(t.value);if(!isNaN(i)){if(!this.pinchZoomLeft)throw Error("Missing pinch-zoom element");this.pinchZoomLeft.scaleTo(i/100,Dt)}},this.onPinchZoomLeftChange=e=>{if(!this.pinchZoomRight||!this.pinchZoomLeft)throw Error("Missing pinch-zoom element");this.setState({scale:this.pinchZoomLeft.scale}),this.pinchZoomRight.setTransform({scale:this.pinchZoomLeft.scale,x:this.pinchZoomLeft.x,y:this.pinchZoomLeft.y})},this.onRetargetableEvent=e=>{const t=e.target;if(!this.pinchZoomLeft)throw Error("Missing pinch-zoom element");if("wheel"!==e.type&&t.closest(`.${Et}`))return;if(this.retargetedEvents.has(e))return;e.stopImmediatePropagation(),e.preventDefault();const i=new e.constructor(e.type,e);this.retargetedEvents.add(i),this.pinchZoomLeft.dispatchEvent(i),"touchend"===e.type&&document.activeElement&&document.activeElement instanceof HTMLElement&&document.activeElement.blur()}}componentDidMount(){const e=this.leftDrawable(),t=this.rightDrawable();this.pinchZoomLeft.setTransform({allowChangeEvent:!0,x:0,y:0,scale:1}),this.canvasLeft&&e&&A(this.canvasLeft,e),this.canvasRight&&t&&A(this.canvasRight,t)}componentDidUpdate(e,t){const i=this.leftDrawable(e),s=this.rightDrawable(e),n=this.leftDrawable(),o=this.rightDrawable(),a=!!this.props.source!=!!e.source||this.props.source&&e.source&&this.props.source.file!==e.source.file,r=e.source&&e.source.preprocessed,l=this.props.source&&this.props.source.preprocessed,h=this.pinchZoomLeft;if(a)h.setTransform({allowChangeEvent:!0,x:0,y:0,scale:1});else if(r&&l&&r!==l){const e=1-h.scale,t=r.width/2*e,i=r.height/2*e;h.setTransform({allowChangeEvent:!0,x:h.x-t+i,y:h.y-i+t})}n&&n!==i&&this.canvasLeft&&A(this.canvasLeft,n),o&&o!==s&&this.canvasRight&&A(this.canvasRight,o)}shouldComponentUpdate(e,t){return!k(this.props,e)||!k(this.state,t)}leftDrawable(e=this.props){return e.leftCompressed||e.source&&e.source.preprocessed}rightDrawable(e=this.props){return e.rightCompressed||e.source&&e.source.preprocessed}render({mobileView:e,leftImgContain:t,rightImgContain:s,source:n},{scale:o,editingScale:u,altBackground:g,aliasing:_}){const m=this.leftDrawable(),f=this.rightDrawable(),v=n&&n.preprocessed;return i.h(i.p,null,i.h("div",{class:"_output_ljgnb_1 "+(g?"_alt-background_ljgnb_16":"")},i.h("two-up",{"legacy-clip-compat":!0,class:"_two-up_ljgnb_21 abs-fill",orientation:e?"vertical":"horizontal",onTouchStartCapture:this.onRetargetableEvent,onTouchEndCapture:this.onRetargetableEvent,onTouchMoveCapture:this.onRetargetableEvent,onPointerDownCapture:C?void 0:this.onRetargetableEvent,onMouseDownCapture:this.onRetargetableEvent,onWheelCapture:this.onRetargetableEvent},i.h("pinch-zoom",{class:Lt,onChange:this.onPinchZoomLeftChange,ref:i.linkRef(this,"pinchZoomLeft")},i.h("canvas",{class:`${Mt} ${_?Tt:""}`,ref:i.linkRef(this,"canvasLeft"),width:m&&m.width,height:m&&m.height,style:{width:v?v.width:"",height:v?v.height:"",objectFit:t?"contain":""}})),i.h("pinch-zoom",{class:Lt,ref:i.linkRef(this,"pinchZoomRight")},i.h("canvas",{class:`${Mt} ${_?Tt:""}`,ref:i.linkRef(this,"canvasRight"),width:f&&f.width,height:f&&f.height,style:{width:v?v.width:"",height:v?v.height:"",objectFit:s?"contain":""}})))),i.h("div",{class:"_controls_ljgnb_42"},i.h("div",{class:Ot},i.h("button",{class:jt,onClick:this.zoomOut},i.h(d,null)),u?i.h("input",{type:"number",step:"1",min:"1",max:"1000000",ref:i.linkRef(this,"scaleInput"),class:Bt,value:Math.round(100*o),onInput:this.onScaleInputChanged,onBlur:this.onScaleInputBlur}):i.h("span",{class:Bt,tabIndex:0,onFocus:this.onScaleValueFocus},i.h("span",{class:"_zoom-value_ljgnb_146"},Math.round(100*o)),"%"),i.h("button",{class:Rt,onClick:this.zoomIn},i.h(p,null))),this.props.children&&i.h("div",{class:Ot},this.props.children),i.h("div",{class:Ot},i.h("button",{class:jt,onClick:this.onRotateClick,title:"Rotate"},i.h(c,null)),!C&&i.h("button",{class:"_button_ljgnb_68",onClick:this.toggleAliasing,title:"Toggle smoothing"},_?i.h(r,null):i.h(a,null)),i.h("button",{class:Rt,onClick:this.toggleBackground,title:"Toggle background"},g?i.h(h,null):i.h(l,null)))))}}class Ut{constructor(){this._entries=[]}add(e){this._entries.unshift(e),this._entries.length>5&&this._entries.pop()}match(e,t,i){const s=this._entries.findIndex((s=>{if(s.preprocessed!==e)return!1;if(s.encoderState.type!==i.type)return!1;for(const e in t)if(!k(t[e],s.processorState[e]))return!1;return!!k(i.options,s.encoderState.options)}));if(-1===s)return;const n=this._entries[s];return 0!==s&&(this._entries.splice(s,1),this._entries.unshift(n)),{...n}}}i.appendCss("@font-face{font-family:Roboto Mono Numbers;font-style:normal;font-weight:700;src:url(\"data:font/woff;base64,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\") format(\"woff\")}@keyframes _action-enter_17s86_1{0%{transform:rotate(-90deg);opacity:0;animation-timing-function:ease-out}}@keyframes _action-leave_17s86_1{0%{transform:rotate(0deg);opacity:1;animation-timing-function:ease-out}}._results_17s86_26{--download-overflow-size:9px;background:rgba(0,0,0,.67);border-radius:5px;display:grid;grid-template-columns:max-content [bubble] 1fr [download] max-content}@media (min-width:600px){._results_17s86_26{--download-overflow-size:30px;background:none;border-radius:none;grid-template-columns:[download] auto [bubble] 1fr;align-items:center;margin-bottom:calc(var(--download-overflow-size)/2)}}._expand-arrow_17s86_43{fill:var(--white);transform:rotate(180deg);margin:0 1rem;align-self:center}@media (min-width:600px){._expand-arrow_17s86_43{display:none}}:focus ._expand-arrow_17s86_43{fill:var(--main-theme-color)}[content-expanded] ._expand-arrow_17s86_43{transform:none}._expand-arrow_17s86_43 svg{display:block;--size:15px;width:var(--size);height:var(--size)}._bubble_17s86_72{align-self:center}@media (min-width:600px){._bubble_17s86_72{position:relative;width:max-content;grid-row:1;grid-column:bubble}._bubble_17s86_72:before{content:\"\";position:absolute;top:0;left:0;right:0;bottom:0;border-image-source:url(\"data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='186.5' height='280.3'%3E%3Cpath fill='rgba(30,31,29,0.69)' d='M181.5 0H16.4a5 5 0 00-5 5v134L0 146.5h11.4v128.8a5 5 0 005 5h165.1a5 5 0 005-5V5a5 5 0 00-5-5z'/%3E%3Cpath fill='rgba(0,0,0,0.23)' d='M16.4 1a4 4 0 00-4 4v134.5l-.5.3-8.6 5.7h9v129.8a4 4 0 004 4h165.2a4 4 0 004-4V5a4 4 0 00-4-4H16.4m0-1h165.1a5 5 0 015 5v270.3a5 5 0 01-5 5H16.4a5 5 0 01-5-5V146.5H0l11.4-7.5V5a5 5 0 015-5z'/%3E%3C/svg%3E\");border-image-slice:12 12 12 17 fill;border-image-width:12px 12px 12px 17px;border-image-repeat:repeat}}._bubble-inner_17s86_96{display:grid;grid-template-columns:[size-info] 1fr [percent-info] auto}@media (min-width:600px){._bubble-inner_17s86_96{position:relative;--main-padding:1px;--speech-padding:2.1rem;padding:var(--main-padding) var(--main-padding) var(--main-padding) var(--speech-padding);gap:.9rem}}._unit_17s86_110{color:var(--main-theme-color)}@media (min-width:600px){._type-label_17s86_114{display:none}}._size-info_17s86_120{background:var(--dark-gray);border-radius:19px;align-self:center;grid-column:size-info;grid-row:1;justify-self:start;padding:.6rem 1.2rem;margin:.4rem 0}@media (min-width:600px){._size-info_17s86_120{border-radius:none;background:none;padding:0;margin:0}}._percent-info_17s86_139{align-self:center;margin-left:1rem;margin-right:.3rem}@media (min-width:600px){._percent-info_17s86_139{margin:0;display:grid;--arrow-width:16px;grid-template-columns:[arrow] var(--arrow-width) [data] auto;grid-column:percent-info;grid-row:1;--shadow-direction:-1px;filter:drop-shadow(var(--shadow-direction) 0 0 rgba(0,0,0,.67))}}._big-arrow_17s86_156{display:none}@media (min-width:600px){._big-arrow_17s86_156{display:block;width:100%;fill:var(--main-theme-color);grid-column:arrow;grid-row:1;align-self:stretch}}._percent-output_17s86_169{grid-column:data;grid-row:1;display:grid;grid-template-columns:auto auto auto;line-height:1}@media (min-width:600px){._percent-output_17s86_169{background:var(--main-theme-color);--radius:4px;border-radius:0 var(--radius) var(--radius) 0;--padding-arrow-side:0.6rem;--padding-other-side:1.1rem;padding:.7rem var(--padding-other-side);padding-left:var(--padding-arrow-side)}}._size-direction_17s86_187{font-weight:700;align-self:center;font-family:sans-serif;opacity:.76;text-shadow:0 2px rgba(0,0,0,.3);font-size:1.5rem;position:relative;top:3px}._size-value_17s86_198{font-family:Roboto Mono Numbers;font-size:2.6rem;text-shadow:0 2px rgba(0,0,0,.3)}._percent-char_17s86_204{align-self:start;position:relative;top:4px;opacity:.76;margin-left:.2rem}._download_17s86_212{--size:59px;width:calc(var(--size) + var(--download-overflow-size));height:calc(var(--size) + var(--download-overflow-size));position:relative;grid-row:1;grid-column:download;margin:calc(var(--download-overflow-size)/-2) 0;margin-right:calc(var(--download-overflow-size)/-3);display:grid;align-items:center;justify-items:center;align-self:center}@media (min-width:600px){._download_17s86_212{--size:63px}}._download_17s86_212 loading-spinner{grid-area:1/1;position:relative;--color:var(--white);--size:21px;top:0;left:1px}@media (min-width:600px){._download_17s86_212 loading-spinner{top:-1px;left:2px;--size:28px}}._download-blobs_17s86_246{position:absolute;top:0;left:0;width:100%;height:100%}._download-blobs_17s86_246 path{fill:var(--hot-theme-color);opacity:.7}._download-icon_17s86_258{grid-area:1/1}._download-icon_17s86_258 svg{--size:19px;width:var(--size);height:var(--size);fill:var(--white);position:relative;top:3px;left:1px;animation:_action-enter_17s86_1 .2s}@media (min-width:600px){._download-icon_17s86_258 svg{--size:27px;top:2px;left:2px}}._download-disable_17s86_279{pointer-events:none}._download-disable_17s86_279 ._download-icon_17s86_258 svg{opacity:0;transform:rotate(90deg);animation:_action-leave_17s86_1 .2s}@media (min-width:600px){._results-right_17s86_295{grid-template-columns:[bubble] 1fr [download] auto}}@media (min-width:600px){._results-right_17s86_295 ._bubble_17s86_72{justify-self:end}._results-right_17s86_295 ._bubble_17s86_72:before{transform:scaleX(-1)}}._results-right_17s86_295 ._download_17s86_212{margin-left:calc(var(--download-overflow-size)/-3);margin-right:0}@media (min-width:600px){._results-right_17s86_295 ._bubble-inner_17s86_96{padding:var(--main-padding) var(--speech-padding) var(--main-padding) var(--main-padding);grid-template-columns:[percent-info] auto [size-info] 1fr}}@media (min-width:600px){._results-right_17s86_295 ._percent-info_17s86_139{grid-template-columns:[data] auto [arrow] var(--arrow-width);--shadow-direction:1px}}@media (min-width:600px){._results-right_17s86_295 ._percent-output_17s86_169{border-radius:var(--radius) 0 0 var(--radius);padding-left:var(--padding-other-side);padding-right:var(--padding-arrow-side)}}._results-right_17s86_295 ._big-arrow_17s86_156{transform:scaleX(-1)}._is-original_17s86_345 ._big-arrow_17s86_156{fill:transparent}._is-original_17s86_345 ._percent-output_17s86_169{background:none}._is-original_17s86_345 ._download-blobs_17s86_246 path{fill:var(--black)}._is-original_17s86_345 ._unit_17s86_110{color:var(--white);opacity:.76}");const Ht=["B","kB","MB","GB","TB","PB","EB","ZB","YB"];class Vt extends i.d{constructor(){super(...arguments),this.state={showLoadingState:this.props.loading},this.loadingTimeoutId=0,this.onDownload=()=>{const e=Math.round(this.props.source.file.size/1024),t=Math.round(this.props.imageFile.size/1024),i=Math.round(t/e*1e3);ga("send","event","compression","download",{metric1:e,metric2:t,metric3:i})}}componentDidUpdate(e,t){e.loading&&!this.props.loading?(clearTimeout(this.loadingTimeoutId),this.setState({showLoadingState:!1})):!e.loading&&this.props.loading&&(this.loadingTimeoutId=self.setTimeout((()=>this.setState({showLoadingState:!0})),500))}render({source:e,imageFile:t,downloadUrl:s,flipSide:n,typeLabel:o},{showLoadingState:a}){const r=t&&function(e){const t=e<0,i=t?"-":"";if(t&&(e=-e),e<1)return{value:i+e,unit:Ht[0]};const s=Math.min(Math.floor(Math.log10(e)/3),Ht.length-1);return{unit:Ht[s],value:i+(e/Math.pow(1e3,s)).toPrecision(3)}}(t.size),l=!e||!t||e.file===t;let h,c;if(e&&t){h=t.size/e.file.size;const i=Math.round(100*Math.abs(h));c=h>1?i-100:100-i}return i.h("div",{class:(n?"_results-right_17s86_295 _results_17s86_26":"_results-left_17s86_291 _results_17s86_26")+" "+(l?"_is-original_17s86_345":"")},i.h("div",{class:"_expand-arrow_17s86_43"},i.h(_,null)),i.h("div",{class:"_bubble_17s86_72"},i.h("div",{class:"_bubble-inner_17s86_96"},i.h("div",{class:"_size-info_17s86_120"},i.h("div",{class:"_file-size_17s86_69"},r?i.h(i.p,null,r.value," ",i.h("span",{class:"_unit_17s86_110"},r.unit),i.h("span",{class:"_type-label_17s86_114"}," ",o)):"…")),i.h("div",{class:"_percent-info_17s86_139"},i.h("svg",{viewBox:"0 0 1 2",class:"_big-arrow_17s86_156",preserveAspectRatio:"none"},i.h("path",{d:"M1 0v2L0 1z"})),i.h("div",{class:"_percent-output_17s86_169"},h&&1!==h&&i.h("span",{class:"_size-direction_17s86_187"},h<1?"↓":"↑"),i.h("span",{class:"_size-value_17s86_198"},c||0),i.h("span",{class:"_percent-char_17s86_204"},"%"))))),i.h("a",{class:a?"_download-disable_17s86_279 _download_17s86_212":"_download_17s86_212",href:s,download:t?t.name:"",title:"Download All",onClick:this.onDownload},i.h("svg",{class:"_download-blobs_17s86_246",viewBox:"0 0 89.6 86.9"},i.h("title",null,"Download All"),i.h("path",{d:"M27.3 72c-8-4-15.6-12.3-16.9-21-1.2-8.7 4-17.8 10.5-26s14.4-15.6 24-16 21.2 6 28.6 16.5c7.4 10.5 10.8 25 6.6 34S64.1 71.8 54 73.6c-10.2 2-18.7 2.3-26.7-1.6z"}),i.h("path",{d:"M19.8 24.8c4.3-7.8 13-15 21.8-15.7 8.7-.8 17.5 4.8 25.4 11.8 7.8 6.9 14.8 15.2 14.7 24.9s-7.1 20.7-18 27.6c-10.8 6.8-25.5 9.5-34.2 4.8S18.1 61.6 16.7 51.4c-1.3-10.3-1.3-18.8 3-26.6z"})),i.h("div",{class:"_download-icon_17s86_258"},i.h(m,null)),a&&i.h("loading-spinner",null)))}}async function Nt(e,t,i,s,n){j(e);const o=We[i.type],a=await o.encode(e,n,t,i.options),r=o.meta.mimeType;return new File([a],s.replace(/.[^.]*$/,`.${o.meta.extension}`),{type:r})}async function Yt(e,t){j(e);const i=new DOMParser,s=await R(e,function(e){return new Response(e).text()}(t)),n=i.parseFromString(s,"image/svg+xml"),o=n.documentElement;if(o.hasAttribute("width")&&o.hasAttribute("height"))return I(t);const a=o.getAttribute("viewBox");if(null===a)throw Error("SVG must have width/height or viewBox");const r=a.split(/\s+/);o.setAttribute("width",r[2]),o.setAttribute("height",r[3]);const l=(new XMLSerializer).serializeToString(n);return R(e,I(new Blob([l],{type:"image/svg+xml"})))}async function Qt(e,t,i){j(e);const s=await R(e,E(t)),n=await R(e,function(e){if(!S.has(e)){const t=(async()=>{const t=document.createElement("picture"),i=document.createElement("img"),s=document.createElement("source");return s.srcset="data:,x",s.type=e,t.append(s,i),await 0,!!i.currentSrc})();S.set(e,t)}return S.get(e)}(s));try{if(!n){if("image/avif"===s)return await i.avifDecode(e,t);if("image/webp"===s)return await i.webpDecode(e,t);if("image/jxl"===s)return await i.jxlDecode(e,t);if("image/webp2"===s)return await i.wp2Decode(e,t);if("image/qoi"===s)return await i.qoiDecode(e,t)}return await async function(e,t){return j(e),y(await R(e,"createImageBitmap"in self?createImageBitmap(t):I(t)))}(e,t)}catch(e){if(e instanceof Error&&"AbortError"===e.name)throw e;throw console.log(e),Error("Couldn't decode image")}}async function Gt(e,t,i){if(j(e),t.type.startsWith("image/svg+xml")){const i=await Yt(e,t);return{decoded:y(i),vectorImage:i}}return{decoded:await Qt(e,t,i)}}async function Wt(e,t,i,s){j(e);let n=t;return 0!==i.rotate.rotate&&(n=await s.rotate(e,n,i.rotate)),n}const Jt="_options-1-theme_cfw4k_91",Xt="_options-2-theme_cfw4k_104";async function Zt(e,t,i,s){j(e);let n=t.preprocessed;return i.resize.enabled&&(n=await te(e,t,i.resize,s)),i.quantize.enabled&&(n=await s.quantize(e,n,i.quantize)),n}const Kt=document.title;function $t(e){const{loading:t,filename:i}=e;let s="";t&&(s+="⏳ "),i&&(s+=i+" - "),s+=Kt,document.title=s}class ei extends i.d{constructor(t){super(t),this.widthQuery=window.matchMedia("(max-width: 599px)"),this.state={source:void 0,loading:!1,preprocessorState:Xe,sides:[localStorage.getItem("leftSideSettings")?{...JSON.parse(localStorage.getItem("leftSideSettings")),loading:!1}:{latestSettings:{processorState:Je,encoderState:void 0},loading:!1},localStorage.getItem("rightSideSettings")?{...JSON.parse(localStorage.getItem("rightSideSettings")),loading:!1}:{latestSettings:{processorState:Je,encoderState:{type:"mozJPEG",options:We.mozJPEG.meta.defaultOptions}},loading:!1}],mobileView:this.widthQuery.matches},this.encodeCache=new Ut,this.workerBridges=[new st,new st],this.mainAbortController=new AbortController,this.sideAbortControllers=[new AbortController,new AbortController],this.onMobileWidthChange=()=>{this.setState({mobileView:this.widthQuery.matches})},this.onEncoderTypeChange=(e,t)=>{this.setState({sides:et(this.state.sides,`${e}.latestSettings.encoderState`,"identity"===t?void 0:{type:t,options:We[t].meta.defaultOptions})})},this.onProcessorOptionsChange=(e,t)=>{this.setState({sides:et(this.state.sides,`${e}.latestSettings.processorState`,t)})},this.onEncoderOptionsChange=(e,t)=>{this.setState({sides:et(this.state.sides,`${e}.latestSettings.encoderState.options`,t)})},this.onCopyToOtherClick=async e=>{const t=e?0:1,i=this.state.sides[t],s={...this.state.sides[e]};s.file&&(s.downloadUrl=URL.createObjectURL(s.file)),this.setState({sides:et(this.state.sides,t,s)});"undo"===await this.props.showSnack("Settings copied across",{timeout:5e3,actions:["undo","dismiss"]})&&this.setState({sides:et(this.state.sides,t,i)})},this.onSaveSideSettingsClick=async e=>{if(0===e){const t=JSON.stringify({encodedSettings:this.state.sides[e].encodedSettings,latestSettings:this.state.sides[e].latestSettings});return localStorage.setItem("leftSideSettings",t),window.dispatchEvent(new CustomEvent("leftSideSettings")),void await this.props.showSnack("Left side settings saved",{timeout:1500,actions:["dismiss"]})}if(1===e){const t=JSON.stringify({encodedSettings:this.state.sides[e].encodedSettings,latestSettings:this.state.sides[e].latestSettings});return localStorage.setItem("rightSideSettings",t),window.dispatchEvent(new CustomEvent("rightSideSettings")),void await this.props.showSnack("Right side settings saved",{timeout:1500,actions:["dismiss"]})}},this.onImportSideSettingsClick=async e=>{const t=localStorage.getItem("leftSideSettings"),i=localStorage.getItem("rightSideSettings");if(0===e&&t){const i=this.state.sides[e],s={...this.state.sides[e],...JSON.parse(t)};this.setState({sides:et(this.state.sides,e,s)});"undo"===await this.props.showSnack("Left side settings imported",{timeout:3e3,actions:["undo","dismiss"]})&&this.setState({sides:et(this.state.sides,e,i)})}else if(1===e&&i){const t=this.state.sides[e],s={...this.state.sides[e],...JSON.parse(i)};this.setState({sides:et(this.state.sides,e,s)});"undo"===await this.props.showSnack("Right side settings imported",{timeout:3e3,actions:["undo","dismiss"]})&&this.setState({sides:et(this.state.sides,e,t)})}else;},this.onPreprocessorChange=async e=>{if(!this.state.source)return;const t=this.state.preprocessorState.rotate.rotate%180!=e.rotate.rotate%180;this.setState((i=>({loading:!0,preprocessorState:e,sides:t?i.sides.map((e=>{const t=e.latestSettings.processorState.resize;return $e(e,"latestSettings.processorState.resize",{width:t.height,height:t.width})})):i.sides})))},this.activeSideJobs=[void 0,void 0],this.widthQuery.addListener(this.onMobileWidthChange),this.sourceFile=t.files[0],this.files=t.files,this.queuePreviewedImageUpdate({immediate:!0}),e("./sw-bridge-00e498e8").then((({mainAppLoaded:e})=>e()))}componentWillReceiveProps(e){e.files!==this.props.files&&(this.files=e.files,this.sourceFile=this.files[0],this.queuePreviewedImageUpdate({immediate:!0}))}componentWillUnmount(){$t({loading:!1}),this.widthQuery.removeListener(this.onMobileWidthChange),this.mainAbortController.abort();for(const e of this.sideAbortControllers)e.abort()}componentDidUpdate(e,t){var i;const s=t.loading||t.sides[0].loading||t.sides[1].loading,n=this.state.loading||this.state.sides[0].loading||this.state.sides[1].loading,o=t.source!==this.state.source;(s!==n||o)&&$t({loading:n,filename:null===(i=this.state.source)||void 0===i?void 0:i.file.name}),this.queuePreviewedImageUpdate()}async queuePreviewedImageUpdate({immediate:e}={}){clearTimeout(this.updateImageTimeout),e?this.updateImage():this.updateImageTimeout=window.setTimeout(this.updateImage.bind(this),100)}async immediateImageUpdate(e,t){const i=this.state.sides[t],s=this.workerBridges[0],n=this.mainAbortController.signal,o=this.state.preprocessorState,a=i.latestSettings.encoderState?i.latestSettings.processorState:Je,r=i.latestSettings.encoderState;if(!r)return e;try{const{decoded:t,vectorImage:i}=await Gt(n,e,s),l=await Wt(n,t,o,s),h=await Zt(n,{decoded:t,file:e,preprocessed:l,vectorImage:i},a,s);return await Nt(n,h,r,e.name,s)}catch(e){if(tt(e))return null;throw console.error(`Image processing error: ${e}`),e}}async updateImage(){const e=this.state,t=this.activeMainJob||{file:e.source&&e.source.file,preprocessorState:e.encodedPreprocessorState},i=e.sides.map(((e,t)=>this.activeSideJobs[t]||{processorState:e.encodedSettings&&e.encodedSettings.processorState,encoderState:e.encodedSettings&&e.encodedSettings.encoderState})),s={file:this.sourceFile,preprocessorState:e.preprocessorState},n=e.sides.map((e=>({processorState:e.latestSettings.encoderState?e.latestSettings.processorState:Je,encoderState:e.latestSettings.encoderState}))),o=t.file!=s.file,a=o||t.preprocessorState!==s.preprocessorState,r=i.map(((e,t)=>{const i=a||!e.processorState||!!e.encoderState!=!!n[t].encoderState||!function(e,t){if(e===t)return!0;for(const i of Object.keys(e))if((e[i].enabled||t[i].enabled)&&e!==t)return!1;return!0}(e.processorState,n[t].processorState);return{processing:i,encoding:i||e.encoderState!==n[t].encoderState}}));let l=!1;(o||a)&&(this.mainAbortController.abort(),this.mainAbortController=new AbortController,l=!0,this.activeMainJob=s);for(const[e,t]of r.entries())(t.processing||t.encoding)&&(this.sideAbortControllers[e].abort(),this.sideAbortControllers[e]=new AbortController,l=!0,this.activeSideJobs[e]=n[e]);if(!l)return;const h=this.mainAbortController.signal,c=this.sideAbortControllers.map((e=>e.signal));let p,d,u;if(o)try{j(h),this.setState({source:void 0,loading:!0});const{decoded:e,vectorImage:t}=await Gt(h,s.file,this.workerBridges[0]);p=e,d=t,this.setState((e=>{if(h.aborted)return{};return{sides:e.sides.map((e=>$e(e,"latestSettings.processorState.resize",{width:p.width,height:p.height,method:d?"vector":"lanczos3",enabled:!1})))}}))}catch(e){if(tt(e))return;throw this.props.showSnack(`Source decoding error: ${e}`),e}else({decoded:p,vectorImage:d}=e.source);if(a)try{j(h),this.setState({loading:!0});const e=await Wt(h,p,s.preprocessorState,this.workerBridges[0]);u={decoded:p,vectorImage:d,preprocessed:e,file:s.file},this.setState((t=>{if(h.aborted)return{};let i={...t,loading:!1,source:u,encodedPreprocessorState:s.preprocessorState,sides:t.sides.map((t=>{t.downloadUrl&&URL.revokeObjectURL(t.downloadUrl);return{...t,data:e,processed:void 0,encodedSettings:void 0}}))};return i=function(e){let t={...e};for(const i of[0,1]){const s=e.sides[i].downloadUrl;s&&URL.revokeObjectURL(s),t=$e(e,`sides.${i}`,{preprocessed:void 0,file:void 0,downloadUrl:void 0,data:void 0,encodedSettings:void 0})}return t}(i),i}))}catch(e){if(e instanceof Error&&"AbortError"===e.name)return;throw this.setState({loading:!1}),this.props.showSnack(`Preprocessing error: ${e}`),e}else u=e.source;this.activeMainJob=void 0,r.forEach((async(t,i)=>{try{if(!t.encoding)return;const s=c[i],o=n[i],a=this.workerBridges[i];let r,l,h;if(o.encoderState){const n=this.encodeCache.match(u.preprocessed,o.processorState,o.encoderState);n?({file:r,processed:h,data:l}=n):(this.setState((e=>{if(s.aborted)return{};return{sides:$e(e.sides,i,{loading:!0})}})),t.processing?(h=await Zt(s,u,o.processorState,a),this.setState((e=>{if(s.aborted)return{};const t=e.sides[i],n={...t,processed:h,data:h,encodedSettings:{...t.encodedSettings,processorState:o.processorState}};return{sides:et(e.sides,i,n)}}))):h=e.sides[i].processed,r=await Nt(s,h,o.encoderState,u.file.name,a),l=await Qt(s,r,a),this.encodeCache.add({data:l,processed:h,file:r,preprocessed:u.preprocessed,encoderState:o.encoderState,processorState:o.processorState}))}else r=u.file,l=u.preprocessed;this.setState((e=>{if(s.aborted)return{};const t=e.sides[i];t.downloadUrl&&URL.revokeObjectURL(t.downloadUrl);const n={...t,data:l,file:r,downloadUrl:URL.createObjectURL(r),loading:!1,processed:h,encodedSettings:{processorState:o.processorState,encoderState:o.encoderState}};return{sides:et(e.sides,i,n)}})),this.activeSideJobs[i]=void 0}catch(e){if(e instanceof Error&&"AbortError"===e.name)return;throw this.setState((e=>({sides:$e(e.sides,i,{loading:!1})}))),this.props.showSnack(`Processing error: ${e}`),e}}))}downloadFromUrl(e,t){let i=document.createElement("a");i.setAttribute("href",e),i.setAttribute("download",t),i.setAttribute("target","_blank"),i.click(),i.remove()}handleDownloadAll(e){return async()=>{try{let t=0;const i=async e=>{if(0===e.length)return;const i=(await Promise.allSettled(e)).map((e=>"fulfilled"===e.status?e.value:null)).filter(Boolean),s=i.map((e=>({url:URL.createObjectURL(e),fileName:e.name})));for(const{url:e,fileName:t}of s)this.downloadFromUrl(e,t),await new Promise((e=>setTimeout(e,300)));s.forEach((({url:e})=>URL.revokeObjectURL(e))),t+=s.length},s=10;let n=[];for(let t=0;t<this.files.length;t++){const o=this.files[t],a=this.immediateImageUpdate(o,e);n.push(a),t%s==0&&(await i(n),n=[])}await i(n),t===this.files.length?this.props.showSnack("All files have been saved successfully!"):this.props.showSnack("Some files could not be processed")}catch(e){console.error("Error saving files:",e),this.props.showSnack("There was an error saving the files. Please try again.")}}}onFileChanged(e){const t=e.target.value,i=this.files.find((e=>e.name===t));i?(this.sourceFile=i,this.queuePreviewedImageUpdate({immediate:!0})):console.warn("Could not find the file specified. File name: "+t)}render({onBack:e},{loading:t,sides:s,source:n,mobileView:o,preprocessorState:a}){const[r,l]=s,[h,c]=s.map((e=>e.data)),p=s.map(((e,t)=>i.h(gt,{index:t,source:n,mobileView:o,processorState:e.latestSettings.processorState,encoderState:e.latestSettings.encoderState,onEncoderTypeChange:this.onEncoderTypeChange,onEncoderOptionsChange:this.onEncoderOptionsChange,onProcessorOptionsChange:this.onProcessorOptionsChange,onCopyToOtherSideClick:this.onCopyToOtherClick,onSaveSideSettingsClick:this.onSaveSideSettingsClick,onImportSideSettingsClick:this.onImportSideSettingsClick}))),d=s.map(((e,s)=>i.h(i.p,{key:s},this.files.length>1&&i.h("button",{onClick:this.handleDownloadAll(s),class:0===s?"_download-all-button-left_cfw4k_22":"_download-all-button-right_cfw4k_42"},`Download All ${this.files.length} Files `),i.h(Vt,{downloadUrl:e.downloadUrl,imageFile:e.file,source:n,loading:t||e.loading,flipSide:o||1===s,typeLabel:e.latestSettings.encoderState?We[e.latestSettings.encoderState.type].meta.label:""+(e.file?`${e.file.name}`:"Original Image")})))),u=r.encodedSettings||r.latestSettings,g=l.encodedSettings||l.latestSettings,_=u.processorState.resize.enabled&&"contain"===u.processorState.resize.fitMethod,m=g.processorState.resize.enabled&&"contain"===g.processorState.resize.fitMethod;return i.h("div",{class:"_compress_cfw4k_1"},i.h(Ft,{source:n,mobileView:o,leftCompressed:h,rightCompressed:c,leftImgContain:_,rightImgContain:m,preprocessorState:a,onPreprocessorChange:this.onPreprocessorChange},this.files.length>1?i.h(D,{style:{height:"100%"},value:this.sourceFile.name,onChange:this.onFileChanged.bind(this)},this.files.map(((e,t)=>i.h("option",{value:e.name,key:e.name+t},e.name)))):i.h(i.p,null)),i.h("div",{class:"_top_cfw4k_180"},i.h("button",{class:"_back_cfw4k_154 unbutton",onClick:e},i.h("svg",{viewBox:"0 0 61 53.3"},i.h("title",null,"Back"),i.h("path",{class:"_back-blob_cfw4k_165",d:"M0 25.6c-.5-7.1 4.1-14.5 10-19.1S23.4.1 32.2 0c8.8 0 19 1.6 24.4 8s5.6 17.8 1.7 27a29.7 29.7 0 01-20.5 18c-8.4 1.5-17.3-2.6-24.5-8S.5 32.6.1 25.6z"}),i.h("path",{class:"_back-x_cfw4k_207",d:"M41.6 17.1l-2-2.1-8.3 8.2-8.2-8.2-2 2 8.2 8.3-8.3 8.2 2.1 2 8.2-8.1 8.3 8.2 2-2-8.2-8.3z"})))),o?i.h("div",{class:"_options_cfw4k_70"},i.h("multi-panel",{class:"_multi-panel_cfw4k_129","open-one-only":!0},i.h("div",{class:Jt},d[0]),i.h("div",{class:Jt},p[0]),i.h("div",{class:Xt},d[1]),i.h("div",{class:Xt},p[1]))):[i.h("div",{class:"_options-1_cfw4k_91 _options_cfw4k_70 _options-1-theme_cfw4k_91",key:"options1"},p[0],d[0]),i.h("div",{class:"_options-2_cfw4k_104 _options_cfw4k_70 _options-2-theme_cfw4k_104",key:"options2"},p[1],d[1])])}}t.default=ei}));
