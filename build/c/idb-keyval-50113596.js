define(["require","exports"],(function(e,r){class t{constructor(e="keyval-store",r="keyval"){this.storeName=r,this._dbp=new Promise(((t,o)=>{const n=indexedDB.open(e,1);n.onerror=()=>o(n.error),n.onsuccess=()=>t(n.result),n.onupgradeneeded=()=>{n.result.createObjectStore(r)}}))}_withIDBStore(e,r){return this._dbp.then((t=>new Promise(((o,n)=>{const s=t.transaction(this.storeName,e);s.oncomplete=()=>o(),s.onabort=s.onerror=()=>n(s.error),r(s.objectStore(this.storeName))}))))}}let o;function n(){return o||(o=new t),o}r.get=function(e,r=n()){let t;return r._withIDBStore("readonly",(r=>{t=r.get(e)})).then((()=>t.result))},r.set=function(e,r,t=n()){return t._withIDBStore("readwrite",(t=>{t.put(r,e)}))},r.swUrl="/serviceworker.js"}));
