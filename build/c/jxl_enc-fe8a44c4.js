define(["module","require","exports"],(function(n,r,t){var e,a=(e=n.uri,function(r){var t,a;(r=void 0!==(r=r||{})?r:{}).ready=new Promise((function(n,r){t=n,a=r}));var o,i={};for(o in r)r.hasOwnProperty(o)&&(i[o]=r[o]);var u,c="./this.program",f=!0,s="";s=self.location.href,e&&(s=e),s=0!==s.indexOf("blob:")?s.substr(0,s.lastIndexOf("/")+1):"",u=function(n){var r=new XMLHttpRequest;return r.open("GET",n,!1),r.responseType="arraybuffer",r.send(null),new Uint8Array(r.response)};var l,p,h=r.print||console.log.bind(console),g=r.printErr||console.warn.bind(console);for(o in i)i.hasOwnProperty(o)&&(r[o]=i[o]);i=null,r.arguments&&r.arguments,r.thisProgram&&(c=r.thisProgram),r.quit&&r.quit,r.wasmBinary&&(l=r.wasmBinary),r.noExitRuntime,"object"!=typeof WebAssembly&&B("no native wasm support detected");var y=!1,v=new TextDecoder("utf8");function d(n,r){if(!n)return"";for(var t=n+r,e=n;!(e>=t)&&F[e];)++e;return v.decode(F.subarray(n,e))}function m(n,r,t,e){if(!(e>0))return 0;for(var a=t,o=t+e-1,i=0;i<n.length;++i){var u=n.charCodeAt(i);if(u>=55296&&u<=57343&&(u=65536+((1023&u)<<10)|1023&n.charCodeAt(++i)),u<=127){if(t>=o)break;r[t++]=u}else if(u<=2047){if(t+1>=o)break;r[t++]=192|u>>6,r[t++]=128|63&u}else if(u<=65535){if(t+2>=o)break;r[t++]=224|u>>12,r[t++]=128|u>>6&63,r[t++]=128|63&u}else{if(t+3>=o)break;r[t++]=240|u>>18,r[t++]=128|u>>12&63,r[t++]=128|u>>6&63,r[t++]=128|63&u}}return r[t]=0,t-a}function _(n){for(var r=0,t=0;t<n.length;++t){var e=n.charCodeAt(t);e>=55296&&e<=57343&&(e=65536+((1023&e)<<10)|1023&n.charCodeAt(++t)),e<=127?++r:r+=e<=2047?2:e<=65535?3:4}return r}var w,T,F,b,E,A,C,S,O,R,D=new TextDecoder("utf-16le");function P(n,r){for(var t=n,e=t>>1,a=e+r/2;!(e>=a)&&E[e];)++e;return t=e<<1,D.decode(F.subarray(n,t))}function W(n,r,t){if(void 0===t&&(t=2147483647),t<2)return 0;for(var e=r,a=(t-=2)<2*n.length?t/2:n.length,o=0;o<a;++o){var i=n.charCodeAt(o);b[r>>1]=i,r+=2}return b[r>>1]=0,r-e}function U(n){return 2*n.length}function M(n,r){for(var t=0,e="";!(t>=r/4);){var a=A[n+4*t>>2];if(0==a)break;if(++t,a>=65536){var o=a-65536;e+=String.fromCharCode(55296|o>>10,56320|1023&o)}else e+=String.fromCharCode(a)}return e}function k(n,r,t){if(void 0===t&&(t=2147483647),t<4)return 0;for(var e=r,a=e+t-4,o=0;o<n.length;++o){var i=n.charCodeAt(o);if(i>=55296&&i<=57343&&(i=65536+((1023&i)<<10)|1023&n.charCodeAt(++o)),A[r>>2]=i,(r+=4)+4>a)break}return A[r>>2]=0,r-e}function j(n){for(var r=0,t=0;t<n.length;++t){var e=n.charCodeAt(t);e>=55296&&e<=57343&&++t,r+=4}return r}function I(n){w=n,r.HEAP8=T=new Int8Array(n),r.HEAP16=b=new Int16Array(n),r.HEAP32=A=new Int32Array(n),r.HEAPU8=F=new Uint8Array(n),r.HEAPU16=E=new Uint16Array(n),r.HEAPU32=C=new Uint32Array(n),r.HEAPF32=S=new Float32Array(n),r.HEAPF64=O=new Float64Array(n)}r.INITIAL_MEMORY;var Y=[],H=[],x=[],N=0,V=null;function B(n){r.onAbort&&r.onAbort(n),g(n+=""),y=!0,n="abort("+n+"). Build with -s ASSERTIONS=1 for more info.";var t=new WebAssembly.RuntimeError(n);throw a(t),t}r.preloadedImages={},r.preloadedAudios={};var z,q="data:application/octet-stream;base64,";function G(n){return n.startsWith(q)}if(r.locateFile)G(L="jxl_enc.wasm")||(z=L,L=r.locateFile?r.locateFile(z,s):s+z);else var L=new URL("/c/jxl_enc-68f8271f.wasm",n.uri).toString();function Z(n){try{if(n==L&&l)return new Uint8Array(l);if(u)return u(n);throw"both async and sync fetching of the wasm failed"}catch(n){B(n)}}function J(n){for(;n.length>0;){var t=n.shift();if("function"!=typeof t){var e=t.func;"number"==typeof e?void 0===t.arg?R.get(e)():R.get(e)(t.arg):e(void 0===t.arg?null:t.arg)}else t(r)}}var X={DESTRUCTOR_OFFSET:0,REFCOUNT_OFFSET:4,TYPE_OFFSET:8,CAUGHT_OFFSET:12,RETHROWN_OFFSET:13,SIZE:16};function K(n){this.excPtr=n,this.ptr=n-X.SIZE,this.set_type=function(n){A[this.ptr+X.TYPE_OFFSET>>2]=n},this.get_type=function(){return A[this.ptr+X.TYPE_OFFSET>>2]},this.set_destructor=function(n){A[this.ptr+X.DESTRUCTOR_OFFSET>>2]=n},this.get_destructor=function(){return A[this.ptr+X.DESTRUCTOR_OFFSET>>2]},this.set_refcount=function(n){A[this.ptr+X.REFCOUNT_OFFSET>>2]=n},this.set_caught=function(n){n=n?1:0,T[this.ptr+X.CAUGHT_OFFSET|0]=n},this.get_caught=function(){return 0!=T[this.ptr+X.CAUGHT_OFFSET|0]},this.set_rethrown=function(n){n=n?1:0,T[this.ptr+X.RETHROWN_OFFSET|0]=n},this.get_rethrown=function(){return 0!=T[this.ptr+X.RETHROWN_OFFSET|0]},this.init=function(n,r){this.set_type(n),this.set_destructor(r),this.set_refcount(0),this.set_caught(!1),this.set_rethrown(!1)},this.add_ref=function(){var n=A[this.ptr+X.REFCOUNT_OFFSET>>2];A[this.ptr+X.REFCOUNT_OFFSET>>2]=n+1},this.release_ref=function(){var n=A[this.ptr+X.REFCOUNT_OFFSET>>2];return A[this.ptr+X.REFCOUNT_OFFSET>>2]=n-1,1===n}}var Q={mappings:{},buffers:[null,[],[]],printChar:function(n,r){var t=Q.buffers[n];0===r||10===r?((1===n?h:g)(function(n,r,t){for(var e=r+t,a=r;n[a]&&!(a>=e);)++a;return v.decode(n.subarray?n.subarray(r,a):new Uint8Array(n.slice(r,a)))}(t,0)),t.length=0):t.push(r)},varargs:void 0,get:function(){return Q.varargs+=4,A[Q.varargs-4>>2]},getStr:function(n){return d(n)},get64:function(n,r){return n}},$={};function nn(n){for(;n.length;){var r=n.pop();n.pop()(r)}}function rn(n){return this.fromWireType(C[n>>2])}var tn={},en={},an={},on=48,un=57;function cn(n){if(void 0===n)return"_unknown";var r=(n=n.replace(/[^a-zA-Z0-9_]/g,"$")).charCodeAt(0);return r>=on&&r<=un?"_"+n:n}function fn(n,r){return n=cn(n),new Function("body","return function "+n+'() {\n    "use strict";    return body.apply(this, arguments);\n};\n')(r)}function sn(n,r){var t=fn(r,(function(n){this.name=r,this.message=n;var t=new Error(n).stack;void 0!==t&&(this.stack=this.toString()+"\n"+t.replace(/^Error(:[^\n]*)?\n/,""))}));return t.prototype=Object.create(n.prototype),t.prototype.constructor=t,t.prototype.toString=function(){return void 0===this.message?this.name:this.name+": "+this.message},t}var ln=void 0;function pn(n){throw new ln(n)}function hn(n,r,t){function e(r){var e=t(r);e.length!==n.length&&pn("Mismatched type converter count");for(var a=0;a<n.length;++a)_n(n[a],e[a])}n.forEach((function(n){an[n]=r}));var a=new Array(r.length),o=[],i=0;r.forEach((function(n,r){en.hasOwnProperty(n)?a[r]=en[n]:(o.push(n),tn.hasOwnProperty(n)||(tn[n]=[]),tn[n].push((function(){a[r]=en[n],++i===o.length&&e(a)})))})),0===o.length&&e(a)}function gn(n){switch(n){case 1:return 0;case 2:return 1;case 4:return 2;case 8:return 3;default:throw new TypeError("Unknown type size: "+n)}}var yn=void 0;function vn(n){for(var r="",t=n;F[t];)r+=yn[F[t++]];return r}var dn=void 0;function mn(n){throw new dn(n)}function _n(n,r,t){if(t=t||{},!("argPackAdvance"in r))throw new TypeError("registerType registeredInstance requires argPackAdvance");var e=r.name;if(n||mn('type "'+e+'" must have a positive integer typeid pointer'),en.hasOwnProperty(n)){if(t.ignoreDuplicateRegistrations)return;mn("Cannot register type '"+e+"' twice")}if(en[n]=r,delete an[n],tn.hasOwnProperty(n)){var a=tn[n];delete tn[n],a.forEach((function(n){n()}))}}var wn=[],Tn=[{},{value:void 0},{value:null},{value:!0},{value:!1}];function Fn(n){n>4&&0==--Tn[n].refcount&&(Tn[n]=void 0,wn.push(n))}function bn(){for(var n=0,r=5;r<Tn.length;++r)void 0!==Tn[r]&&++n;return n}function En(){for(var n=5;n<Tn.length;++n)if(void 0!==Tn[n])return Tn[n];return null}function An(n){switch(n){case void 0:return 1;case null:return 2;case!0:return 3;case!1:return 4;default:var r=wn.length?wn.pop():Tn.length;return Tn[r]={refcount:1,value:n},r}}function Cn(n){if(null===n)return"null";var r=typeof n;return"object"===r||"array"===r||"function"===r?n.toString():""+n}function Sn(n,r){switch(r){case 2:return function(n){return this.fromWireType(S[n>>2])};case 3:return function(n){return this.fromWireType(O[n>>3])};default:throw new TypeError("Unknown float type: "+n)}}function On(n,r,t,e,a){var o=r.length;o<2&&mn("argTypes array size mismatch! Must at least get return value and 'this' types!");for(var i=null!==r[1]&&null!==t,u=!1,c=1;c<r.length;++c)if(null!==r[c]&&void 0===r[c].destructorFunction){u=!0;break}var f="void"!==r[0].name,s="",l="";for(c=0;c<o-2;++c)s+=(0!==c?", ":"")+"arg"+c,l+=(0!==c?", ":"")+"arg"+c+"Wired";var p="return function "+cn(n)+"("+s+") {\nif (arguments.length !== "+(o-2)+") {\nthrowBindingError('function "+n+" called with ' + arguments.length + ' arguments, expected "+(o-2)+" args!');\n}\n";u&&(p+="var destructors = [];\n");var h=u?"destructors":"null",g=["throwBindingError","invoker","fn","runDestructors","retType","classParam"],y=[mn,e,a,nn,r[0],r[1]];for(i&&(p+="var thisWired = classParam.toWireType("+h+", this);\n"),c=0;c<o-2;++c)p+="var arg"+c+"Wired = argType"+c+".toWireType("+h+", arg"+c+"); // "+r[c+2].name+"\n",g.push("argType"+c),y.push(r[c+2]);if(i&&(l="thisWired"+(l.length>0?", ":"")+l),p+=(f?"var rv = ":"")+"invoker(fn"+(l.length>0?", ":"")+l+");\n",u)p+="runDestructors(destructors);\n";else for(c=i?1:2;c<r.length;++c){var v=1===c?"thisWired":"arg"+(c-2)+"Wired";null!==r[c].destructorFunction&&(p+=v+"_dtor("+v+"); // "+r[c].name+"\n",g.push(v+"_dtor"),y.push(r[c].destructorFunction))}return f&&(p+="var ret = retType.fromWireType(rv);\nreturn ret;\n"),p+="}\n",g.push(p),function(n,r){if(!(n instanceof Function))throw new TypeError("new_ called with constructor type "+typeof n+" which is not a function");var t=fn(n.name||"unknownFunctionName",(function(){}));t.prototype=n.prototype;var e=new t,a=n.apply(e,r);return a instanceof Object?a:e}(Function,g).apply(null,y)}function Rn(n,t,e){r.hasOwnProperty(n)?((void 0===e||void 0!==r[n].overloadTable&&void 0!==r[n].overloadTable[e])&&mn("Cannot register public name '"+n+"' twice"),function(n,r,t){if(void 0===n[r].overloadTable){var e=n[r];n[r]=function(){return n[r].overloadTable.hasOwnProperty(arguments.length)||mn("Function '"+t+"' called with an invalid number of arguments ("+arguments.length+") - expects one of ("+n[r].overloadTable+")!"),n[r].overloadTable[arguments.length].apply(this,arguments)},n[r].overloadTable=[],n[r].overloadTable[e.argCount]=e}}(r,n,n),r.hasOwnProperty(e)&&mn("Cannot register multiple overloads of a function with the same number of arguments ("+e+")!"),r[n].overloadTable[e]=t):(r[n]=t,void 0!==e&&(r[n].numArguments=e))}function Dn(n,t,e){return n.includes("j")?function(n,t,e){var a=r["dynCall_"+n];return e&&e.length?a.apply(null,[t].concat(e)):a.call(null,t)}(n,t,e):R.get(t).apply(null,e)}function Pn(n,r){var t,e,a,o=(n=vn(n)).includes("j")?(t=n,e=r,a=[],function(){a.length=arguments.length;for(var n=0;n<arguments.length;n++)a[n]=arguments[n];return Dn(t,e,a)}):R.get(r);return"function"!=typeof o&&mn("unknown function pointer with signature "+n+": "+r),o}var Wn=void 0;function Un(n){var r=Kn(n),t=vn(r);return Xn(r),t}function Mn(n,r,t){switch(r){case 0:return t?function(n){return T[n]}:function(n){return F[n]};case 1:return t?function(n){return b[n>>1]}:function(n){return E[n>>1]};case 2:return t?function(n){return A[n>>2]}:function(n){return C[n>>2]};default:throw new TypeError("Unknown integer type: "+n)}}var kn={};function jn(){return"object"==typeof globalThis?globalThis:Function("return this")()}function In(n,r){var t=en[n];return void 0===t&&mn(r+" has unknown type "+Un(n)),t}var Yn={};function Hn(n){try{return p.grow(n-w.byteLength+65535>>>16),I(p.buffer),1}catch(n){}}var xn={};function Nn(){if(!Nn.strings){var n={USER:"web_user",LOGNAME:"web_user",PATH:"/",PWD:"/",HOME:"/home/<USER>",LANG:("object"==typeof navigator&&navigator.languages&&navigator.languages[0]||"C").replace("-","_")+".UTF-8",_:c||"./this.program"};for(var r in xn)n[r]=xn[r];var t=[];for(var r in n)t.push(r+"="+n[r]);Nn.strings=t}return Nn.strings}function Vn(n){return n%4==0&&(n%100!=0||n%400==0)}function Bn(n,r){for(var t=0,e=0;e<=r;t+=n[e++]);return t}var zn=[31,29,31,30,31,30,31,31,30,31,30,31],qn=[31,28,31,30,31,30,31,31,30,31,30,31];function Gn(n,r){for(var t=new Date(n.getTime());r>0;){var e=Vn(t.getFullYear()),a=t.getMonth(),o=(e?zn:qn)[a];if(!(r>o-t.getDate()))return t.setDate(t.getDate()+r),t;r-=o-t.getDate()+1,t.setDate(1),a<11?t.setMonth(a+1):(t.setMonth(0),t.setFullYear(t.getFullYear()+1))}return t}function Ln(n,r,t,e){var a=A[e+40>>2],o={tm_sec:A[e>>2],tm_min:A[e+4>>2],tm_hour:A[e+8>>2],tm_mday:A[e+12>>2],tm_mon:A[e+16>>2],tm_year:A[e+20>>2],tm_wday:A[e+24>>2],tm_yday:A[e+28>>2],tm_isdst:A[e+32>>2],tm_gmtoff:A[e+36>>2],tm_zone:a?d(a):""},i=d(t),u={"%c":"%a %b %d %H:%M:%S %Y","%D":"%m/%d/%y","%F":"%Y-%m-%d","%h":"%b","%r":"%I:%M:%S %p","%R":"%H:%M","%T":"%H:%M:%S","%x":"%m/%d/%y","%X":"%H:%M:%S","%Ec":"%c","%EC":"%C","%Ex":"%m/%d/%y","%EX":"%H:%M:%S","%Ey":"%y","%EY":"%Y","%Od":"%d","%Oe":"%e","%OH":"%H","%OI":"%I","%Om":"%m","%OM":"%M","%OS":"%S","%Ou":"%u","%OU":"%U","%OV":"%V","%Ow":"%w","%OW":"%W","%Oy":"%y"};for(var c in u)i=i.replace(new RegExp(c,"g"),u[c]);var f=["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],s=["January","February","March","April","May","June","July","August","September","October","November","December"];function l(n,r,t){for(var e="number"==typeof n?n.toString():n||"";e.length<r;)e=t[0]+e;return e}function p(n,r){return l(n,r,"0")}function h(n,r){function t(n){return n<0?-1:n>0?1:0}var e;return 0===(e=t(n.getFullYear()-r.getFullYear()))&&0===(e=t(n.getMonth()-r.getMonth()))&&(e=t(n.getDate()-r.getDate())),e}function g(n){switch(n.getDay()){case 0:return new Date(n.getFullYear()-1,11,29);case 1:return n;case 2:return new Date(n.getFullYear(),0,3);case 3:return new Date(n.getFullYear(),0,2);case 4:return new Date(n.getFullYear(),0,1);case 5:return new Date(n.getFullYear()-1,11,31);case 6:return new Date(n.getFullYear()-1,11,30)}}function y(n){var r=Gn(new Date(n.tm_year+1900,0,1),n.tm_yday),t=new Date(r.getFullYear(),0,4),e=new Date(r.getFullYear()+1,0,4),a=g(t),o=g(e);return h(a,r)<=0?h(o,r)<=0?r.getFullYear()+1:r.getFullYear():r.getFullYear()-1}var v={"%a":function(n){return f[n.tm_wday].substring(0,3)},"%A":function(n){return f[n.tm_wday]},"%b":function(n){return s[n.tm_mon].substring(0,3)},"%B":function(n){return s[n.tm_mon]},"%C":function(n){return p((n.tm_year+1900)/100|0,2)},"%d":function(n){return p(n.tm_mday,2)},"%e":function(n){return l(n.tm_mday,2," ")},"%g":function(n){return y(n).toString().substring(2)},"%G":function(n){return y(n)},"%H":function(n){return p(n.tm_hour,2)},"%I":function(n){var r=n.tm_hour;return 0==r?r=12:r>12&&(r-=12),p(r,2)},"%j":function(n){return p(n.tm_mday+Bn(Vn(n.tm_year+1900)?zn:qn,n.tm_mon-1),3)},"%m":function(n){return p(n.tm_mon+1,2)},"%M":function(n){return p(n.tm_min,2)},"%n":function(){return"\n"},"%p":function(n){return n.tm_hour>=0&&n.tm_hour<12?"AM":"PM"},"%S":function(n){return p(n.tm_sec,2)},"%t":function(){return"\t"},"%u":function(n){return n.tm_wday||7},"%U":function(n){var r=new Date(n.tm_year+1900,0,1),t=0===r.getDay()?r:Gn(r,7-r.getDay()),e=new Date(n.tm_year+1900,n.tm_mon,n.tm_mday);if(h(t,e)<0){var a=Bn(Vn(e.getFullYear())?zn:qn,e.getMonth()-1)-31,o=31-t.getDate()+a+e.getDate();return p(Math.ceil(o/7),2)}return 0===h(t,r)?"01":"00"},"%V":function(n){var r,t=new Date(n.tm_year+1900,0,4),e=new Date(n.tm_year+1901,0,4),a=g(t),o=g(e),i=Gn(new Date(n.tm_year+1900,0,1),n.tm_yday);return h(i,a)<0?"53":h(o,i)<=0?"01":(r=a.getFullYear()<n.tm_year+1900?n.tm_yday+32-a.getDate():n.tm_yday+1-a.getDate(),p(Math.ceil(r/7),2))},"%w":function(n){return n.tm_wday},"%W":function(n){var r=new Date(n.tm_year,0,1),t=1===r.getDay()?r:Gn(r,0===r.getDay()?1:7-r.getDay()+1),e=new Date(n.tm_year+1900,n.tm_mon,n.tm_mday);if(h(t,e)<0){var a=Bn(Vn(e.getFullYear())?zn:qn,e.getMonth()-1)-31,o=31-t.getDate()+a+e.getDate();return p(Math.ceil(o/7),2)}return 0===h(t,r)?"01":"00"},"%y":function(n){return(n.tm_year+1900).toString().substring(2)},"%Y":function(n){return n.tm_year+1900},"%z":function(n){var r=n.tm_gmtoff,t=r>=0;return r=(r=Math.abs(r)/60)/60*100+r%60,(t?"+":"-")+String("0000"+r).slice(-4)},"%Z":function(n){return n.tm_zone},"%%":function(){return"%"}};for(var c in v)i.includes(c)&&(i=i.replace(new RegExp(c,"g"),v[c](o)));var w,F,b,E,C,S,O=(w=i,F=!1,E=b>0?b:_(w)+1,C=new Array(E),S=m(w,C,0,C.length),F&&(C.length=S),C);return O.length>r?0:(function(n,r){T.set(n,r)}(O,n),O.length-1)}ln=r.InternalError=sn(Error,"InternalError"),function(){for(var n=new Array(256),r=0;r<256;++r)n[r]=String.fromCharCode(r);yn=n}(),dn=r.BindingError=sn(Error,"BindingError"),r.count_emval_handles=bn,r.get_first_emval=En,Wn=r.UnboundTypeError=sn(Error,"UnboundTypeError");var Zn={u:function(n){return Jn(n+X.SIZE)+X.SIZE},I:function(n,r){},p:function(n,r,t){throw new K(n).init(r,t),n},h:function(n,r,t){return Q.varargs=t,0},A:function(n,r,t){return Q.varargs=t,0},B:function(n,r,t){Q.varargs=t},m:function(n){var r=$[n];delete $[n];var t=r.rawConstructor,e=r.rawDestructor,a=r.fields;hn([n],a.map((function(n){return n.getterReturnType})).concat(a.map((function(n){return n.setterArgumentType}))),(function(n){var o={};return a.forEach((function(r,t){var e=r.fieldName,i=n[t],u=r.getter,c=r.getterContext,f=n[t+a.length],s=r.setter,l=r.setterContext;o[e]={read:function(n){return i.fromWireType(u(c,n))},write:function(n,r){var t=[];s(l,n,f.toWireType(t,r)),nn(t)}}})),[{name:r.name,fromWireType:function(n){var r={};for(var t in o)r[t]=o[t].read(n);return e(n),r},toWireType:function(n,r){for(var a in o)if(!(a in r))throw new TypeError('Missing field:  "'+a+'"');var i=t();for(a in o)o[a].write(i,r[a]);return null!==n&&n.push(e,i),i},argPackAdvance:8,readValueFromPointer:rn,destructorFunction:e}]}))},r:function(n,r,t,e,a){},D:function(n,r,t,e,a){var o=gn(t);_n(n,{name:r=vn(r),fromWireType:function(n){return!!n},toWireType:function(n,r){return r?e:a},argPackAdvance:8,readValueFromPointer:function(n){var e;if(1===t)e=T;else if(2===t)e=b;else{if(4!==t)throw new TypeError("Unknown boolean type size: "+r);e=A}return this.fromWireType(e[n>>o])},destructorFunction:null})},C:function(n,r){_n(n,{name:r=vn(r),fromWireType:function(n){var r=Tn[n].value;return Fn(n),r},toWireType:function(n,r){return An(r)},argPackAdvance:8,readValueFromPointer:rn,destructorFunction:null})},j:function(n,r,t){var e=gn(t);_n(n,{name:r=vn(r),fromWireType:function(n){return n},toWireType:function(n,r){if("number"!=typeof r&&"boolean"!=typeof r)throw new TypeError('Cannot convert "'+Cn(r)+'" to '+this.name);return r},argPackAdvance:8,readValueFromPointer:Sn(r,e),destructorFunction:null})},l:function(n,t,e,a,o,i){var u=function(n,r){for(var t=[],e=0;e<n;e++)t.push(A[(r>>2)+e]);return t}(t,e);n=vn(n),o=Pn(a,o),Rn(n,(function(){!function(n,r){var t=[],e={};throw r.forEach((function n(r){e[r]||en[r]||(an[r]?an[r].forEach(n):(t.push(r),e[r]=!0))})),new Wn(n+": "+t.map(Un).join([", "]))}("Cannot call "+n+" due to unbound types",u)}),t-1),hn([],u,(function(e){var a=[e[0],null].concat(e.slice(1));return function(n,t,e){r.hasOwnProperty(n)||pn("Replacing nonexistant public symbol"),void 0!==r[n].overloadTable&&void 0!==e?r[n].overloadTable[e]=t:(r[n]=t,r[n].argCount=e)}(n,On(n,a,null,o,i),t-1),[]}))},c:function(n,r,t,e,a){r=vn(r),-1===a&&(a=4294967295);var o=gn(t),i=function(n){return n};if(0===e){var u=32-8*t;i=function(n){return n<<u>>>u}}var c=r.includes("unsigned");_n(n,{name:r,fromWireType:i,toWireType:function(n,t){if("number"!=typeof t&&"boolean"!=typeof t)throw new TypeError('Cannot convert "'+Cn(t)+'" to '+this.name);if(t<e||t>a)throw new TypeError('Passing a number "'+Cn(t)+'" from JS side to C/C++ side to an argument of type "'+r+'", which is outside the valid range ['+e+", "+a+"]!");return c?t>>>0:0|t},argPackAdvance:8,readValueFromPointer:Mn(r,o,0!==e),destructorFunction:null})},b:function(n,r,t){var e=[Int8Array,Uint8Array,Int16Array,Uint16Array,Int32Array,Uint32Array,Float32Array,Float64Array][r];function a(n){var r=C,t=r[n>>=2],a=r[n+1];return new e(w,a,t)}_n(n,{name:t=vn(t),fromWireType:a,argPackAdvance:8,readValueFromPointer:a},{ignoreDuplicateRegistrations:!0})},k:function(n,r){var t="std::string"===(r=vn(r));_n(n,{name:r,fromWireType:function(n){var r,e=C[n>>2];if(t)for(var a=n+4,o=0;o<=e;++o){var i=n+4+o;if(o==e||0==F[i]){var u=d(a,i-a);void 0===r?r=u:(r+=String.fromCharCode(0),r+=u),a=i+1}}else{var c=new Array(e);for(o=0;o<e;++o)c[o]=String.fromCharCode(F[n+4+o]);r=c.join("")}return Xn(n),r},toWireType:function(n,r){r instanceof ArrayBuffer&&(r=new Uint8Array(r));var e="string"==typeof r;e||r instanceof Uint8Array||r instanceof Uint8ClampedArray||r instanceof Int8Array||mn("Cannot pass non-string to std::string");var a=(t&&e?function(){return _(r)}:function(){return r.length})(),o=Jn(4+a+1);if(C[o>>2]=a,t&&e)m(r,F,o+4,a+1);else if(e)for(var i=0;i<a;++i){var u=r.charCodeAt(i);u>255&&(Xn(o),mn("String has UTF-16 code units that do not fit in 8 bits")),F[o+4+i]=u}else for(i=0;i<a;++i)F[o+4+i]=r[i];return null!==n&&n.push(Xn,o),o},argPackAdvance:8,readValueFromPointer:rn,destructorFunction:function(n){Xn(n)}})},g:function(n,r,t){var e,a,o,i,u;t=vn(t),2===r?(e=P,a=W,i=U,o=function(){return E},u=1):4===r&&(e=M,a=k,i=j,o=function(){return C},u=2),_n(n,{name:t,fromWireType:function(n){for(var t,a=C[n>>2],i=o(),c=n+4,f=0;f<=a;++f){var s=n+4+f*r;if(f==a||0==i[s>>u]){var l=e(c,s-c);void 0===t?t=l:(t+=String.fromCharCode(0),t+=l),c=s+r}}return Xn(n),t},toWireType:function(n,e){"string"!=typeof e&&mn("Cannot pass non-string to C++ string type "+t);var o=i(e),c=Jn(4+o+r);return C[c>>2]=o>>u,a(e,c+4,o+r),null!==n&&n.push(Xn,c),c},argPackAdvance:8,readValueFromPointer:rn,destructorFunction:function(n){Xn(n)}})},n:function(n,r,t,e,a,o){$[n]={name:vn(r),rawConstructor:Pn(t,e),rawDestructor:Pn(a,o),fields:[]}},d:function(n,r,t,e,a,o,i,u,c,f){$[n].fields.push({fieldName:vn(r),getterReturnType:t,getter:Pn(e,a),getterContext:o,setterArgumentType:i,setter:Pn(u,c),setterContext:f})},E:function(n,r){_n(n,{isVoid:!0,name:r=vn(r),argPackAdvance:0,fromWireType:function(){},toWireType:function(n,r){}})},e:Fn,H:function(n){return 0===n?An(jn()):(n=void 0===(t=kn[r=n])?vn(r):t,An(jn()[n]));var r,t},G:function(n){n>4&&(Tn[n].refcount+=1)},o:function(n,t,e,a){n=function(n){return n||mn("Cannot use deleted val. handle = "+n),Tn[n].value}(n);var o=Yn[t];return o||(o=function(n){for(var t="",e=0;e<n;++e)t+=(0!==e?", ":"")+"arg"+e;var a="return function emval_allocator_"+n+"(constructor, argTypes, args) {\n";for(e=0;e<n;++e)a+="var argType"+e+" = requireRegisteredType(Module['HEAP32'][(argTypes >>> 2) + "+e+'], "parameter '+e+'");\nvar arg'+e+" = argType"+e+".readValueFromPointer(args);\nargs += argType"+e+"['argPackAdvance'];\n";return a+="var obj = new constructor("+t+");\nreturn __emval_register(obj);\n}\n",new Function("requireRegisteredType","Module","__emval_register",a)(In,r,An)}(t),Yn[t]=o),o(n,e,a)},a:function(){B()},t:function(n,r,t){F.copyWithin(n,r,r+t)},f:function(n){var r,t,e=F.length,a=2147483648;if((n>>>=0)>a)return!1;for(var o=1;o<=4;o*=2){var i=e*(1+.2/o);if(i=Math.min(i,n+100663296),Hn(Math.min(a,((r=Math.max(n,i))%(t=65536)>0&&(r+=t-r%t),r))))return!0}return!1},w:function(n,r){var t=0;return Nn().forEach((function(e,a){var o=r+t;A[n+4*a>>2]=o,function(n,r,t){for(var e=0;e<n.length;++e)T[0|r++]=n.charCodeAt(e);t||(T[0|r]=0)}(e,o),t+=e.length+1})),0},x:function(n,r){var t=Nn();A[n>>2]=t.length;var e=0;return t.forEach((function(n){e+=n.length+1})),A[r>>2]=e,0},i:function(n){return 0},z:function(n,r,t,e){var a=Q.getStreamFromFD(n),o=Q.doReadv(a,r,t);return A[e>>2]=o,0},q:function(n,r,t,e,a){},y:function(n,r,t,e){for(var a=0,o=0;o<t;o++){for(var i=A[r+8*o>>2],u=A[r+(8*o+4)>>2],c=0;c<u;c++)Q.printChar(n,F[i+c]);a+=u}return A[e>>2]=a,0},s:function(n){},v:function(n,r,t,e){return Ln(n,r,t,e)},F:function(n){return n?(r=52,A[$n()>>2]=r,-1):0;var r}};!function(){var n={a:Zn};function t(n,t){var e,a=n.exports;r.asm=a,I((p=r.asm.J).buffer),R=r.asm.Q,e=r.asm.K,H.unshift(e),function(){if(N--,r.monitorRunDependencies&&r.monitorRunDependencies(N),0==N&&V){var n=V;V=null,n()}}()}function e(n){t(n.instance)}function o(r){return(!l&&f&&"function"==typeof fetch?fetch(L,{credentials:"same-origin"}).then((function(n){if(!n.ok)throw"failed to load wasm binary file at '"+L+"'";return n.arrayBuffer()})).catch((function(){return Z(L)})):Promise.resolve().then((function(){return Z(L)}))).then((function(r){return WebAssembly.instantiate(r,n)})).then(r,(function(n){g("failed to asynchronously prepare wasm: "+n),B(n)}))}if(N++,r.monitorRunDependencies&&r.monitorRunDependencies(N),r.instantiateWasm)try{return r.instantiateWasm(n,t)}catch(n){return g("Module.instantiateWasm callback failed with error: "+n),!1}(l||"function"!=typeof WebAssembly.instantiateStreaming||G(L)||"function"!=typeof fetch?o(e):fetch(L,{credentials:"same-origin"}).then((function(r){return WebAssembly.instantiateStreaming(r,n).then(e,(function(n){return g("wasm streaming compile failed: "+n),g("falling back to ArrayBuffer instantiation"),o(e)}))}))).catch(a)}(),r.___wasm_call_ctors=function(){return(r.___wasm_call_ctors=r.asm.K).apply(null,arguments)};var Jn=r._malloc=function(){return(Jn=r._malloc=r.asm.L).apply(null,arguments)},Xn=r._free=function(){return(Xn=r._free=r.asm.M).apply(null,arguments)},Kn=r.___getTypeName=function(){return(Kn=r.___getTypeName=r.asm.N).apply(null,arguments)};r.___embind_register_native_and_builtin_types=function(){return(r.___embind_register_native_and_builtin_types=r.asm.O).apply(null,arguments)};var Qn,$n=r.___errno_location=function(){return($n=r.___errno_location=r.asm.P).apply(null,arguments)};function nr(n){function e(){Qn||(Qn=!0,r.calledRun=!0,y||(J(H),t(r),r.onRuntimeInitialized&&r.onRuntimeInitialized(),function(){if(r.postRun)for("function"==typeof r.postRun&&(r.postRun=[r.postRun]);r.postRun.length;)n=r.postRun.shift(),x.unshift(n);var n;J(x)}()))}N>0||(function(){if(r.preRun)for("function"==typeof r.preRun&&(r.preRun=[r.preRun]);r.preRun.length;)n=r.preRun.shift(),Y.unshift(n);var n;J(Y)}(),N>0||(r.setStatus?(r.setStatus("Running..."),setTimeout((function(){setTimeout((function(){r.setStatus("")}),1),e()}),1)):e()))}if(r.dynCall_jiji=function(){return(r.dynCall_jiji=r.asm.R).apply(null,arguments)},r.dynCall_iiji=function(){return(r.dynCall_iiji=r.asm.S).apply(null,arguments)},r.dynCall_iiiiij=function(){return(r.dynCall_iiiiij=r.asm.T).apply(null,arguments)},r.dynCall_iiiiijj=function(){return(r.dynCall_iiiiijj=r.asm.U).apply(null,arguments)},r.dynCall_iiiiiijj=function(){return(r.dynCall_iiiiiijj=r.asm.V).apply(null,arguments)},r.dynCall_viijii=function(){return(r.dynCall_viijii=r.asm.W).apply(null,arguments)},V=function n(){Qn||nr(),Qn||(V=n)},r.run=nr,r.preInit)for("function"==typeof r.preInit&&(r.preInit=[r.preInit]);r.preInit.length>0;)r.preInit.pop()();return nr(),r.ready});t.default=a}));
