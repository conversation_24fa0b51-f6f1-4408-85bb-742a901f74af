define(["module","require","exports"],(function(e,r,n){var t,a=(t=e.uri,function(r){function n(){return A.buffer!=S&&Y(A.buffer),O}function a(){return A.buffer!=S&&Y(A.buffer),k}function o(){return A.buffer!=S&&Y(A.buffer),x}function i(){return A.buffer!=S&&Y(A.buffer),P}function u(){return A.buffer!=S&&Y(A.buffer),R}function s(){return A.buffer!=S&&Y(A.buffer),N}function c(){return A.buffer!=S&&Y(A.buffer),F}var f,l;(r=void 0!==(r=r||{})?r:{}).ready=new Promise((function(e,r){f=e,l=r}));var d,h={};for(d in r)r.hasOwnProperty(d)&&(h[d]=r[d]);var p,v=function(e,r){throw r},m=!0,E=r.ENVIRONMENT_IS_PTHREAD||!1,g="";function _(e){return r.locateFile?r.locateFile(e,g):g+e}g=self.location.href,t&&(g=t),g=0!==g.indexOf("blob:")?g.substr(0,g.lastIndexOf("/")+1):"",p=function(e){var r=new XMLHttpRequest;return r.open("GET",e,!1),r.responseType="arraybuffer",r.send(null),new Uint8Array(r.response)};var y,T=r.print||console.log.bind(console),w=r.printErr||console.warn.bind(console);for(d in h)h.hasOwnProperty(d)&&(r[d]=h[d]);h=null,r.arguments&&r.arguments,r.thisProgram&&r.thisProgram,r.quit&&(v=r.quit),r.wasmBinary&&(y=r.wasmBinary);var A,C,b=r.noExitRuntime||!0;"object"!=typeof WebAssembly&&ne("no native wasm support detected");var S,O,k,x,P,R,N,W,F,I=!1;function L(e,r){e||ne("Assertion failed: "+r)}function D(e,r,n){for(var t=r+n,a="";!(r>=t);){var o=e[r++];if(!o)return a;if(128&o){var i=63&e[r++];if(192!=(224&o)){var u=63&e[r++];if((o=224==(240&o)?(15&o)<<12|i<<6|u:(7&o)<<18|i<<12|u<<6|63&e[r++])<65536)a+=String.fromCharCode(o);else{var s=o-65536;a+=String.fromCharCode(55296|s>>10,56320|1023&s)}}else a+=String.fromCharCode((31&o)<<6|i)}else a+=String.fromCharCode(o)}return a}function M(e,r){return e?D(a(),e,r):""}function H(e,r,n){return function(e,r,n,t){if(!(t>0))return 0;for(var a=n,o=n+t-1,i=0;i<e.length;++i){var u=e.charCodeAt(i);if(u>=55296&&u<=57343&&(u=65536+((1023&u)<<10)|1023&e.charCodeAt(++i)),u<=127){if(n>=o)break;r[n++]=u}else if(u<=2047){if(n+1>=o)break;r[n++]=192|u>>6,r[n++]=128|63&u}else if(u<=65535){if(n+2>=o)break;r[n++]=224|u>>12,r[n++]=128|u>>6&63,r[n++]=128|63&u}else{if(n+3>=o)break;r[n++]=240|u>>18,r[n++]=128|u>>12&63,r[n++]=128|u>>6&63,r[n++]=128|63&u}}return r[n]=0,n-a}(e,a(),r,n)}function B(e){for(var r=0,n=0;n<e.length;++n){var t=e.charCodeAt(n);t>=55296&&t<=57343&&(t=65536+((1023&t)<<10)|1023&e.charCodeAt(++n)),t<=127?++r:r+=t<=2047?2:t<=65535?3:4}return r}function U(e,r){for(var n="",t=0;!(t>=r/2);++t){var a=o()[e+2*t>>1];if(0==a)break;n+=String.fromCharCode(a)}return n}function j(e,r,n){if(void 0===n&&(n=2147483647),n<2)return 0;for(var t=r,a=(n-=2)<2*e.length?n/2:e.length,i=0;i<a;++i){var u=e.charCodeAt(i);o()[r>>1]=u,r+=2}return o()[r>>1]=0,r-t}function G(e){return 2*e.length}function V(e,r){for(var n=0,t="";!(n>=r/4);){var a=u()[e+4*n>>2];if(0==a)break;if(++n,a>=65536){var o=a-65536;t+=String.fromCharCode(55296|o>>10,56320|1023&o)}else t+=String.fromCharCode(a)}return t}function q(e,r,n){if(void 0===n&&(n=2147483647),n<4)return 0;for(var t=r,a=t+n-4,o=0;o<e.length;++o){var i=e.charCodeAt(o);if(i>=55296&&i<=57343&&(i=65536+((1023&i)<<10)|1023&e.charCodeAt(++o)),u()[r>>2]=i,(r+=4)+4>a)break}return u()[r>>2]=0,r-t}function z(e){for(var r=0,n=0;n<e.length;++n){var t=e.charCodeAt(n);t>=55296&&t<=57343&&++n,r+=4}return r}function Y(e){S=e,r.HEAP8=O=new Int8Array(e),r.HEAP16=x=new Int16Array(e),r.HEAP32=R=new Int32Array(e),r.HEAPU8=k=new Uint8Array(e),r.HEAPU16=P=new Uint16Array(e),r.HEAPU32=N=new Uint32Array(e),r.HEAPF32=W=new Float32Array(e),r.HEAPF64=F=new Float64Array(e)}E&&(S=r.buffer);var K,Q=r.INITIAL_MEMORY||16777216;if(E)A=r.wasmMemory,S=r.buffer;else if(r.wasmMemory)A=r.wasmMemory;else if(!((A=new WebAssembly.Memory({initial:Q/65536,maximum:32768,shared:!0})).buffer instanceof SharedArrayBuffer))throw w("requested a shared WebAssembly.Memory but the returned buffer is not a SharedArrayBuffer, indicating that while the browser has SharedArrayBuffer it does not have WebAssembly threads support - you may need to set a flag"),Error("bad memory");A&&(S=A.buffer),Q=S.byteLength,Y(S);var X=[],Z=[],J=[];function $(){E||se(Z)}var ee=0,re=null;function ne(e){r.onAbort&&r.onAbort(e),E&&console.error("Pthread aborting at "+(new Error).stack),w(e+=""),I=!0,e="abort("+e+"). Build with -s ASSERTIONS=1 for more info.";var n=new WebAssembly.RuntimeError(e);throw l(n),n}r.preloadedImages={},r.preloadedAudios={};var te="data:application/octet-stream;base64,";function ae(e){return e.startsWith(te)}if(r.locateFile)ae(oe="wp2_enc_mt_simd.wasm")||(oe=_(oe));else var oe=new URL("/c/wp2_enc_mt_simd-0b0595e9.wasm",e.uri).toString();function ie(e){try{if(e==oe&&y)return new Uint8Array(y);if(p)return p(e);throw"both async and sync fetching of the wasm failed"}catch(e){ne(e)}}var ue={112528:function(){throw"Canceled!"},112546:function(e,r){setTimeout((function(){Rr(e,r)}),0)}};function se(e){for(;e.length>0;){var n=e.shift();if("function"!=typeof n){var t=n.func;"number"==typeof t?void 0===n.arg?K.get(t)():K.get(t)(n.arg):t(void 0===n.arg?null:n.arg)}else n(r)}}var ce={EPERM:63,ENOENT:44,ESRCH:71,EINTR:27,EIO:29,ENXIO:60,E2BIG:1,ENOEXEC:45,EBADF:8,ECHILD:12,EAGAIN:6,EWOULDBLOCK:6,ENOMEM:48,EACCES:2,EFAULT:21,ENOTBLK:105,EBUSY:10,EEXIST:20,EXDEV:75,ENODEV:43,ENOTDIR:54,EISDIR:31,EINVAL:28,ENFILE:41,EMFILE:33,ENOTTY:59,ETXTBSY:74,EFBIG:22,ENOSPC:51,ESPIPE:70,EROFS:69,EMLINK:34,EPIPE:64,EDOM:18,ERANGE:68,ENOMSG:49,EIDRM:24,ECHRNG:106,EL2NSYNC:156,EL3HLT:107,EL3RST:108,ELNRNG:109,EUNATCH:110,ENOCSI:111,EL2HLT:112,EDEADLK:16,ENOLCK:46,EBADE:113,EBADR:114,EXFULL:115,ENOANO:104,EBADRQC:103,EBADSLT:102,EDEADLOCK:16,EBFONT:101,ENOSTR:100,ENODATA:116,ETIME:117,ENOSR:118,ENONET:119,ENOPKG:120,EREMOTE:121,ENOLINK:47,EADV:122,ESRMNT:123,ECOMM:124,EPROTO:65,EMULTIHOP:36,EDOTDOT:125,EBADMSG:9,ENOTUNIQ:126,EBADFD:127,EREMCHG:128,ELIBACC:129,ELIBBAD:130,ELIBSCN:131,ELIBMAX:132,ELIBEXEC:133,ENOSYS:52,ENOTEMPTY:55,ENAMETOOLONG:37,ELOOP:32,EOPNOTSUPP:138,EPFNOSUPPORT:139,ECONNRESET:15,ENOBUFS:42,EAFNOSUPPORT:5,EPROTOTYPE:67,ENOTSOCK:57,ENOPROTOOPT:50,ESHUTDOWN:140,ECONNREFUSED:14,EADDRINUSE:3,ECONNABORTED:13,ENETUNREACH:40,ENETDOWN:38,ETIMEDOUT:73,EHOSTDOWN:142,EHOSTUNREACH:23,EINPROGRESS:26,EALREADY:7,EDESTADDRREQ:17,EMSGSIZE:35,EPROTONOSUPPORT:66,ESOCKTNOSUPPORT:137,EADDRNOTAVAIL:4,ENETRESET:39,EISCONN:30,ENOTCONN:53,ETOOMANYREFS:141,EUSERS:136,EDQUOT:19,ESTALE:72,ENOTSUP:138,ENOMEDIUM:148,EILSEQ:25,EOVERFLOW:61,ECANCELED:11,ENOTRECOVERABLE:56,EOWNERDEAD:62,ESTRPIPE:135};function fe(e,r){if(e<=0||e>n().length||!0&e||r<0)return-28;if(0==r)return 0;r>=2147483647&&(r=1/0);var t=Atomics.load(u(),Yr>>2),a=0;if(t==e&&Atomics.compareExchange(u(),Yr>>2,t,0)==t&&(a=1,--r<=0))return 1;var o=Atomics.notify(u(),e>>2,r);if(o>=0)return o+a;throw"Atomics.notify returned an unexpected value "+o}function le(e){if(E)throw"Internal Error! cleanupThread() can only ever be called from main application thread!";if(!e)throw"Internal Error! Null pthread_ptr in cleanupThread!";var r=de.pthreads[e];if(r){u()[e+12>>2]=0;var n=r.worker;de.returnWorkerToPool(n)}}r._emscripten_futex_wake=fe;var de={unusedWorkers:[],runningWorkers:[],tlsInitFunctions:[],initMainThreadBlock:function(){for(var e=navigator.hardwareConcurrency,r=0;r<e;++r)de.allocateUnusedWorker()},initRuntime:function(){for(var e=Or(228),r=0;r<57;++r)s()[e/4+r]=0;u()[e+12>>2]=e;var n=e+152;u()[n>>2]=n;var t=Or(512);for(r=0;r<128;++r)s()[t/4+r]=0;Atomics.store(s(),e+100>>2,t),Atomics.store(s(),e+40>>2,e),Lr(e,!m,1),xr(e)},initWorker:function(){},pthreads:{},threadExitHandlers:[],runExitHandlers:function(){for(;de.threadExitHandlers.length>0;)de.threadExitHandlers.pop()();E&&Mr()&&Hr()},runExitHandlersAndDeinitThread:function(e,r){Atomics.store(s(),e+56>>2,1),Atomics.store(s(),e+60>>2,0),de.runExitHandlers(),Atomics.store(s(),e+4>>2,r),Atomics.store(s(),e+0>>2,1),fe(e+0,2147483647),Lr(0,0,0)},setExitStatus:function(e){},threadExit:function(e){var r=Mr();r&&(de.runExitHandlersAndDeinitThread(r,e),E&&postMessage({cmd:"exit"}))},threadCancel:function(){de.runExitHandlersAndDeinitThread(Mr(),-1),postMessage({cmd:"cancelDone"})},terminateAllThreads:function(){for(var e in de.pthreads)(t=de.pthreads[e])&&t.worker&&de.returnWorkerToPool(t.worker);de.pthreads={};for(var r=0;r<de.unusedWorkers.length;++r)(n=de.unusedWorkers[r]).terminate();for(de.unusedWorkers=[],r=0;r<de.runningWorkers.length;++r){var n,t=(n=de.runningWorkers[r]).pthread;de.freeThreadData(t),n.terminate()}de.runningWorkers=[]},freeThreadData:function(e){if(e){if(e.threadInfoStruct){var r=u()[e.threadInfoStruct+100>>2];u()[e.threadInfoStruct+100>>2]=0,Sr(r),Sr(e.threadInfoStruct)}e.threadInfoStruct=0,e.allocatedOwnStack&&e.stackBase&&Sr(e.stackBase),e.stackBase=0,e.worker&&(e.worker.pthread=null)}},returnWorkerToPool:function(e){de.runWithoutMainThreadQueuedCalls((function(){delete de.pthreads[e.pthread.threadInfoStruct],de.unusedWorkers.push(e),de.runningWorkers.splice(de.runningWorkers.indexOf(e),1),de.freeThreadData(e.pthread),e.pthread=void 0}))},runWithoutMainThreadQueuedCalls:function(e){u()[zr>>2]=0;try{e()}finally{u()[zr>>2]=1}},receiveObjectTransfer:function(e){},threadInit:function(){for(var e in de.tlsInitFunctions)de.tlsInitFunctions[e]()},loadWasmModuleToWorker:function(e,n){e.onmessage=function(t){var a=t.data,o=a.cmd;if(e.pthread&&(de.currentProxiedOperationCallerThread=e.pthread.threadInfoStruct),a.targetThread&&a.targetThread!=Mr()){var i=de.pthreads[a.targetThread];return i?i.worker.postMessage(t.data,a.transferList):console.error('Internal error! Worker sent a message "'+o+'" to target pthread '+a.targetThread+", but that thread no longer exists!"),void(de.currentProxiedOperationCallerThread=void 0)}if("processQueuedMainThreadWork"===o)Wr();else if("spawnThread"===o)wr(t.data);else if("cleanupThread"===o)le(a.thread);else if("killThread"===o)!function(e){if(E)throw"Internal Error! killThread() can only ever be called from main application thread!";if(!e)throw"Internal Error! Null pthread_ptr in killThread!";u()[e+12>>2]=0;var r=de.pthreads[e];r.worker.terminate(),de.freeThreadData(r),de.runningWorkers.splice(de.runningWorkers.indexOf(r.worker),1),r.worker.pthread=void 0}(a.thread);else if("cancelThread"===o)!function(e){if(E)throw"Internal Error! cancelThread() can only ever be called from main application thread!";if(!e)throw"Internal Error! Null pthread_ptr in cancelThread!";de.pthreads[e].worker.postMessage({cmd:"cancel"})}(a.thread);else if("loaded"===o)e.loaded=!0,n&&n(e),e.runPthread&&(e.runPthread(),delete e.runPthread);else if("print"===o)T("Thread "+a.threadId+": "+a.text);else if("printErr"===o)w("Thread "+a.threadId+": "+a.text);else if("alert"===o)alert("Thread "+a.threadId+": "+a.text);else if("exit"===o)e.pthread&&Atomics.load(s(),e.pthread.threadInfoStruct+64>>2)&&de.returnWorkerToPool(e);else if("exitProcess"===o)try{!function(e,n){if(!n||!ve()||0!==e){if(!n&&E)throw postMessage({cmd:"exitProcess",returnCode:e}),new Kr(e);ve()||(de.terminateAllThreads(),r.onExit&&r.onExit(e),I=!0),v(e,new Kr(e))}}(a.returnCode)}catch(t){if(t instanceof Kr)return;throw t}else"cancelDone"===o?de.returnWorkerToPool(e):"objectTransfer"===o?de.receiveObjectTransfer(t.data):"setimmediate"===t.data.target?e.postMessage(t.data):w("worker sent an unknown command "+o);de.currentProxiedOperationCallerThread=void 0},e.onerror=function(e){w("pthread sent an error! "+e.filename+":"+e.lineno+": "+e.message)},e.postMessage({cmd:"load",urlOrBlob:r.mainScriptUrlOrBlob,wasmMemory:A,wasmModule:C})},allocateUnusedWorker:function(){if(r.locateFile){var n=_("wp2_enc_mt_simd.worker.js");de.unusedWorkers.push(new Worker(n))}else de.unusedWorkers.push(new Worker(new URL("/c/wp2_enc_mt_simd.worker-c154edb5.js",e.uri)))},getNewWorker:function(){return 0==de.unusedWorkers.length&&(de.allocateUnusedWorker(),de.loadWasmModuleToWorker(de.unusedWorkers[0])),de.unusedWorkers.pop()},busySpinWait:function(e){for(var r=performance.now()+e;performance.now()<r;);}};r.establishStackSpace=function(e,r){Gr(e,r),Ur(e)},r.invokeEntryPoint=function(e,r){return K.get(e)(r)};var he,pe=0;function ve(){return b||pe>0}r.keepRuntimeAlive=ve,he=E?function(){return performance.now()-r.__performance_now_clock_drift}:function(){return performance.now()};var me={DESTRUCTOR_OFFSET:0,REFCOUNT_OFFSET:4,TYPE_OFFSET:8,CAUGHT_OFFSET:12,RETHROWN_OFFSET:13,SIZE:16};function Ee(e){this.excPtr=e,this.ptr=e-me.SIZE,this.set_type=function(e){u()[this.ptr+me.TYPE_OFFSET>>2]=e},this.get_type=function(){return u()[this.ptr+me.TYPE_OFFSET>>2]},this.set_destructor=function(e){u()[this.ptr+me.DESTRUCTOR_OFFSET>>2]=e},this.get_destructor=function(){return u()[this.ptr+me.DESTRUCTOR_OFFSET>>2]},this.set_refcount=function(e){u()[this.ptr+me.REFCOUNT_OFFSET>>2]=e},this.set_caught=function(e){e=e?1:0,n()[this.ptr+me.CAUGHT_OFFSET|0]=e},this.get_caught=function(){return 0!=n()[this.ptr+me.CAUGHT_OFFSET|0]},this.set_rethrown=function(e){e=e?1:0,n()[this.ptr+me.RETHROWN_OFFSET|0]=e},this.get_rethrown=function(){return 0!=n()[this.ptr+me.RETHROWN_OFFSET|0]},this.init=function(e,r){this.set_type(e),this.set_destructor(r),this.set_refcount(0),this.set_caught(!1),this.set_rethrown(!1)},this.add_ref=function(){Atomics.add(u(),this.ptr+me.REFCOUNT_OFFSET>>2,1)},this.release_ref=function(){return 1===Atomics.sub(u(),this.ptr+me.REFCOUNT_OFFSET>>2,1)}}var ge={};function _e(e){for(;e.length;){var r=e.pop();e.pop()(r)}}function ye(e){return this.fromWireType(s()[e>>2])}var Te={},we={},Ae={},Ce=48,be=57;function Se(e){if(void 0===e)return"_unknown";var r=(e=e.replace(/[^a-zA-Z0-9_]/g,"$")).charCodeAt(0);return r>=Ce&&r<=be?"_"+e:e}function Oe(e,r){return e=Se(e),new Function("body","return function "+e+'() {\n    "use strict";    return body.apply(this, arguments);\n};\n')(r)}function ke(e,r){var n=Oe(r,(function(e){this.name=r,this.message=e;var n=new Error(e).stack;void 0!==n&&(this.stack=this.toString()+"\n"+n.replace(/^Error(:[^\n]*)?\n/,""))}));return n.prototype=Object.create(e.prototype),n.prototype.constructor=n,n.prototype.toString=function(){return void 0===this.message?this.name:this.name+": "+this.message},n}var xe=void 0;function Pe(e){throw new xe(e)}function Re(e,r,n){function t(r){var t=n(r);t.length!==e.length&&Pe("Mismatched type converter count");for(var a=0;a<e.length;++a)De(e[a],t[a])}e.forEach((function(e){Ae[e]=r}));var a=new Array(r.length),o=[],i=0;r.forEach((function(e,r){we.hasOwnProperty(e)?a[r]=we[e]:(o.push(e),Te.hasOwnProperty(e)||(Te[e]=[]),Te[e].push((function(){a[r]=we[e],++i===o.length&&t(a)})))})),0===o.length&&t(a)}function Ne(e){switch(e){case 1:return 0;case 2:return 1;case 4:return 2;case 8:return 3;default:throw new TypeError("Unknown type size: "+e)}}var We=void 0;function Fe(e){for(var r="",n=e;a()[n];)r+=We[a()[n++]];return r}var Ie=void 0;function Le(e){throw new Ie(e)}function De(e,r,n){if(n=n||{},!("argPackAdvance"in r))throw new TypeError("registerType registeredInstance requires argPackAdvance");var t=r.name;if(e||Le('type "'+t+'" must have a positive integer typeid pointer'),we.hasOwnProperty(e)){if(n.ignoreDuplicateRegistrations)return;Le("Cannot register type '"+t+"' twice")}if(we[e]=r,delete Ae[e],Te.hasOwnProperty(e)){var a=Te[e];delete Te[e],a.forEach((function(e){e()}))}}var Me=[],He=[{},{value:void 0},{value:null},{value:!0},{value:!1}];function Be(e){e>4&&0==--He[e].refcount&&(He[e]=void 0,Me.push(e))}function Ue(){for(var e=0,r=5;r<He.length;++r)void 0!==He[r]&&++e;return e}function je(){for(var e=5;e<He.length;++e)if(void 0!==He[e])return He[e];return null}function Ge(e){switch(e){case void 0:return 1;case null:return 2;case!0:return 3;case!1:return 4;default:var r=Me.length?Me.pop():He.length;return He[r]={refcount:1,value:e},r}}function Ve(e){if(null===e)return"null";var r=typeof e;return"object"===r||"array"===r||"function"===r?e.toString():""+e}function qe(e,r){switch(r){case 2:return function(e){return this.fromWireType((A.buffer!=S&&Y(A.buffer),W)[e>>2])};case 3:return function(e){return this.fromWireType(c()[e>>3])};default:throw new TypeError("Unknown float type: "+e)}}function ze(e,r,n,t,a){var o=r.length;o<2&&Le("argTypes array size mismatch! Must at least get return value and 'this' types!");for(var i=null!==r[1]&&null!==n,u=!1,s=1;s<r.length;++s)if(null!==r[s]&&void 0===r[s].destructorFunction){u=!0;break}var c="void"!==r[0].name,f="",l="";for(s=0;s<o-2;++s)f+=(0!==s?", ":"")+"arg"+s,l+=(0!==s?", ":"")+"arg"+s+"Wired";var d="return function "+Se(e)+"("+f+") {\nif (arguments.length !== "+(o-2)+") {\nthrowBindingError('function "+e+" called with ' + arguments.length + ' arguments, expected "+(o-2)+" args!');\n}\n";u&&(d+="var destructors = [];\n");var h=u?"destructors":"null",p=["throwBindingError","invoker","fn","runDestructors","retType","classParam"],v=[Le,t,a,_e,r[0],r[1]];for(i&&(d+="var thisWired = classParam.toWireType("+h+", this);\n"),s=0;s<o-2;++s)d+="var arg"+s+"Wired = argType"+s+".toWireType("+h+", arg"+s+"); // "+r[s+2].name+"\n",p.push("argType"+s),v.push(r[s+2]);if(i&&(l="thisWired"+(l.length>0?", ":"")+l),d+=(c?"var rv = ":"")+"invoker(fn"+(l.length>0?", ":"")+l+");\n",u)d+="runDestructors(destructors);\n";else for(s=i?1:2;s<r.length;++s){var m=1===s?"thisWired":"arg"+(s-2)+"Wired";null!==r[s].destructorFunction&&(d+=m+"_dtor("+m+"); // "+r[s].name+"\n",p.push(m+"_dtor"),v.push(r[s].destructorFunction))}return c&&(d+="var ret = retType.fromWireType(rv);\nreturn ret;\n"),d+="}\n",p.push(d),function(e,r){if(!(e instanceof Function))throw new TypeError("new_ called with constructor type "+typeof e+" which is not a function");var n=Oe(e.name||"unknownFunctionName",(function(){}));n.prototype=e.prototype;var t=new n,a=e.apply(t,r);return a instanceof Object?a:t}(Function,p).apply(null,v)}function Ye(e,n,t){r.hasOwnProperty(e)?((void 0===t||void 0!==r[e].overloadTable&&void 0!==r[e].overloadTable[t])&&Le("Cannot register public name '"+e+"' twice"),function(e,r,n){if(void 0===e[r].overloadTable){var t=e[r];e[r]=function(){return e[r].overloadTable.hasOwnProperty(arguments.length)||Le("Function '"+n+"' called with an invalid number of arguments ("+arguments.length+") - expects one of ("+e[r].overloadTable+")!"),e[r].overloadTable[arguments.length].apply(this,arguments)},e[r].overloadTable=[],e[r].overloadTable[t.argCount]=t}}(r,e,e),r.hasOwnProperty(t)&&Le("Cannot register multiple overloads of a function with the same number of arguments ("+t+")!"),r[e].overloadTable[t]=n):(r[e]=n,void 0!==t&&(r[e].numArguments=t))}function Ke(e,n,t){return e.includes("j")?function(e,n,t){var a=r["dynCall_"+e];return t&&t.length?a.apply(null,[n].concat(t)):a.call(null,n)}(e,n,t):K.get(n).apply(null,t)}function Qe(e,r){var n,t,a,o=(e=Fe(e)).includes("j")?(n=e,t=r,a=[],function(){a.length=arguments.length;for(var e=0;e<arguments.length;e++)a[e]=arguments[e];return Ke(n,t,a)}):K.get(r);return"function"!=typeof o&&Le("unknown function pointer with signature "+e+": "+r),o}var Xe=void 0;function Ze(e){var r=kr(e),n=Fe(r);return Sr(r),n}function Je(e,r,t){switch(r){case 0:return t?function(e){return n()[e]}:function(e){return a()[e]};case 1:return t?function(e){return o()[e>>1]}:function(e){return i()[e>>1]};case 2:return t?function(e){return u()[e>>2]}:function(e){return s()[e>>2]};default:throw new TypeError("Unknown integer type: "+e)}}var $e={};function er(){return"object"==typeof globalThis?globalThis:Function("return this")()}function rr(e,r){var n=we[e];return void 0===n&&Le(r+" has unknown type "+Ze(e)),n}var nr={},tr=[];function ar(e,r,t){if(e<=0||e>n().length||!0&e)return-28;var a=Atomics.wait(u(),e>>2,r,t);if("timed-out"===a)return-73;if("not-equal"===a)return-6;if("ok"===a)return 0;throw"Atomics.wait returned an unexpected value "+a}function or(e,r){for(var n=arguments.length-2,t=Br(),a=n,o=jr(8*a),i=o>>3,u=0;u<n;u++){var s=arguments[2+u];c()[i+u]=s}var f=Fr(e,a,o,r);return Ur(t),f}var ir=[];function ur(e){try{return A.grow(e-S.byteLength+65535>>>16),Y(A.buffer),1}catch(e){}}var sr={inEventHandler:0,removeAllEventListeners:function(){for(var e=sr.eventHandlers.length-1;e>=0;--e)sr._removeHandler(e);sr.eventHandlers=[],sr.deferredCalls=[]},registerRemoveEventListeners:function(){sr.removeEventListenersRegistered||(sr.removeEventListenersRegistered=!0)},deferredCalls:[],deferCall:function(e,r,n){function t(e,r){if(e.length!=r.length)return!1;for(var n in e)if(e[n]!=r[n])return!1;return!0}for(var a in sr.deferredCalls){var o=sr.deferredCalls[a];if(o.targetFunction==e&&t(o.argsList,n))return}sr.deferredCalls.push({targetFunction:e,precedence:r,argsList:n}),sr.deferredCalls.sort((function(e,r){return e.precedence<r.precedence}))},removeDeferredCalls:function(e){for(var r=0;r<sr.deferredCalls.length;++r)sr.deferredCalls[r].targetFunction==e&&(sr.deferredCalls.splice(r,1),--r)},canPerformEventHandlerRequests:function(){return sr.inEventHandler&&sr.currentEventHandler.allowsDeferredCalls},runDeferredCalls:function(){if(sr.canPerformEventHandlerRequests())for(var e=0;e<sr.deferredCalls.length;++e){var r=sr.deferredCalls[e];sr.deferredCalls.splice(e,1),--e,r.targetFunction.apply(null,r.argsList)}},eventHandlers:[],removeAllHandlersOnTarget:function(e,r){for(var n=0;n<sr.eventHandlers.length;++n)sr.eventHandlers[n].target!=e||r&&r!=sr.eventHandlers[n].eventTypeString||sr._removeHandler(n--)},_removeHandler:function(e){var r=sr.eventHandlers[e];r.target.removeEventListener(r.eventTypeString,r.eventListenerFunc,r.useCapture),sr.eventHandlers.splice(e,1)},registerOrRemoveHandler:function(e){var r=function(r){++sr.inEventHandler,sr.currentEventHandler=e,sr.runDeferredCalls(),e.handlerFunc(r),sr.runDeferredCalls(),--sr.inEventHandler};if(e.callbackfunc)e.eventListenerFunc=r,e.target.addEventListener(e.eventTypeString,r,e.useCapture),sr.eventHandlers.push(e),sr.registerRemoveEventListeners();else for(var n=0;n<sr.eventHandlers.length;++n)sr.eventHandlers[n].target==e.target&&sr.eventHandlers[n].eventTypeString==e.eventTypeString&&sr._removeHandler(n--)},queueEventHandlerOnThread_iiii:function(e,r,n,t,a){var o=Br(),i=jr(12);u()[i>>2]=n,u()[i+4>>2]=t,u()[i+8>>2]=a,Ir(0,e,637534208,r,t,i),Ur(o)},getTargetThreadForEventCallback:function(e){switch(e){case 1:return 0;case 2:return de.currentProxiedOperationCallerThread;default:return e}},getNodeNameForTarget:function(e){return e?e==window?"#window":e==screen?"#screen":e&&e.nodeName?e.nodeName:"":""},fullscreenEnabled:function(){return document.fullscreenEnabled||document.webkitFullscreenEnabled}};function cr(e,r,n,t){var a,o,i,s=Br(),c=jr(12),f=0;r&&(o=B(a=r)+1,i=Or(o),H(a,i,o),f=i),u()[c>>2]=f,u()[c+4>>2]=n,u()[c+8>>2]=t,Ir(0,e,657457152,0,f,c),Ur(s)}var fr=[0,"undefined"!=typeof document?document:0,"undefined"!=typeof window?window:0];function lr(e){var r;return e=(r=e)>2?M(r):r,fr[e]||("undefined"!=typeof document?document.querySelector(e):void 0)}function dr(e){return lr(e)}function hr(e,r,n){var t=dr(e);if(!t)return-4;if(t.canvasSharedPtr&&(u()[t.canvasSharedPtr>>2]=r,u()[t.canvasSharedPtr+4>>2]=n),!t.offscreenCanvas&&t.controlTransferredOffscreen)return t.canvasSharedPtr?(function(e,r,n,t){cr(e,r=r?M(r):"",n,t)}(u()[t.canvasSharedPtr+8>>2],e,r,n),1):-4;t.offscreenCanvas&&(t=t.offscreenCanvas);var a=!1;if(t.GLctxObject&&t.GLctxObject.GLctx){var o=t.GLctxObject.GLctx.getParameter(2978);a=0===o[0]&&0===o[1]&&o[2]===t.width&&o[3]===t.height}return t.width=r,t.height=n,a&&t.GLctxObject.GLctx.viewport(0,0,r,n),0}function pr(e,r,n){return E?or(1,1,e,r,n):hr(e,r,n)}var vr,mr={counter:1,buffers:[],programs:[],framebuffers:[],renderbuffers:[],textures:[],shaders:[],vaos:[],contexts:{},offscreenCanvases:{},queries:[],stringCache:{},unpackAlignment:4,recordError:function(e){mr.lastError||(mr.lastError=e)},getNewId:function(e){for(var r=mr.counter++,n=e.length;n<r;n++)e[n]=null;return r},getSource:function(e,r,n,t){for(var a="",o=0;o<r;++o){var i=t?u()[t+4*o>>2]:-1;a+=M(u()[n+4*o>>2],i<0?void 0:i)}return a},createContext:function(e,r){e.getContextSafariWebGL2Fixed||(e.getContextSafariWebGL2Fixed=e.getContext,e.getContext=function(r,n){var t=e.getContextSafariWebGL2Fixed(r,n);return"webgl"==r==t instanceof WebGLRenderingContext?t:null});var n=e.getContext("webgl",r);return n?mr.registerContext(n,r):0},registerContext:function(e,r){var n=Or(8);u()[n+4>>2]=Mr();var t={handle:n,attributes:r,version:r.majorVersion,GLctx:e};return e.canvas&&(e.canvas.GLctxObject=t),mr.contexts[n]=t,(void 0===r.enableExtensionsByDefault||r.enableExtensionsByDefault)&&mr.initExtensions(t),n},makeContextCurrent:function(e){return mr.currentContext=mr.contexts[e],r.ctx=vr=mr.currentContext&&mr.currentContext.GLctx,!(e&&!vr)},getContext:function(e){return mr.contexts[e]},deleteContext:function(e){mr.currentContext===mr.contexts[e]&&(mr.currentContext=null),"object"==typeof sr&&sr.removeAllHandlersOnTarget(mr.contexts[e].GLctx.canvas),mr.contexts[e]&&mr.contexts[e].GLctx.canvas&&(mr.contexts[e].GLctx.canvas.GLctxObject=void 0),Sr(mr.contexts[e].handle),mr.contexts[e]=null},initExtensions:function(e){if(e||(e=mr.currentContext),!e.initExtensionsDone){e.initExtensionsDone=!0;var r,n=e.GLctx;!function(e){var r=e.getExtension("ANGLE_instanced_arrays");r&&(e.vertexAttribDivisor=function(e,n){r.vertexAttribDivisorANGLE(e,n)},e.drawArraysInstanced=function(e,n,t,a){r.drawArraysInstancedANGLE(e,n,t,a)},e.drawElementsInstanced=function(e,n,t,a,o){r.drawElementsInstancedANGLE(e,n,t,a,o)})}(n),function(e){var r=e.getExtension("OES_vertex_array_object");r&&(e.createVertexArray=function(){return r.createVertexArrayOES()},e.deleteVertexArray=function(e){r.deleteVertexArrayOES(e)},e.bindVertexArray=function(e){r.bindVertexArrayOES(e)},e.isVertexArray=function(e){return r.isVertexArrayOES(e)})}(n),function(e){var r=e.getExtension("WEBGL_draw_buffers");r&&(e.drawBuffers=function(e,n){r.drawBuffersWEBGL(e,n)})}(n),n.disjointTimerQueryExt=n.getExtension("EXT_disjoint_timer_query"),(r=n).multiDrawWebgl=r.getExtension("WEBGL_multi_draw"),(n.getSupportedExtensions()||[]).forEach((function(e){e.includes("lose_context")||e.includes("debug")||n.getExtension(e)}))}}},Er=["default","low-power","high-performance"],gr={mappings:{},buffers:[null,[],[]],printChar:function(e,r){var n=gr.buffers[e];0===r||10===r?((1===e?T:w)(D(n,0)),n.length=0):n.push(r)},varargs:void 0,get:function(){return gr.varargs+=4,u()[gr.varargs-4>>2]},getStr:function(e){return M(e)},get64:function(e,r){return e}};function _r(e){return E?or(2,1,e):0}function yr(e,r,n,t,a){if(E)return or(3,1,e,r,n,t,a)}function Tr(e,r,n,t){if(E)return or(4,1,e,r,n,t);for(var o=0,i=0;i<n;i++){for(var s=u()[r+8*i>>2],c=u()[r+(8*i+4)>>2],f=0;f<c;f++)gr.printChar(e,a()[s+f]);o+=c}return u()[t>>2]=o,0}function wr(e){if(E)throw"Internal Error! spawnThread() can only ever be called from main application thread!";var r=de.getNewWorker();if(!r)return 6;if(void 0!==r.pthread)throw"Internal error!";if(!e.pthread_ptr)throw"Internal error, no pthread ptr!";de.runningWorkers.push(r);for(var n=Or(512),t=0;t<128;++t)u()[n+4*t>>2]=0;var a=e.stackBase+e.stackSize,o=de.pthreads[e.pthread_ptr]={worker:r,stackBase:e.stackBase,stackSize:e.stackSize,allocatedOwnStack:e.allocatedOwnStack,threadInfoStruct:e.pthread_ptr},i=o.threadInfoStruct>>2;Atomics.store(s(),i+16,e.detached),Atomics.store(s(),i+25,n),Atomics.store(s(),i+10,o.threadInfoStruct),Atomics.store(s(),i+20,e.stackSize),Atomics.store(s(),i+19,a),Atomics.store(s(),i+26,e.stackSize),Atomics.store(s(),i+28,a),Atomics.store(s(),i+29,e.detached);var c=Dr()+40;Atomics.store(s(),i+43,c),r.pthread=o;var f={cmd:"run",start_routine:e.startRoutine,arg:e.arg,threadInfoStruct:e.pthread_ptr,stackBase:e.stackBase,stackSize:e.stackSize};return r.runPthread=function(){f.time=performance.now(),r.postMessage(f,e.transferList)},r.loaded&&(r.runPthread(),delete r.runPthread),0}function Ar(){if(E){var e=Mr();if(e&&!Atomics.load(s(),e+56>>2)&&2==Atomics.load(s(),e+0>>2))throw"Canceled!"}}E||de.initMainThreadBlock(),xe=r.InternalError=ke(Error,"InternalError"),function(){for(var e=new Array(256),r=0;r<256;++r)e[r]=String.fromCharCode(r);We=e}(),Ie=r.BindingError=ke(Error,"BindingError"),r.count_emval_handles=Ue,r.get_first_emval=je,Xe=r.UnboundTypeError=ke(Error,"UnboundTypeError");var Cr=[null,pr,_r,yr,Tr],br={d:function(e,r,n,t){ne("Assertion failed: "+M(e)+", at: "+[r?M(r):"unknown filename",n,t?M(t):"unknown function"])},O:function(e){return Or(e+me.SIZE)+me.SIZE},s:function(e,r){return n=e,t=r,void de.threadExitHandlers.push((function(){K.get(n)(t)}));var n,t},N:function(e,r,n){throw new Ee(e).init(r,n),e},u:function(e){var r=ge[e];delete ge[e];var n=r.rawConstructor,t=r.rawDestructor,a=r.fields;Re([e],a.map((function(e){return e.getterReturnType})).concat(a.map((function(e){return e.setterArgumentType}))),(function(e){var o={};return a.forEach((function(r,n){var t=r.fieldName,i=e[n],u=r.getter,s=r.getterContext,c=e[n+a.length],f=r.setter,l=r.setterContext;o[t]={read:function(e){return i.fromWireType(u(s,e))},write:function(e,r){var n=[];f(l,e,c.toWireType(n,r)),_e(n)}}})),[{name:r.name,fromWireType:function(e){var r={};for(var n in o)r[n]=o[n].read(e);return t(e),r},toWireType:function(e,r){for(var a in o)if(!(a in r))throw new TypeError('Missing field:  "'+a+'"');var i=n();for(a in o)o[a].write(i,r[a]);return null!==e&&e.push(t,i),i},argPackAdvance:8,readValueFromPointer:ye,destructorFunction:t}]}))},y:function(e,r,n,t,a){},L:function(e,r,t,a,i){var s=Ne(t);De(e,{name:r=Fe(r),fromWireType:function(e){return!!e},toWireType:function(e,r){return r?a:i},argPackAdvance:8,readValueFromPointer:function(e){var a;if(1===t)a=n();else if(2===t)a=o();else{if(4!==t)throw new TypeError("Unknown boolean type size: "+r);a=u()}return this.fromWireType(a[e>>s])},destructorFunction:null})},K:function(e,r){De(e,{name:r=Fe(r),fromWireType:function(e){var r=He[e].value;return Be(e),r},toWireType:function(e,r){return Ge(r)},argPackAdvance:8,readValueFromPointer:ye,destructorFunction:null})},p:function(e,r,n){var t=Ne(n);De(e,{name:r=Fe(r),fromWireType:function(e){return e},toWireType:function(e,r){if("number"!=typeof r&&"boolean"!=typeof r)throw new TypeError('Cannot convert "'+Ve(r)+'" to '+this.name);return r},argPackAdvance:8,readValueFromPointer:qe(r,t),destructorFunction:null})},t:function(e,n,t,a,o,i){var s=function(e,r){for(var n=[],t=0;t<e;t++)n.push(u()[(r>>2)+t]);return n}(n,t);e=Fe(e),o=Qe(a,o),Ye(e,(function(){!function(e,r){var n=[],t={};throw r.forEach((function e(r){t[r]||we[r]||(Ae[r]?Ae[r].forEach(e):(n.push(r),t[r]=!0))})),new Xe(e+": "+n.map(Ze).join([", "]))}("Cannot call "+e+" due to unbound types",s)}),n-1),Re([],s,(function(t){var a=[t[0],null].concat(t.slice(1));return function(e,n,t){r.hasOwnProperty(e)||Pe("Replacing nonexistant public symbol"),void 0!==r[e].overloadTable&&void 0!==t?r[e].overloadTable[t]=n:(r[e]=n,r[e].argCount=t)}(e,ze(e,a,null,o,i),n-1),[]}))},e:function(e,r,n,t,a){r=Fe(r),-1===a&&(a=4294967295);var o=Ne(n),i=function(e){return e};if(0===t){var u=32-8*n;i=function(e){return e<<u>>>u}}var s=r.includes("unsigned");De(e,{name:r,fromWireType:i,toWireType:function(e,n){if("number"!=typeof n&&"boolean"!=typeof n)throw new TypeError('Cannot convert "'+Ve(n)+'" to '+this.name);if(n<t||n>a)throw new TypeError('Passing a number "'+Ve(n)+'" from JS side to C/C++ side to an argument of type "'+r+'", which is outside the valid range ['+t+", "+a+"]!");return s?n>>>0:0|n},argPackAdvance:8,readValueFromPointer:Je(r,o,0!==t),destructorFunction:null})},b:function(e,r,n){var t=[Int8Array,Uint8Array,Int16Array,Uint16Array,Int32Array,Uint32Array,Float32Array,Float64Array][r];function a(e){e>>=2;var r=s(),n=r[e],a=r[e+1];return new t(S,a,n)}De(e,{name:n=Fe(n),fromWireType:a,argPackAdvance:8,readValueFromPointer:a},{ignoreDuplicateRegistrations:!0})},q:function(e,r){var n="std::string"===(r=Fe(r));De(e,{name:r,fromWireType:function(e){var r,t=s()[e>>2];if(n)for(var o=e+4,i=0;i<=t;++i){var u=e+4+i;if(i==t||0==a()[u]){var c=M(o,u-o);void 0===r?r=c:(r+=String.fromCharCode(0),r+=c),o=u+1}}else{var f=new Array(t);for(i=0;i<t;++i)f[i]=String.fromCharCode(a()[e+4+i]);r=f.join("")}return Sr(e),r},toWireType:function(e,r){r instanceof ArrayBuffer&&(r=new Uint8Array(r));var t="string"==typeof r;t||r instanceof Uint8Array||r instanceof Uint8ClampedArray||r instanceof Int8Array||Le("Cannot pass non-string to std::string");var o=(n&&t?function(){return B(r)}:function(){return r.length})(),i=Or(4+o+1);if(s()[i>>2]=o,n&&t)H(r,i+4,o+1);else if(t)for(var u=0;u<o;++u){var c=r.charCodeAt(u);c>255&&(Sr(i),Le("String has UTF-16 code units that do not fit in 8 bits")),a()[i+4+u]=c}else for(u=0;u<o;++u)a()[i+4+u]=r[u];return null!==e&&e.push(Sr,i),i},argPackAdvance:8,readValueFromPointer:ye,destructorFunction:function(e){Sr(e)}})},j:function(e,r,n){var t,a,o,u,c;n=Fe(n),2===r?(t=U,a=j,u=G,o=function(){return i()},c=1):4===r&&(t=V,a=q,u=z,o=function(){return s()},c=2),De(e,{name:n,fromWireType:function(e){for(var n,a=s()[e>>2],i=o(),u=e+4,f=0;f<=a;++f){var l=e+4+f*r;if(f==a||0==i[l>>c]){var d=t(u,l-u);void 0===n?n=d:(n+=String.fromCharCode(0),n+=d),u=l+r}}return Sr(e),n},toWireType:function(e,t){"string"!=typeof t&&Le("Cannot pass non-string to C++ string type "+n);var o=u(t),i=Or(4+o+r);return s()[i>>2]=o>>c,a(t,i+4,o+r),null!==e&&e.push(Sr,i),i},argPackAdvance:8,readValueFromPointer:ye,destructorFunction:function(e){Sr(e)}})},v:function(e,r,n,t,a,o){ge[e]={name:Fe(r),rawConstructor:Qe(n,t),rawDestructor:Qe(a,o),fields:[]}},c:function(e,r,n,t,a,o,i,u,s,c){ge[e].fields.push({fieldName:Fe(r),getterReturnType:n,getter:Qe(t,a),getterContext:o,setterArgumentType:i,setter:Qe(u,s),setterContext:c})},M:function(e,r){De(e,{isVoid:!0,name:r=Fe(r),argPackAdvance:0,fromWireType:function(){},toWireType:function(e,r){}})},G:function(e,r){if(e==r)postMessage({cmd:"processQueuedMainThreadWork"});else if(E)postMessage({targetThread:e,cmd:"processThreadQueue"});else{var n=de.pthreads[e],t=n&&n.worker;if(!t)return;t.postMessage({cmd:"processThreadQueue"})}return 1},m:Be,J:function(e){return 0===e?Ge(er()):(e=void 0===(n=$e[r=e])?Fe(r):n,Ge(er()[e]));var r,n},Q:function(e){e>4&&(He[e].refcount+=1)},w:function(e,n,t,a){e=function(e){return e||Le("Cannot use deleted val. handle = "+e),He[e].value}(e);var o=nr[n];return o||(o=function(e){for(var n="",t=0;t<e;++t)n+=(0!==t?", ":"")+"arg"+t;var a="return function emval_allocator_"+e+"(constructor, argTypes, args) {\n";for(t=0;t<e;++t)a+="var argType"+t+" = requireRegisteredType(Module['HEAP32'][(argTypes >>> 2) + "+t+'], "parameter '+t+'");\nvar arg'+t+" = argType"+t+".readValueFromPointer(args);\nargs += argType"+t+"['argPackAdvance'];\n";return a+="var obj = new constructor("+n+");\nreturn __emval_register(obj);\n}\n",new Function("requireRegisteredType","Module","__emval_register",a)(rr,r,Ge)}(n),nr[n]=o),o(e,t,a)},k:function(){ne()},o:function(e,r,n){var t=function(e,r){var n;for(tr.length=0,r>>=2;n=a()[e++];){var t=n<105;t&&1&r&&r++,tr.push(t?c()[r++>>1]:u()[r]),++r}return tr}(r,n);return ue[e].apply(null,t)},C:function(){},n:function(e,r){},f:ar,g:fe,h:he,A:function(e,r,n){a().copyWithin(e,r,r+n)},R:function(){return navigator.hardwareConcurrency},D:function(e,r,n){ir.length=r;for(var t=n>>3,a=0;a<r;a++)ir[a]=c()[t+a];return(e<0?ue[-e-1]:Cr[e]).apply(null,ir)},B:function(e){var r=a().length;if((e>>>=0)<=r)return!1;var n,t,o=2147483648;if(e>o)return!1;for(var i=1;i<=4;i*=2){var u=r*(1+.2/i);if(u=Math.min(u,e+100663296),ur(Math.min(o,((n=Math.max(e,u))%(t=65536)>0&&(n+=t-n%t),n))))return!0}return!1},E:function(e,r,n){return dr(e)?hr(e,r,n):pr(e,r,n)},l:function(e){},F:function(e,r){return n=e,t=r>>2,a=u()[t+6],o={alpha:!!u()[t+0],depth:!!u()[t+1],stencil:!!u()[t+2],antialias:!!u()[t+3],premultipliedAlpha:!!u()[t+4],preserveDrawingBuffer:!!u()[t+5],powerPreference:Er[a],failIfMajorPerformanceCaveat:!!u()[t+7],majorVersion:u()[t+8],minorVersion:u()[t+9],enableExtensionsByDefault:u()[t+10],explicitSwapControl:u()[t+11],proxyContextToMainThread:u()[t+12],renderViaOffscreenBackBuffer:u()[t+13]},(i=dr(n))?o.explicitSwapControl?0:mr.createContext(i,o):0;var n,t,a,o,i},I:_r,x:yr,H:Tr,z:function(){de.initRuntime()},a:A||r.wasmMemory,r:function(e,r,n,t){if("undefined"==typeof SharedArrayBuffer)return w("Current environment does not support SharedArrayBuffer, pthreads are not available!"),6;if(!e)return w("pthread_create called with a null thread pointer!"),28;var a=[];if(E&&0===a.length)return Nr(687865856,e,r,n,t);var o=0,i=0,c=0;r&&-1!=r?(o=u()[r>>2],o+=81920,i=u()[r+8>>2],c=0!==u()[r+12>>2]):o=2097152;var f=0==i;f?i=Vr(16,o):L((i-=o)>0);for(var l=Or(228),d=0;d<57;++d)s()[(l>>2)+d]=0;u()[e>>2]=l,u()[l+12>>2]=l;var h=l+152;u()[h>>2]=h;var p={stackBase:i,stackSize:o,allocatedOwnStack:f,detached:c,startRoutine:n,pthread_ptr:l,arg:t,transferList:a};return E?(p.cmd="spawnThread",postMessage(p,a),0):wr(p)},P:function(e,r){return function(e,r,n){if(!e)return w("pthread_join attempted on a null thread pointer!"),ce.ESRCH;if(E&&Mr()==e)return w("PThread "+e+" is attempting to join to itself!"),ce.EDEADLK;if(!E&&Pr()==e)return w("Main thread "+e+" is attempting to join to itself!"),ce.EDEADLK;if(u()[e+12>>2]!==e)return w("pthread_join attempted on thread "+e+", which does not point to a valid thread, or does not exist anymore!"),ce.ESRCH;if(Atomics.load(s(),e+64>>2))return w("Attempted to join thread "+e+", which was already detached!"),ce.EINVAL;for(;;){var t=Atomics.load(s(),e+0>>2);if(1==t){var a=Atomics.load(s(),e+4>>2);return r&&(u()[r>>2]=a),Atomics.store(s(),e+64>>2,1),E?postMessage({cmd:"cleanupThread",thread:e}):le(e),0}if(!n)return ce.EBUSY;Ar(),E||Wr(),ar(e+0,t,E?100:1)}}(e,r,!0)},i:function(e){}};!function(){var e={a:br};function n(e,n){var t,a=e.exports;if(r.asm=a,K=r.asm.W,t=r.asm.S,Z.unshift(t),de.tlsInitFunctions.push(r.asm.V),C=n,!E){var o=de.unusedWorkers.length;de.unusedWorkers.forEach((function(e){de.loadWasmModuleToWorker(e,(function(){--o||function(){if(ee--,r.monitorRunDependencies&&r.monitorRunDependencies(ee),0==ee&&re){var e=re;re=null,e()}}()}))}))}}function t(e){n(e.instance,e.module)}function a(r){return(!y&&m&&"function"==typeof fetch?fetch(oe,{credentials:"same-origin"}).then((function(e){if(!e.ok)throw"failed to load wasm binary file at '"+oe+"'";return e.arrayBuffer()})).catch((function(){return ie(oe)})):Promise.resolve().then((function(){return ie(oe)}))).then((function(r){return WebAssembly.instantiate(r,e)})).then(r,(function(e){w("failed to asynchronously prepare wasm: "+e),ne(e)}))}if(E||(L(!E,"addRunDependency cannot be used in a pthread worker"),ee++,r.monitorRunDependencies&&r.monitorRunDependencies(ee)),r.instantiateWasm)try{return r.instantiateWasm(e,n)}catch(e){return w("Module.instantiateWasm callback failed with error: "+e),!1}(y||"function"!=typeof WebAssembly.instantiateStreaming||ae(oe)||"function"!=typeof fetch?a(t):fetch(oe,{credentials:"same-origin"}).then((function(r){return WebAssembly.instantiateStreaming(r,e).then(t,(function(e){return w("wasm streaming compile failed: "+e),w("falling back to ArrayBuffer instantiation"),a(t)}))}))).catch(l)}(),r.___wasm_call_ctors=function(){return(r.___wasm_call_ctors=r.asm.S).apply(null,arguments)};var Sr=r._free=function(){return(Sr=r._free=r.asm.T).apply(null,arguments)},Or=r._malloc=function(){return(Or=r._malloc=r.asm.U).apply(null,arguments)};r._emscripten_tls_init=function(){return(r._emscripten_tls_init=r.asm.V).apply(null,arguments)};var kr=r.___getTypeName=function(){return(kr=r.___getTypeName=r.asm.X).apply(null,arguments)};r.___embind_register_native_and_builtin_types=function(){return(r.___embind_register_native_and_builtin_types=r.asm.Y).apply(null,arguments)},r._emscripten_current_thread_process_queued_calls=function(){return(r._emscripten_current_thread_process_queued_calls=r.asm.Z).apply(null,arguments)};var xr=r._emscripten_register_main_browser_thread_id=function(){return(xr=r._emscripten_register_main_browser_thread_id=r.asm._).apply(null,arguments)},Pr=r._emscripten_main_browser_thread_id=function(){return(Pr=r._emscripten_main_browser_thread_id=r.asm.$).apply(null,arguments)},Rr=r.__emscripten_do_dispatch_to_thread=function(){return(Rr=r.__emscripten_do_dispatch_to_thread=r.asm.aa).apply(null,arguments)},Nr=r._emscripten_sync_run_in_main_thread_4=function(){return(Nr=r._emscripten_sync_run_in_main_thread_4=r.asm.ba).apply(null,arguments)},Wr=r._emscripten_main_thread_process_queued_calls=function(){return(Wr=r._emscripten_main_thread_process_queued_calls=r.asm.ca).apply(null,arguments)},Fr=r._emscripten_run_in_main_runtime_thread_js=function(){return(Fr=r._emscripten_run_in_main_runtime_thread_js=r.asm.da).apply(null,arguments)},Ir=r.__emscripten_call_on_thread=function(){return(Ir=r.__emscripten_call_on_thread=r.asm.ea).apply(null,arguments)},Lr=r.__emscripten_thread_init=function(){return(Lr=r.__emscripten_thread_init=r.asm.fa).apply(null,arguments)},Dr=r._emscripten_get_global_libc=function(){return(Dr=r._emscripten_get_global_libc=r.asm.ga).apply(null,arguments)},Mr=r._pthread_self=function(){return(Mr=r._pthread_self=r.asm.ha).apply(null,arguments)},Hr=r.___pthread_tsd_run_dtors=function(){return(Hr=r.___pthread_tsd_run_dtors=r.asm.ia).apply(null,arguments)},Br=r.stackSave=function(){return(Br=r.stackSave=r.asm.ja).apply(null,arguments)},Ur=r.stackRestore=function(){return(Ur=r.stackRestore=r.asm.ka).apply(null,arguments)},jr=r.stackAlloc=function(){return(jr=r.stackAlloc=r.asm.la).apply(null,arguments)},Gr=r._emscripten_stack_set_limits=function(){return(Gr=r._emscripten_stack_set_limits=r.asm.ma).apply(null,arguments)},Vr=r._memalign=function(){return(Vr=r._memalign=r.asm.na).apply(null,arguments)};r.dynCall_jiii=function(){return(r.dynCall_jiii=r.asm.oa).apply(null,arguments)},r.dynCall_jiiiiii=function(){return(r.dynCall_jiiiiii=r.asm.pa).apply(null,arguments)},r.dynCall_jiji=function(){return(r.dynCall_jiji=r.asm.qa).apply(null,arguments)};var qr,zr=r.__emscripten_allow_main_runtime_queued_calls=112520,Yr=r.__emscripten_main_thread_futex=123420;function Kr(e){this.name="ExitStatus",this.message="Program terminated with exit("+e+")",this.status=e}function Qr(e){if(!(ee>0)){if(E)return f(r),$(),void postMessage({cmd:"loaded"});!function(){if(!E){if(r.preRun)for("function"==typeof r.preRun&&(r.preRun=[r.preRun]);r.preRun.length;)e=r.preRun.shift(),X.unshift(e);var e;se(X)}}(),ee>0||(r.setStatus?(r.setStatus("Running..."),setTimeout((function(){setTimeout((function(){r.setStatus("")}),1),n()}),1)):n())}function n(){qr||(qr=!0,r.calledRun=!0,I||($(),f(r),r.onRuntimeInitialized&&r.onRuntimeInitialized(),function(){if(!E){if(r.postRun)for("function"==typeof r.postRun&&(r.postRun=[r.postRun]);r.postRun.length;)e=r.postRun.shift(),J.unshift(e);var e;se(J)}}()))}}if(r.PThread=de,r.PThread=de,r.wasmMemory=A,r.ExitStatus=Kr,re=function e(){qr||Qr(),qr||(re=e)},r.run=Qr,r.preInit)for("function"==typeof r.preInit&&(r.preInit=[r.preInit]);r.preInit.length>0;)r.preInit.pop()();return E&&(b=!1,de.initWorker()),Qr(),r.ready});n.default=a}));
