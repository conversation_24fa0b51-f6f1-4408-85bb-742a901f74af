define(["module","require","exports"],(function(e,r,n){var t,a=(t=e.uri,function(r){function n(){return k.buffer!=R&&K(k.buffer),L}function a(){return k.buffer!=R&&K(k.buffer),M}function o(){return k.buffer!=R&&K(k.buffer),H}function i(){return k.buffer!=R&&K(k.buffer),O}function u(){return k.buffer!=R&&K(k.buffer),B}function c(){return k.buffer!=R&&K(k.buffer),D}function s(){return k.buffer!=R&&K(k.buffer),G}var f,l;(r=void 0!==(r=r||{})?r:{}).ready=new Promise((function(e,r){f=e,l=r}));var d,p={};for(d in r)r.hasOwnProperty(d)&&(p[d]=r[d]);var h,v=function(e,r){throw r},m=!0,g=r.ENVIRONMENT_IS_PTHREAD||!1,y="";function _(e){return r.locateFile?r.locateFile(e,y):y+e}y=self.location.href,t&&(y=t),y=0!==y.indexOf("blob:")?y.substr(0,y.replace(/[?#].*/,"").lastIndexOf("/")+1):"",h=function(e){var r=new XMLHttpRequest;return r.open("GET",e,!1),r.responseType="arraybuffer",r.send(null),new Uint8Array(r.response)};var w=r.print||console.log.bind(console),b=r.printErr||console.warn.bind(console);for(d in p)p.hasOwnProperty(d)&&(r[d]=p[d]);p=null,r.arguments&&r.arguments,r.thisProgram&&r.thisProgram,r.quit&&(v=r.quit);var T,A=0;r.wasmBinary&&(T=r.wasmBinary);var k,x,C=r.noExitRuntime||!0;"object"!=typeof WebAssembly&&ce("no native wasm support detected");var E,W=!1;function S(e){var r=new TextDecoder(e);this.decode=function(e){return e.buffer instanceof SharedArrayBuffer&&(e=new Uint8Array(e)),r.decode.call(r,e)}}var P=new S("utf8");function j(e,r){if(!e)return"";for(var n=e+r,t=e;!(t>=n)&&a()[t];)++t;return P.decode(a().subarray(e,t))}function F(e,r,n){return function(e,r,n,t){if(!(t>0))return 0;for(var a=n,o=n+t-1,i=0;i<e.length;++i){var u=e.charCodeAt(i);if(u>=55296&&u<=57343&&(u=65536+((1023&u)<<10)|1023&e.charCodeAt(++i)),u<=127){if(n>=o)break;r[n++]=u}else if(u<=2047){if(n+1>=o)break;r[n++]=192|u>>6,r[n++]=128|63&u}else if(u<=65535){if(n+2>=o)break;r[n++]=224|u>>12,r[n++]=128|u>>6&63,r[n++]=128|63&u}else{if(n+3>=o)break;r[n++]=240|u>>18,r[n++]=128|u>>12&63,r[n++]=128|u>>6&63,r[n++]=128|63&u}}return r[n]=0,n-a}(e,a(),r,n)}function I(e){for(var r=0,n=0;n<e.length;++n){var t=e.charCodeAt(n);t>=55296&&t<=57343&&(t=65536+((1023&t)<<10)|1023&e.charCodeAt(++n)),t<=127?++r:r+=t<=2047?2:t<=65535?3:4}return r}var R,L,M,H,O,B,D,U,G,V=new S("utf-16le");function N(e,r){for(var n=e,t=n>>1,o=t+r/2;!(t>=o)&&i()[t];)++t;return n=t<<1,V.decode(a().subarray(e,n))}function q(e,r,n){if(void 0===n&&(n=2147483647),n<2)return 0;for(var t=r,a=(n-=2)<2*e.length?n/2:e.length,i=0;i<a;++i){var u=e.charCodeAt(i);o()[r>>1]=u,r+=2}return o()[r>>1]=0,r-t}function z(e){return 2*e.length}function Q(e,r){for(var n=0,t="";!(n>=r/4);){var a=u()[e+4*n>>2];if(0==a)break;if(++n,a>=65536){var o=a-65536;t+=String.fromCharCode(55296|o>>10,56320|1023&o)}else t+=String.fromCharCode(a)}return t}function J(e,r,n){if(void 0===n&&(n=2147483647),n<4)return 0;for(var t=r,a=t+n-4,o=0;o<e.length;++o){var i=e.charCodeAt(o);if(i>=55296&&i<=57343&&(i=65536+((1023&i)<<10)|1023&e.charCodeAt(++o)),u()[r>>2]=i,(r+=4)+4>a)break}return u()[r>>2]=0,r-t}function X(e){for(var r=0,n=0;n<e.length;++n){var t=e.charCodeAt(n);t>=55296&&t<=57343&&++n,r+=4}return r}function K(e){R=e,r.HEAP8=L=new Int8Array(e),r.HEAP16=H=new Int16Array(e),r.HEAP32=B=new Int32Array(e),r.HEAPU8=M=new Uint8Array(e),r.HEAPU16=O=new Uint16Array(e),r.HEAPU32=D=new Uint32Array(e),r.HEAPF32=U=new Float32Array(e),r.HEAPF64=G=new Float64Array(e)}g&&(R=r.buffer);var Y,Z=r.INITIAL_MEMORY||16777216;if(g)k=r.wasmMemory,R=r.buffer;else if(r.wasmMemory)k=r.wasmMemory;else if(!((k=new WebAssembly.Memory({initial:Z/65536,maximum:32768,shared:!0})).buffer instanceof SharedArrayBuffer))throw b("requested a shared WebAssembly.Memory but the returned buffer is not a SharedArrayBuffer, indicating that while the browser has SharedArrayBuffer it does not have WebAssembly threads support - you may need to set a flag"),Error("bad memory");k&&(R=k.buffer),Z=R.byteLength,K(R);var $=[],ee=[],re=[],ne=!1,te=0;function ae(){return C||te>0}function oe(){g||he(ee)}var ie=0,ue=null;function ce(e){g?postMessage({cmd:"onAbort",arg:e}):r.onAbort&&r.onAbort(e),b(e="Aborted("+e+")"),W=!0,E=1,e+=". Build with -s ASSERTIONS=1 for more info.";var n=new WebAssembly.RuntimeError(e);throw l(n),n}r.preloadedImages={},r.preloadedAudios={};var se,fe="data:application/octet-stream;base64,";function le(e){return e.startsWith(fe)}function de(e){try{if(e==se&&T)return new Uint8Array(T);if(h)return h(e);throw"both async and sync fetching of the wasm failed"}catch(e){ce(e)}}r.locateFile?le(se="avif_enc_mt.wasm")||(se=_(se)):se=new URL("/c/avif_enc_mt-9d34100e.wasm",e.uri).toString();var pe={};function he(e){for(;e.length>0;){var n=e.shift();if("function"!=typeof n){var t=n.func;"number"==typeof t?void 0===n.arg?xe(t)():xe(t)(n.arg):t(void 0===n.arg?null:n.arg)}else n(r)}}function ve(e){var r=Xr(),n=e();return Kr(r),n}function me(e,r){if(e<=0||e>n().length||!0&e||r<0)return-28;if(0==r)return 0;r>=2147483647&&(r=1/0);var t=Atomics.load(u(),an>>2),a=0;if(t==e&&Atomics.compareExchange(u(),an>>2,t,0)==t&&(a=1,--r<=0))return 1;var o=Atomics.notify(u(),e>>2,r);if(o>=0)return o+a;throw"Atomics.notify returned an unexpected value "+o}function ge(e){var r=be.pthreads[e];if(r){u()[e+8>>2]=0;var n=r.worker;be.returnWorkerToPool(n)}}function ye(e){e&&(e.threadInfoStruct&&Or(e.threadInfoStruct),e.threadInfoStruct=0,e.allocatedOwnStack&&e.stackBase&&Or(e.stackBase),e.stackBase=0,e.worker&&(e.worker.pthread=null))}function _e(e){!function(e,n){if(E=e,!n&&g)throw Te(e),"unwind";var t;ae()||g||(be.terminateAllThreads(),ne=!0),E=t=e,ae()||(be.terminateAllThreads(),r.onExit&&r.onExit(t),W=!0),v(t,new on(t))}(e)}function we(e){if(e instanceof on||"unwind"==e)return E;v(1,e)}r._emscripten_futex_wake=me,r._exit=_e;var be={unusedWorkers:[],runningWorkers:[],tlsInitFunctions:[],initMainThreadBlock:function(){for(var e=navigator.hardwareConcurrency,r=0;r<e;++r)be.allocateUnusedWorker()},initWorker:function(){},pthreads:{},setExitStatus:function(e){E=e},terminateAllThreads:function(){for(var e in be.pthreads){var r=be.pthreads[e];r&&r.worker&&be.returnWorkerToPool(r.worker)}for(var n=0;n<be.unusedWorkers.length;++n)be.unusedWorkers[n].terminate();be.unusedWorkers=[]},returnWorkerToPool:function(e){be.runWithoutMainThreadQueuedCalls((function(){delete be.pthreads[e.pthread.threadInfoStruct],be.unusedWorkers.push(e),be.runningWorkers.splice(be.runningWorkers.indexOf(e),1),ye(e.pthread),e.pthread=void 0}))},runWithoutMainThreadQueuedCalls:function(e){u()[tn>>2]=0;try{e()}finally{u()[tn>>2]=1}},receiveObjectTransfer:function(e){},threadInit:function(){for(var e in be.tlsInitFunctions)be.tlsInitFunctions[e]()},loadWasmModuleToWorker:function(e,n){e.onmessage=function(t){var a,o=t.data,i=o.cmd;if(e.pthread&&(be.currentProxiedOperationCallerThread=e.pthread.threadInfoStruct),o.targetThread&&o.targetThread!=zr()){var c=be.pthreads[o.targetThread];return c?c.worker.postMessage(o,o.transferList):b('Internal error! Worker sent a message "'+i+'" to target pthread '+o.targetThread+", but that thread no longer exists!"),void(be.currentProxiedOperationCallerThread=void 0)}"processQueuedMainThreadWork"===i?Gr():"spawnThread"===i?Ce(o):"cleanupThread"===i?ge(o.thread):"killThread"===i?function(e){u()[e+8>>2]=0;var r=be.pthreads[e];delete be.pthreads[e],r.worker.terminate(),ye(r),be.runningWorkers.splice(be.runningWorkers.indexOf(r.worker),1),r.worker.pthread=void 0}(o.thread):"cancelThread"===i?(a=o.thread,be.pthreads[a].worker.postMessage({cmd:"cancel"})):"loaded"===i?(e.loaded=!0,n&&n(e),e.runPthread&&(e.runPthread(),delete e.runPthread)):"print"===i?w("Thread "+o.threadId+": "+o.text):"printErr"===i?b("Thread "+o.threadId+": "+o.text):"alert"===i?alert("Thread "+o.threadId+": "+o.text):"detachedExit"===i||"cancelDone"===i?be.returnWorkerToPool(e):"setimmediate"===o.target?e.postMessage(o):"onAbort"===i?r.onAbort&&r.onAbort(o.arg):b("worker sent an unknown command "+i),be.currentProxiedOperationCallerThread=void 0},e.onerror=function(e){throw b("pthread sent an error! "+e.filename+":"+e.lineno+": "+e.message),e},e.postMessage({cmd:"load",urlOrBlob:r.mainScriptUrlOrBlob,wasmMemory:k,wasmModule:x})},allocateUnusedWorker:function(){if(r.locateFile){var n=_("avif_enc_mt.worker.js");be.unusedWorkers.push(new Worker(n))}else be.unusedWorkers.push(new Worker(new URL("/c/avif_enc_mt.worker-d6f56392.js",e.uri)))},getNewWorker:function(){return 0==be.unusedWorkers.length&&(be.allocateUnusedWorker(),be.loadWasmModuleToWorker(be.unusedWorkers[0])),be.unusedWorkers.pop()}};function Te(e){if(g)return gr(1,0,e);try{_e(e)}catch(e){we(e)}}r.establishStackSpace=function(e,r){Zr(e,r),Kr(e)};var Ae,ke=[];function xe(e){var r=ke[e];return r||(e>=ke.length&&(ke.length=e+1),ke[e]=r=Y.get(e)),r}function Ce(e){var r=be.getNewWorker();if(!r)return 6;be.runningWorkers.push(r);var n=e.stackBase+e.stackSize,t=be.pthreads[e.pthread_ptr]={worker:r,stackBase:e.stackBase,stackSize:e.stackSize,allocatedOwnStack:e.allocatedOwnStack,threadInfoStruct:e.pthread_ptr},a=t.threadInfoStruct>>2;Atomics.store(c(),a+15,e.detached),Atomics.store(c(),a+19,e.stackSize),Atomics.store(c(),a+18,n),Atomics.store(c(),a+25,e.stackSize),Atomics.store(c(),a+27,n),Atomics.store(c(),a+28,e.detached),r.pthread=t;var o={cmd:"run",start_routine:e.startRoutine,arg:e.arg,threadInfoStruct:e.pthread_ptr,stackBase:e.stackBase,stackSize:e.stackSize};return r.runPthread=function(){o.time=performance.now(),r.postMessage(o,e.transferList)},r.loaded&&(r.runPthread(),delete r.runPthread),0}function Ee(e,r,t){if(e<=0||e>n().length||!0&e)return-28;var a=Atomics.wait(u(),e>>2,r,t);if("timed-out"===a)return-73;if("not-equal"===a)return-6;if("ok"===a)return 0;throw"Atomics.wait returned an unexpected value "+a}r.invokeEntryPoint=function(e,r){return xe(e)(r)},Ae=g?function(){return performance.now()-r.__performance_now_clock_drift}:function(){return performance.now()};var We={mappings:{},buffers:[null,[],[]],printChar:function(e,r){var n=We.buffers[e];0===r||10===r?((1===e?w:b)(function(e,r,n){for(var t=r+n,a=r;e[a]&&!(a>=t);)++a;return P.decode(e.subarray?e.subarray(r,a):new Uint8Array(e.slice(r,a)))}(n,0)),n.length=0):n.push(r)},varargs:void 0,get:function(){return We.varargs+=4,u()[We.varargs-4>>2]},getStr:function(e){return j(e)},get64:function(e,r){return e}};function Se(e,r,n){return g?gr(3,1,e,r,n):(We.varargs=n,0)}function Pe(e,r,n){return g?gr(4,1,e,r,n):(We.varargs=n,0)}function je(e,r,n){if(g)return gr(5,1,e,r,n);We.varargs=n}var Fe={};function Ie(e){for(;e.length;){var r=e.pop();e.pop()(r)}}function Re(e){return this.fromWireType(c()[e>>2])}var Le={},Me={},He={},Oe=48,Be=57;function De(e){if(void 0===e)return"_unknown";var r=(e=e.replace(/[^a-zA-Z0-9_]/g,"$")).charCodeAt(0);return r>=Oe&&r<=Be?"_"+e:e}function Ue(e,r){return e=De(e),new Function("body","return function "+e+'() {\n    "use strict";    return body.apply(this, arguments);\n};\n')(r)}function Ge(e,r){var n=Ue(r,(function(e){this.name=r,this.message=e;var n=new Error(e).stack;void 0!==n&&(this.stack=this.toString()+"\n"+n.replace(/^Error(:[^\n]*)?\n/,""))}));return n.prototype=Object.create(e.prototype),n.prototype.constructor=n,n.prototype.toString=function(){return void 0===this.message?this.name:this.name+": "+this.message},n}var Ve=void 0;function Ne(e){throw new Ve(e)}function qe(e,r,n){function t(r){var t=n(r);t.length!==e.length&&Ne("Mismatched type converter count");for(var a=0;a<e.length;++a)Ye(e[a],t[a])}e.forEach((function(e){He[e]=r}));var a=new Array(r.length),o=[],i=0;r.forEach((function(e,r){Me.hasOwnProperty(e)?a[r]=Me[e]:(o.push(e),Le.hasOwnProperty(e)||(Le[e]=[]),Le[e].push((function(){a[r]=Me[e],++i===o.length&&t(a)})))})),0===o.length&&t(a)}function ze(e){switch(e){case 1:return 0;case 2:return 1;case 4:return 2;case 8:return 3;default:throw new TypeError("Unknown type size: "+e)}}var Qe=void 0;function Je(e){for(var r="",n=e;a()[n];)r+=Qe[a()[n++]];return r}var Xe=void 0;function Ke(e){throw new Xe(e)}function Ye(e,r,n){if(n=n||{},!("argPackAdvance"in r))throw new TypeError("registerType registeredInstance requires argPackAdvance");var t=r.name;if(e||Ke('type "'+t+'" must have a positive integer typeid pointer'),Me.hasOwnProperty(e)){if(n.ignoreDuplicateRegistrations)return;Ke("Cannot register type '"+t+"' twice")}if(Me[e]=r,delete He[e],Le.hasOwnProperty(e)){var a=Le[e];delete Le[e],a.forEach((function(e){e()}))}}var Ze=[],$e=[{},{value:void 0},{value:null},{value:!0},{value:!1}];function er(e){e>4&&0==--$e[e].refcount&&($e[e]=void 0,Ze.push(e))}function rr(){for(var e=0,r=5;r<$e.length;++r)void 0!==$e[r]&&++e;return e}function nr(){for(var e=5;e<$e.length;++e)if(void 0!==$e[e])return $e[e];return null}var tr={toValue:function(e){return e||Ke("Cannot use deleted val. handle = "+e),$e[e].value},toHandle:function(e){switch(e){case void 0:return 1;case null:return 2;case!0:return 3;case!1:return 4;default:var r=Ze.length?Ze.pop():$e.length;return $e[r]={refcount:1,value:e},r}}};function ar(e){if(null===e)return"null";var r=typeof e;return"object"===r||"array"===r||"function"===r?e.toString():""+e}function or(e,r){switch(r){case 2:return function(e){return this.fromWireType((k.buffer!=R&&K(k.buffer),U)[e>>2])};case 3:return function(e){return this.fromWireType(s()[e>>3])};default:throw new TypeError("Unknown float type: "+e)}}function ir(e,r,n,t,a){var o=r.length;o<2&&Ke("argTypes array size mismatch! Must at least get return value and 'this' types!");for(var i=null!==r[1]&&null!==n,u=!1,c=1;c<r.length;++c)if(null!==r[c]&&void 0===r[c].destructorFunction){u=!0;break}var s="void"!==r[0].name,f="",l="";for(c=0;c<o-2;++c)f+=(0!==c?", ":"")+"arg"+c,l+=(0!==c?", ":"")+"arg"+c+"Wired";var d="return function "+De(e)+"("+f+") {\nif (arguments.length !== "+(o-2)+") {\nthrowBindingError('function "+e+" called with ' + arguments.length + ' arguments, expected "+(o-2)+" args!');\n}\n";u&&(d+="var destructors = [];\n");var p=u?"destructors":"null",h=["throwBindingError","invoker","fn","runDestructors","retType","classParam"],v=[Ke,t,a,Ie,r[0],r[1]];for(i&&(d+="var thisWired = classParam.toWireType("+p+", this);\n"),c=0;c<o-2;++c)d+="var arg"+c+"Wired = argType"+c+".toWireType("+p+", arg"+c+"); // "+r[c+2].name+"\n",h.push("argType"+c),v.push(r[c+2]);if(i&&(l="thisWired"+(l.length>0?", ":"")+l),d+=(s?"var rv = ":"")+"invoker(fn"+(l.length>0?", ":"")+l+");\n",u)d+="runDestructors(destructors);\n";else for(c=i?1:2;c<r.length;++c){var m=1===c?"thisWired":"arg"+(c-2)+"Wired";null!==r[c].destructorFunction&&(d+=m+"_dtor("+m+"); // "+r[c].name+"\n",h.push(m+"_dtor"),v.push(r[c].destructorFunction))}return s&&(d+="var ret = retType.fromWireType(rv);\nreturn ret;\n"),d+="}\n",h.push(d),function(e,r){if(!(e instanceof Function))throw new TypeError("new_ called with constructor type "+typeof e+" which is not a function");var n=Ue(e.name||"unknownFunctionName",(function(){}));n.prototype=e.prototype;var t=new n,a=e.apply(t,r);return a instanceof Object?a:t}(Function,h).apply(null,v)}function ur(e,n,t){r.hasOwnProperty(e)?((void 0===t||void 0!==r[e].overloadTable&&void 0!==r[e].overloadTable[t])&&Ke("Cannot register public name '"+e+"' twice"),function(e,r,n){if(void 0===e[r].overloadTable){var t=e[r];e[r]=function(){return e[r].overloadTable.hasOwnProperty(arguments.length)||Ke("Function '"+n+"' called with an invalid number of arguments ("+arguments.length+") - expects one of ("+e[r].overloadTable+")!"),e[r].overloadTable[arguments.length].apply(this,arguments)},e[r].overloadTable=[],e[r].overloadTable[t.argCount]=t}}(r,e,e),r.hasOwnProperty(t)&&Ke("Cannot register multiple overloads of a function with the same number of arguments ("+t+")!"),r[e].overloadTable[t]=n):(r[e]=n,void 0!==t&&(r[e].numArguments=t))}function cr(e,n,t){return e.includes("j")?function(e,n,t){var a=r["dynCall_"+e];return t&&t.length?a.apply(null,[n].concat(t)):a.call(null,n)}(e,n,t):xe(n).apply(null,t)}function sr(e,r){var n,t,a,o=(e=Je(e)).includes("j")?(n=e,t=r,a=[],function(){a.length=arguments.length;for(var e=0;e<arguments.length;e++)a[e]=arguments[e];return cr(n,t,a)}):xe(r);return"function"!=typeof o&&Ke("unknown function pointer with signature "+e+": "+r),o}var fr=void 0;function lr(e){var r=Br(e),n=Je(r);return Or(r),n}function dr(e,r,t){switch(r){case 0:return t?function(e){return n()[e]}:function(e){return a()[e]};case 1:return t?function(e){return o()[e>>1]}:function(e){return i()[e>>1]};case 2:return t?function(e){return u()[e>>2]}:function(e){return c()[e>>2]};default:throw new TypeError("Unknown integer type: "+e)}}var pr={};function hr(){return"object"==typeof globalThis?globalThis:Function("return this")()}function vr(e,r){var n=Me[e];return void 0===n&&Ke(r+" has unknown type "+lr(e)),n}var mr={};function gr(e,r){var n=arguments.length-2,t=arguments;return ve((function(){for(var a=n,o=Yr(8*a),i=o>>3,u=0;u<n;u++){var c=t[2+u];s()[i+u]=c}return Vr(e,a,o,r)}))}var yr=[];function _r(e){try{return k.grow(e-R.byteLength+65535>>>16),K(k.buffer),1}catch(e){}}var wr={inEventHandler:0,removeAllEventListeners:function(){for(var e=wr.eventHandlers.length-1;e>=0;--e)wr._removeHandler(e);wr.eventHandlers=[],wr.deferredCalls=[]},registerRemoveEventListeners:function(){wr.removeEventListenersRegistered||(wr.removeEventListenersRegistered=!0)},deferredCalls:[],deferCall:function(e,r,n){function t(e,r){if(e.length!=r.length)return!1;for(var n in e)if(e[n]!=r[n])return!1;return!0}for(var a in wr.deferredCalls){var o=wr.deferredCalls[a];if(o.targetFunction==e&&t(o.argsList,n))return}wr.deferredCalls.push({targetFunction:e,precedence:r,argsList:n}),wr.deferredCalls.sort((function(e,r){return e.precedence<r.precedence}))},removeDeferredCalls:function(e){for(var r=0;r<wr.deferredCalls.length;++r)wr.deferredCalls[r].targetFunction==e&&(wr.deferredCalls.splice(r,1),--r)},canPerformEventHandlerRequests:function(){return wr.inEventHandler&&wr.currentEventHandler.allowsDeferredCalls},runDeferredCalls:function(){if(wr.canPerformEventHandlerRequests())for(var e=0;e<wr.deferredCalls.length;++e){var r=wr.deferredCalls[e];wr.deferredCalls.splice(e,1),--e,r.targetFunction.apply(null,r.argsList)}},eventHandlers:[],removeAllHandlersOnTarget:function(e,r){for(var n=0;n<wr.eventHandlers.length;++n)wr.eventHandlers[n].target!=e||r&&r!=wr.eventHandlers[n].eventTypeString||wr._removeHandler(n--)},_removeHandler:function(e){var r=wr.eventHandlers[e];r.target.removeEventListener(r.eventTypeString,r.eventListenerFunc,r.useCapture),wr.eventHandlers.splice(e,1)},registerOrRemoveHandler:function(e){var r=function(r){++wr.inEventHandler,wr.currentEventHandler=e,wr.runDeferredCalls(),e.handlerFunc(r),wr.runDeferredCalls(),--wr.inEventHandler};if(e.callbackfunc)e.eventListenerFunc=r,e.target.addEventListener(e.eventTypeString,r,e.useCapture),wr.eventHandlers.push(e),wr.registerRemoveEventListeners();else for(var n=0;n<wr.eventHandlers.length;++n)wr.eventHandlers[n].target==e.target&&wr.eventHandlers[n].eventTypeString==e.eventTypeString&&wr._removeHandler(n--)},queueEventHandlerOnThread_iiii:function(e,r,n,t,a){ve((function(){var o=Yr(12);u()[o>>2]=n,u()[o+4>>2]=t,u()[o+8>>2]=a,Nr(0,e,637534208,r,t,o)}))},getTargetThreadForEventCallback:function(e){switch(e){case 1:return 0;case 2:return be.currentProxiedOperationCallerThread;default:return e}},getNodeNameForTarget:function(e){return e?e==window?"#window":e==screen?"#screen":e&&e.nodeName?e.nodeName:"":""},fullscreenEnabled:function(){return document.fullscreenEnabled||document.webkitFullscreenEnabled}};function br(e,r,n,t){ve((function(){var a,o,i,c=Yr(12),s=0;r&&(o=I(a=r)+1,i=Hr(o),F(a,i,o),s=i),u()[c>>2]=s,u()[c+4>>2]=n,u()[c+8>>2]=t,Nr(0,e,657457152,0,s,c)}))}var Tr=[0,"undefined"!=typeof document?document:0,"undefined"!=typeof window?window:0];function Ar(e){var r;return e=(r=e)>2?j(r):r,Tr[e]||("undefined"!=typeof document?document.querySelector(e):void 0)}function kr(e){return Ar(e)}function xr(e,r,n){var t=kr(e);if(!t)return-4;if(t.canvasSharedPtr&&(u()[t.canvasSharedPtr>>2]=r,u()[t.canvasSharedPtr+4>>2]=n),!t.offscreenCanvas&&t.controlTransferredOffscreen)return t.canvasSharedPtr?(function(e,r,n,t){br(e,r=r?j(r):"",n,t)}(u()[t.canvasSharedPtr+8>>2],e,r,n),1):-4;t.offscreenCanvas&&(t=t.offscreenCanvas);var a=!1;if(t.GLctxObject&&t.GLctxObject.GLctx){var o=t.GLctxObject.GLctx.getParameter(2978);a=0===o[0]&&0===o[1]&&o[2]===t.width&&o[3]===t.height}return t.width=r,t.height=n,a&&t.GLctxObject.GLctx.viewport(0,0,r,n),0}function Cr(e,r,n){return g?gr(6,1,e,r,n):xr(e,r,n)}function Er(e,r){if(!ne&&!W)if(r)e();else try{e(),g&&function(){if(!ae())try{g?qr(E):_e(E)}catch(e){we(e)}}()}catch(e){we(e)}}var Wr,Sr={counter:1,buffers:[],programs:[],framebuffers:[],renderbuffers:[],textures:[],shaders:[],vaos:[],contexts:{},offscreenCanvases:{},queries:[],stringCache:{},unpackAlignment:4,recordError:function(e){Sr.lastError||(Sr.lastError=e)},getNewId:function(e){for(var r=Sr.counter++,n=e.length;n<r;n++)e[n]=null;return r},getSource:function(e,r,n,t){for(var a="",o=0;o<r;++o){var i=t?u()[t+4*o>>2]:-1;a+=j(u()[n+4*o>>2],i<0?void 0:i)}return a},createContext:function(e,r){e.getContextSafariWebGL2Fixed||(e.getContextSafariWebGL2Fixed=e.getContext,e.getContext=function(r,n){var t=e.getContextSafariWebGL2Fixed(r,n);return"webgl"==r==t instanceof WebGLRenderingContext?t:null});var n=e.getContext("webgl",r);return n?Sr.registerContext(n,r):0},registerContext:function(e,r){var n=Hr(8);u()[n+4>>2]=zr();var t={handle:n,attributes:r,version:r.majorVersion,GLctx:e};return e.canvas&&(e.canvas.GLctxObject=t),Sr.contexts[n]=t,(void 0===r.enableExtensionsByDefault||r.enableExtensionsByDefault)&&Sr.initExtensions(t),n},makeContextCurrent:function(e){return Sr.currentContext=Sr.contexts[e],r.ctx=Wr=Sr.currentContext&&Sr.currentContext.GLctx,!(e&&!Wr)},getContext:function(e){return Sr.contexts[e]},deleteContext:function(e){Sr.currentContext===Sr.contexts[e]&&(Sr.currentContext=null),"object"==typeof wr&&wr.removeAllHandlersOnTarget(Sr.contexts[e].GLctx.canvas),Sr.contexts[e]&&Sr.contexts[e].GLctx.canvas&&(Sr.contexts[e].GLctx.canvas.GLctxObject=void 0),Or(Sr.contexts[e].handle),Sr.contexts[e]=null},initExtensions:function(e){if(e||(e=Sr.currentContext),!e.initExtensionsDone){e.initExtensionsDone=!0;var r,n=e.GLctx;!function(e){var r=e.getExtension("ANGLE_instanced_arrays");r&&(e.vertexAttribDivisor=function(e,n){r.vertexAttribDivisorANGLE(e,n)},e.drawArraysInstanced=function(e,n,t,a){r.drawArraysInstancedANGLE(e,n,t,a)},e.drawElementsInstanced=function(e,n,t,a,o){r.drawElementsInstancedANGLE(e,n,t,a,o)})}(n),function(e){var r=e.getExtension("OES_vertex_array_object");r&&(e.createVertexArray=function(){return r.createVertexArrayOES()},e.deleteVertexArray=function(e){r.deleteVertexArrayOES(e)},e.bindVertexArray=function(e){r.bindVertexArrayOES(e)},e.isVertexArray=function(e){return r.isVertexArrayOES(e)})}(n),function(e){var r=e.getExtension("WEBGL_draw_buffers");r&&(e.drawBuffers=function(e,n){r.drawBuffersWEBGL(e,n)})}(n),n.disjointTimerQueryExt=n.getExtension("EXT_disjoint_timer_query"),(r=n).multiDrawWebgl=r.getExtension("WEBGL_multi_draw"),(n.getSupportedExtensions()||[]).forEach((function(e){e.includes("lose_context")||e.includes("debug")||n.getExtension(e)}))}}},Pr=["default","low-power","high-performance"];function jr(e){return g?gr(7,1,e):0}function Fr(e,r,n,t){if(g)return gr(8,1,e,r,n,t);var a=We.getStreamFromFD(e),o=We.doReadv(a,r,n);return u()[t>>2]=o,0}function Ir(e,r,n,t,a){if(g)return gr(9,1,e,r,n,t,a)}function Rr(e,r,n,t){if(g)return gr(10,1,e,r,n,t);for(var o=0,i=0;i<n;i++){var c=u()[r>>2],s=u()[r+4>>2];r+=8;for(var f=0;f<s;f++)We.printChar(e,a()[c+f]);o+=s}return u()[t>>2]=o,0}g||be.initMainThreadBlock(),Ve=r.InternalError=Ge(Error,"InternalError"),function(){for(var e=new Array(256),r=0;r<256;++r)e[r]=String.fromCharCode(r);Qe=e}(),Xe=r.BindingError=Ge(Error,"BindingError"),r.count_emval_handles=rr,r.get_first_emval=nr,fr=r.UnboundTypeError=Ge(Error,"UnboundTypeError");var Lr=[null,Te,function(e,r){if(g)return gr(2,1,e,r)},Se,Pe,je,Cr,jr,Fr,Ir,Rr],Mr={k:function(e,r,n,t){ce("Assertion failed: "+j(e)+", at: "+[r?j(r):"unknown filename",n,t?j(t):"unknown function"])},ba:function(e){Qr(e,!m,1),be.threadInit()},z:function(e,r,n,t){if("undefined"==typeof SharedArrayBuffer)return b("Current environment does not support SharedArrayBuffer, pthreads are not available!"),6;var a=[];if(g&&0===a.length)return Ur(687865856,e,r,n,t);var o=0,i=0,c=0;r&&-1!=r?(o=u()[r>>2],o+=2097152,i=u()[r+8>>2],c=0!==u()[r+12>>2]):o=2097152;var s,f=0==i;f?i=en(16,o):(i-=o)>0||ce("Assertion failed: "+s);var l={stackBase:i,stackSize:o,allocatedOwnStack:f,detached:c,startRoutine:n,pthread_ptr:e,arg:t,transferList:a};return g?(l.cmd="spawnThread",postMessage(l,a),0):Ce(l)},X:function(){postMessage({cmd:"detachedExit"})},V:function(e,r){return function(e,r,n){if(!e)return b("pthread_join attempted on a null thread pointer!"),71;if(u()[e+8>>2]!==e)return b("pthread_join attempted on thread "+e+", which does not point to a valid thread, or does not exist anymore!"),71;if(Atomics.load(c(),e+60>>2))return b("Attempted to join thread "+e+", which was already detached!"),28;if(g&&zr()==e)return b("PThread "+e+" is attempting to join to itself!"),16;if(!g&&Dr()==e)return b("Main thread "+e+" is attempting to join to itself!"),16;for(;;){var t=Atomics.load(c(),e+0>>2);if(1==t){if(r){var a=Atomics.load(c(),e+88>>2);u()[r>>2]=a}return Atomics.store(c(),e+60>>2,1),g?postMessage({cmd:"cleanupThread",thread:e}):ge(e),0}if(!n)return 10;Jr(),g||Gr(),Ee(e+0,t,g?100:1)}}(e,r,!0)},v:Se,R:Pe,T:je,L:function(e){var r=Fe[e];delete Fe[e];var n=r.rawConstructor,t=r.rawDestructor,a=r.fields;qe([e],a.map((function(e){return e.getterReturnType})).concat(a.map((function(e){return e.setterArgumentType}))),(function(e){var o={};return a.forEach((function(r,n){var t=r.fieldName,i=e[n],u=r.getter,c=r.getterContext,s=e[n+a.length],f=r.setter,l=r.setterContext;o[t]={read:function(e){return i.fromWireType(u(c,e))},write:function(e,r){var n=[];f(l,e,s.toWireType(n,r)),Ie(n)}}})),[{name:r.name,fromWireType:function(e){var r={};for(var n in o)r[n]=o[n].read(e);return t(e),r},toWireType:function(e,r){for(var a in o)if(!(a in r))throw new TypeError('Missing field:  "'+a+'"');var i=n();for(a in o)o[a].write(i,r[a]);return null!==e&&e.push(t,i),i},argPackAdvance:8,readValueFromPointer:Re,destructorFunction:t}]}))},G:function(e,r,n,t,a){},fa:function(e,r,t,a,i){var c=ze(t);Ye(e,{name:r=Je(r),fromWireType:function(e){return!!e},toWireType:function(e,r){return r?a:i},argPackAdvance:8,readValueFromPointer:function(e){var a;if(1===t)a=n();else if(2===t)a=o();else{if(4!==t)throw new TypeError("Unknown boolean type size: "+r);a=u()}return this.fromWireType(a[e>>c])},destructorFunction:null})},ea:function(e,r){Ye(e,{name:r=Je(r),fromWireType:function(e){var r=tr.toValue(e);return er(e),r},toWireType:function(e,r){return tr.toHandle(r)},argPackAdvance:8,readValueFromPointer:Re,destructorFunction:null})},A:function(e,r,n){var t=ze(n);Ye(e,{name:r=Je(r),fromWireType:function(e){return e},toWireType:function(e,r){if("number"!=typeof r&&"boolean"!=typeof r)throw new TypeError('Cannot convert "'+ar(r)+'" to '+this.name);return r},argPackAdvance:8,readValueFromPointer:or(r,t),destructorFunction:null})},D:function(e,n,t,a,o,i){var c=function(e,r){for(var n=[],t=0;t<e;t++)n.push(u()[(r>>2)+t]);return n}(n,t);e=Je(e),o=sr(a,o),ur(e,(function(){!function(e,r){var n=[],t={};throw r.forEach((function e(r){t[r]||Me[r]||(He[r]?He[r].forEach(e):(n.push(r),t[r]=!0))})),new fr(e+": "+n.map(lr).join([", "]))}("Cannot call "+e+" due to unbound types",c)}),n-1),qe([],c,(function(t){var a=[t[0],null].concat(t.slice(1));return function(e,n,t){r.hasOwnProperty(e)||Ne("Replacing nonexistant public symbol"),void 0!==r[e].overloadTable&&void 0!==t?r[e].overloadTable[t]=n:(r[e]=n,r[e].argCount=t)}(e,ir(e,a,null,o,i),n-1),[]}))},l:function(e,r,n,t,a){r=Je(r),-1===a&&(a=4294967295);var o=ze(n),i=function(e){return e};if(0===t){var u=32-8*n;i=function(e){return e<<u>>>u}}var c=r.includes("unsigned");Ye(e,{name:r,fromWireType:i,toWireType:function(e,n){if("number"!=typeof n&&"boolean"!=typeof n)throw new TypeError('Cannot convert "'+ar(n)+'" to '+this.name);if(n<t||n>a)throw new TypeError('Passing a number "'+ar(n)+'" from JS side to C/C++ side to an argument of type "'+r+'", which is outside the valid range ['+t+", "+a+"]!");return c?n>>>0:0|n},argPackAdvance:8,readValueFromPointer:dr(r,o,0!==t),destructorFunction:null})},j:function(e,r,n){var t=[Int8Array,Uint8Array,Int16Array,Uint16Array,Int32Array,Uint32Array,Float32Array,Float64Array][r];function a(e){e>>=2;var r=c(),n=r[e],a=r[e+1];return new t(R,a,n)}Ye(e,{name:n=Je(n),fromWireType:a,argPackAdvance:8,readValueFromPointer:a},{ignoreDuplicateRegistrations:!0})},B:function(e,r){var n="std::string"===(r=Je(r));Ye(e,{name:r,fromWireType:function(e){var r,t=c()[e>>2];if(n)for(var o=e+4,i=0;i<=t;++i){var u=e+4+i;if(i==t||0==a()[u]){var s=j(o,u-o);void 0===r?r=s:(r+=String.fromCharCode(0),r+=s),o=u+1}}else{var f=new Array(t);for(i=0;i<t;++i)f[i]=String.fromCharCode(a()[e+4+i]);r=f.join("")}return Or(e),r},toWireType:function(e,r){r instanceof ArrayBuffer&&(r=new Uint8Array(r));var t="string"==typeof r;t||r instanceof Uint8Array||r instanceof Uint8ClampedArray||r instanceof Int8Array||Ke("Cannot pass non-string to std::string");var o=(n&&t?function(){return I(r)}:function(){return r.length})(),i=Hr(4+o+1);if(c()[i>>2]=o,n&&t)F(r,i+4,o+1);else if(t)for(var u=0;u<o;++u){var s=r.charCodeAt(u);s>255&&(Or(i),Ke("String has UTF-16 code units that do not fit in 8 bits")),a()[i+4+u]=s}else for(u=0;u<o;++u)a()[i+4+u]=r[u];return null!==e&&e.push(Or,i),i},argPackAdvance:8,readValueFromPointer:Re,destructorFunction:function(e){Or(e)}})},s:function(e,r,n){var t,a,o,u,s;n=Je(n),2===r?(t=N,a=q,u=z,o=function(){return i()},s=1):4===r&&(t=Q,a=J,u=X,o=function(){return c()},s=2),Ye(e,{name:n,fromWireType:function(e){for(var n,a=c()[e>>2],i=o(),u=e+4,f=0;f<=a;++f){var l=e+4+f*r;if(f==a||0==i[l>>s]){var d=t(u,l-u);void 0===n?n=d:(n+=String.fromCharCode(0),n+=d),u=l+r}}return Or(e),n},toWireType:function(e,t){"string"!=typeof t&&Ke("Cannot pass non-string to C++ string type "+n);var o=u(t),i=Hr(4+o+r);return c()[i>>2]=o>>s,a(t,i+4,o+r),null!==e&&e.push(Or,i),i},argPackAdvance:8,readValueFromPointer:Re,destructorFunction:function(e){Or(e)}})},S:function(e,r,n,t,a,o){Fe[e]={name:Je(r),rawConstructor:sr(n,t),rawDestructor:sr(a,o),fields:[]}},i:function(e,r,n,t,a,o,i,u,c,s){Fe[e].fields.push({fieldName:Je(r),getterReturnType:n,getter:sr(t,a),getterContext:o,setterArgumentType:i,setter:sr(u,c),setterContext:s})},ga:function(e,r){Ye(e,{isVoid:!0,name:r=Je(r),argPackAdvance:0,fromWireType:function(){},toWireType:function(e,r){}})},da:function(e,r){if(e==r)postMessage({cmd:"processQueuedMainThreadWork"});else if(g)postMessage({targetThread:e,cmd:"processThreadQueue"});else{var n=be.pthreads[e],t=n&&n.worker;if(!t)return;t.postMessage({cmd:"processThreadQueue"})}return 1},M:function(){throw"longjmp"},o:er,ia:function(e){return 0===e?tr.toHandle(hr()):(e=void 0===(n=pr[r=e])?Je(r):n,tr.toHandle(hr()[e]));var r,n},C:function(e){e>4&&($e[e].refcount+=1)},$:function(e,n,t,a){e=tr.toValue(e);var o=mr[n];return o||(o=function(e){for(var n="",t=0;t<e;++t)n+=(0!==t?", ":"")+"arg"+t;var a="return function emval_allocator_"+e+"(constructor, argTypes, args) {\n";for(t=0;t<e;++t)a+="var argType"+t+" = requireRegisteredType(Module['HEAP32'][(argTypes >>> 2) + "+t+'], "parameter '+t+'");\nvar arg'+t+" = argType"+t+".readValueFromPointer(args);\nargs += argType"+t+"['argPackAdvance'];\n";return a+="var obj = new constructor("+n+");\nreturn valueToHandle(obj);\n}\n",new Function("requireRegisteredType","Module","valueToHandle",a)(vr,r,tr.toHandle)}(n),mr[n]=o),o(e,t,a)},e:function(){ce("")},U:function(){},y:function(e,r){},n:Ee,m:me,r:Ae,N:function(e,r,n){a().copyWithin(e,r,r+n)},ja:function(){return navigator.hardwareConcurrency},Z:function(e,r,n){yr.length=r;for(var t=n>>3,a=0;a<r;a++)yr[a]=s()[t+a];return(e<0?pe[-e-1]:Lr[e]).apply(null,yr)},O:function(e){var r=a().length;if((e>>>=0)<=r)return!1;var n,t,o=2147483648;if(e>o)return!1;for(var i=1;i<=4;i*=2){var u=r*(1+.2/i);if(u=Math.min(u,e+100663296),_r(Math.min(o,((n=Math.max(e,u))%(t=65536)>0&&(n+=t-n%t),n))))return!0}return!1},_:function(e,r,n){return kr(e)?xr(e,r,n):Cr(e,r,n)},x:function(e){},ca:function(e,r,n){return te+=1,setTimeout((function(){te-=1,Er((function(){xe(e)(n)}))}),r)},W:function(){throw"unwind"},aa:function(e,r){return n=e,t=r>>2,a=u()[t+6],o={alpha:!!u()[t+0],depth:!!u()[t+1],stencil:!!u()[t+2],antialias:!!u()[t+3],premultipliedAlpha:!!u()[t+4],preserveDrawingBuffer:!!u()[t+5],powerPreference:Pr[a],failIfMajorPerformanceCaveat:!!u()[t+7],majorVersion:u()[t+8],minorVersion:u()[t+9],enableExtensionsByDefault:u()[t+10],explicitSwapControl:u()[t+11],proxyContextToMainThread:u()[t+12],renderViaOffscreenBackBuffer:u()[t+13]},(i=kr(n))?o.explicitSwapControl?0:Sr.createContext(i,o):0;var n,t,a,o,i},Y:_e,w:jr,Q:Fr,F:Ir,P:Rr,c:function(){return A},g:function(e,r){var n=Xr();try{return xe(e)(r)}catch(e){if(Kr(n),e!==e+0&&"longjmp"!==e)throw e;$r(1,0)}},p:function(e,r,n){var t=Xr();try{return xe(e)(r,n)}catch(e){if(Kr(t),e!==e+0&&"longjmp"!==e)throw e;$r(1,0)}},q:function(e,r,n,t,a){var o=Xr();try{return xe(e)(r,n,t,a)}catch(e){if(Kr(o),e!==e+0&&"longjmp"!==e)throw e;$r(1,0)}},K:function(e,r,n,t,a,o,i,u,c,s){var f=Xr();try{return xe(e)(r,n,t,a,o,i,u,c,s)}catch(e){if(Kr(f),e!==e+0&&"longjmp"!==e)throw e;$r(1,0)}},I:function(e,r,n,t,a,o,i,u,c,s,f){var l=Xr();try{return xe(e)(r,n,t,a,o,i,u,c,s,f)}catch(e){if(Kr(l),e!==e+0&&"longjmp"!==e)throw e;$r(1,0)}},J:function(e,r,n,t,a,o,i,u,c,s,f,l){var d=Xr();try{return xe(e)(r,n,t,a,o,i,u,c,s,f,l)}catch(e){if(Kr(d),e!==e+0&&"longjmp"!==e)throw e;$r(1,0)}},E:function(e,r,n,t,a,o){var i=Xr();try{return rn(e,r,n,t,a,o)}catch(e){if(Kr(i),e!==e+0&&"longjmp"!==e)throw e;$r(1,0)}},h:function(e,r){var n=Xr();try{xe(e)(r)}catch(e){if(Kr(n),e!==e+0&&"longjmp"!==e)throw e;$r(1,0)}},f:function(e,r,n){var t=Xr();try{xe(e)(r,n)}catch(e){if(Kr(t),e!==e+0&&"longjmp"!==e)throw e;$r(1,0)}},u:function(e,r,n,t){var a=Xr();try{xe(e)(r,n,t)}catch(e){if(Kr(a),e!==e+0&&"longjmp"!==e)throw e;$r(1,0)}},d:function(e,r,n,t,a){var o=Xr();try{xe(e)(r,n,t,a)}catch(e){if(Kr(o),e!==e+0&&"longjmp"!==e)throw e;$r(1,0)}},H:function(e,r,n,t,a,o){var i=Xr();try{xe(e)(r,n,t,a,o)}catch(e){if(Kr(i),e!==e+0&&"longjmp"!==e)throw e;$r(1,0)}},t:function(e,r,n,t,a,o,i){var u=Xr();try{xe(e)(r,n,t,a,o,i)}catch(e){if(Kr(u),e!==e+0&&"longjmp"!==e)throw e;$r(1,0)}},a:k||r.wasmMemory,b:function(e){A=e},ha:function(e){var r=Date.now()/1e3|0;return e&&(u()[e>>2]=r),r}};!function(){var e={a:Mr};function n(e,n){var t,a,o=e.exports;if(r.asm=o,t=r.asm.na,be.tlsInitFunctions.push(t),Y=r.asm.Ga,a=r.asm.ka,ee.unshift(a),x=n,!g){var i=be.unusedWorkers.length;be.unusedWorkers.forEach((function(e){be.loadWasmModuleToWorker(e,(function(){--i||function(){if(ie--,r.monitorRunDependencies&&r.monitorRunDependencies(ie),0==ie&&ue){var e=ue;ue=null,e()}}()}))}))}}function t(e){n(e.instance,e.module)}function a(r){return(!T&&m&&"function"==typeof fetch?fetch(se,{credentials:"same-origin"}).then((function(e){if(!e.ok)throw"failed to load wasm binary file at '"+se+"'";return e.arrayBuffer()})).catch((function(){return de(se)})):Promise.resolve().then((function(){return de(se)}))).then((function(r){return WebAssembly.instantiate(r,e)})).then((function(e){return e})).then(r,(function(e){b("failed to asynchronously prepare wasm: "+e),ce(e)}))}if(g||(ie++,r.monitorRunDependencies&&r.monitorRunDependencies(ie)),r.instantiateWasm)try{return r.instantiateWasm(e,n)}catch(e){return b("Module.instantiateWasm callback failed with error: "+e),!1}(T||"function"!=typeof WebAssembly.instantiateStreaming||le(se)||"function"!=typeof fetch?a(t):fetch(se,{credentials:"same-origin"}).then((function(r){return WebAssembly.instantiateStreaming(r,e).then(t,(function(e){return b("wasm streaming compile failed: "+e),b("falling back to ArrayBuffer instantiation"),a(t)}))}))).catch(l)}(),r.___wasm_call_ctors=function(){return(r.___wasm_call_ctors=r.asm.ka).apply(null,arguments)};var Hr=r._malloc=function(){return(Hr=r._malloc=r.asm.la).apply(null,arguments)},Or=r._free=function(){return(Or=r._free=r.asm.ma).apply(null,arguments)};r._emscripten_tls_init=function(){return(r._emscripten_tls_init=r.asm.na).apply(null,arguments)};var Br=r.___getTypeName=function(){return(Br=r.___getTypeName=r.asm.oa).apply(null,arguments)};r.___embind_register_native_and_builtin_types=function(){return(r.___embind_register_native_and_builtin_types=r.asm.pa).apply(null,arguments)},r._emscripten_current_thread_process_queued_calls=function(){return(r._emscripten_current_thread_process_queued_calls=r.asm.qa).apply(null,arguments)};var Dr=r._emscripten_main_browser_thread_id=function(){return(Dr=r._emscripten_main_browser_thread_id=r.asm.ra).apply(null,arguments)},Ur=r._emscripten_sync_run_in_main_thread_4=function(){return(Ur=r._emscripten_sync_run_in_main_thread_4=r.asm.sa).apply(null,arguments)},Gr=r._emscripten_main_thread_process_queued_calls=function(){return(Gr=r._emscripten_main_thread_process_queued_calls=r.asm.ta).apply(null,arguments)},Vr=r._emscripten_run_in_main_runtime_thread_js=function(){return(Vr=r._emscripten_run_in_main_runtime_thread_js=r.asm.ua).apply(null,arguments)},Nr=r.__emscripten_call_on_thread=function(){return(Nr=r.__emscripten_call_on_thread=r.asm.va).apply(null,arguments)},qr=r.__emscripten_thread_exit=function(){return(qr=r.__emscripten_thread_exit=r.asm.wa).apply(null,arguments)},zr=r._pthread_self=function(){return(zr=r._pthread_self=r.asm.xa).apply(null,arguments)},Qr=r.__emscripten_thread_init=function(){return(Qr=r.__emscripten_thread_init=r.asm.ya).apply(null,arguments)},Jr=r._pthread_testcancel=function(){return(Jr=r._pthread_testcancel=r.asm.za).apply(null,arguments)},Xr=r.stackSave=function(){return(Xr=r.stackSave=r.asm.Aa).apply(null,arguments)},Kr=r.stackRestore=function(){return(Kr=r.stackRestore=r.asm.Ba).apply(null,arguments)},Yr=r.stackAlloc=function(){return(Yr=r.stackAlloc=r.asm.Ca).apply(null,arguments)},Zr=r._emscripten_stack_set_limits=function(){return(Zr=r._emscripten_stack_set_limits=r.asm.Da).apply(null,arguments)},$r=r._setThrew=function(){return($r=r._setThrew=r.asm.Ea).apply(null,arguments)},en=r._memalign=function(){return(en=r._memalign=r.asm.Fa).apply(null,arguments)};r.dynCall_jiiiiiiiii=function(){return(r.dynCall_jiiiiiiiii=r.asm.Ha).apply(null,arguments)};var rn=r.dynCall_ijiii=function(){return(rn=r.dynCall_ijiii=r.asm.Ia).apply(null,arguments)};r.dynCall_jiji=function(){return(r.dynCall_jiji=r.asm.Ja).apply(null,arguments)},r.dynCall_jiiiiiiii=function(){return(r.dynCall_jiiiiiiii=r.asm.Ka).apply(null,arguments)},r.dynCall_jiiiiii=function(){return(r.dynCall_jiiiiii=r.asm.La).apply(null,arguments)},r.dynCall_jiiiii=function(){return(r.dynCall_jiiiii=r.asm.Ma).apply(null,arguments)},r.dynCall_iiijii=function(){return(r.dynCall_iiijii=r.asm.Na).apply(null,arguments)};var nn,tn=r.__emscripten_allow_main_runtime_queued_calls=618788,an=r.__emscripten_main_thread_futex=921588;function on(e){this.name="ExitStatus",this.message="Program terminated with exit("+e+")",this.status=e}function un(e){if(!(ie>0)){if(g)return f(r),oe(),void postMessage({cmd:"loaded"});!function(){if(!g){if(r.preRun)for("function"==typeof r.preRun&&(r.preRun=[r.preRun]);r.preRun.length;)e=r.preRun.shift(),$.unshift(e);var e;he($)}}(),ie>0||(r.setStatus?(r.setStatus("Running..."),setTimeout((function(){setTimeout((function(){r.setStatus("")}),1),n()}),1)):n())}function n(){nn||(nn=!0,r.calledRun=!0,W||(oe(),f(r),r.onRuntimeInitialized&&r.onRuntimeInitialized(),function(){if(!g){if(r.postRun)for("function"==typeof r.postRun&&(r.postRun=[r.postRun]);r.postRun.length;)e=r.postRun.shift(),re.unshift(e);var e;he(re)}}()))}}if(r.keepRuntimeAlive=ae,r.PThread=be,r.PThread=be,r.wasmMemory=k,r.ExitStatus=on,ue=function e(){nn||un(),nn||(ue=e)},r.run=un,r.preInit)for("function"==typeof r.preInit&&(r.preInit=[r.preInit]);r.preInit.length>0;)r.preInit.pop()();return g&&(C=!1,be.initWorker()),un(),r.ready});n.default=a}));
