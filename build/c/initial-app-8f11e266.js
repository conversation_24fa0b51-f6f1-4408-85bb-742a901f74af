if(!self.define){let e={};const t=(t,n)=>(t=t.startsWith(location.origin)?t:new URL(t+".js",n).href,e[t]||new Promise((e=>{if("document"in self){const n=document.createElement("link");n.rel="preload",n.as="script",n.href=t,n.onload=()=>{const n=document.createElement("script");n.src=t,n.onload=e,document.head.appendChild(n)},document.head.appendChild(n)}else self.nextDefineUri=t,importScripts(t),e()})).then((()=>{let n=e[t];if(!n)throw new Error(`Module ${t} didn’t register its module`);return n})));self.define=(n,i)=>{const o=self.nextDefineUri||("document"in self?document.currentScript.src:"")||location.href;if(e[o])return;let s={};const r=e=>t(e,o),a={module:{uri:o},exports:s,require:r};e[o]=Promise.resolve().then((()=>Promise.all(n.map((e=>a[e]||r(e)))))).then((e=>(i(...e),s)))}}define(["require","exports"],(function(e,t){var n,i,o,s,r,a={},l=[],c=/acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|itera/i;function _(e,t){for(var n in t)e[n]=t[n];return e}function d(e){var t=e.parentNode;t&&t.removeChild(e)}function p(e,t,n){var i,o,s,r=arguments,a={};for(s in t)"key"==s?i=t[s]:"ref"==s?o=t[s]:a[s]=t[s];if(arguments.length>3)for(n=[n],s=3;s<arguments.length;s++)n.push(r[s]);if(null!=n&&(a.children=n),"function"==typeof e&&null!=e.defaultProps)for(s in e.defaultProps)void 0===a[s]&&(a[s]=e.defaultProps[s]);return u(e,a,i,o,null)}function u(e,t,i,o,s){var r={type:e,props:t,key:i,ref:o,__k:null,__:null,__b:0,__e:null,__d:void 0,__c:null,__h:null,constructor:void 0,__v:s};return null==s&&(r.__v=r),null!=n.vnode&&n.vnode(r),r}function h(e){return e.children}function f(e,t){this.props=e,this.context=t}function m(e,t){if(null==t)return e.__?m(e.__,e.__.__k.indexOf(e)+1):null;for(var n;t<e.__k.length;t++)if(null!=(n=e.__k[t])&&null!=n.__e)return n.__e;return"function"==typeof e.type?m(e):null}function v(e){var t,n;if(null!=(e=e.__)&&null!=e.__c){for(e.__e=e.__c.base=null,t=0;t<e.__k.length;t++)if(null!=(n=e.__k[t])&&null!=n.__e){e.__e=e.__c.base=n.__e;break}return v(e)}}function g(e){(!e.__d&&(e.__d=!0)&&i.push(e)&&!b.__r++||s!==n.debounceRendering)&&((s=n.debounceRendering)||o)(b)}function b(){for(var e;b.__r=i.length;)e=i.sort((function(e,t){return e.__v.__b-t.__v.__b})),i=[],e.some((function(e){var t,n,i,o,s,r,a;e.__d&&(r=(s=(t=e).__v).__e,(a=t.__P)&&(n=[],(i=_({},s)).__v=i,o=z(a,s,i,t.__n,void 0!==a.ownerSVGElement,null!=s.__h?[r]:null,n,null==r?m(s):r,s.__h),C(n,s),o!=r&&v(s)))}))}function y(e,t,n,i,o,s,r,c,_,p){var f,v,g,b,y,D,k,E=i&&i.__k||l,x=E.length;for(_==a&&(_=null!=r?r[0]:x?m(i,0):null),n.__k=[],f=0;f<t.length;f++)if(null!=(b=n.__k[f]=null==(b=t[f])||"boolean"==typeof b?null:"string"==typeof b||"number"==typeof b?u(null,b,null,null,b):Array.isArray(b)?u(h,{children:b},null,null,null):null!=b.__e||null!=b.__c?u(b.type,b.props,b.key,null,b.__v):b)){if(b.__=n,b.__b=n.__b+1,null===(g=E[f])||g&&b.key==g.key&&b.type===g.type)E[f]=void 0;else for(v=0;v<x;v++){if((g=E[v])&&b.key==g.key&&b.type===g.type){E[v]=void 0;break}g=null}y=z(e,b,g=g||a,o,s,r,c,_,p),(v=b.ref)&&g.ref!=v&&(k||(k=[]),g.ref&&k.push(g.ref,null,b),k.push(v,b.__c||y,b)),null!=y?(null==D&&(D=y),_=w(e,b,g,E,r,y,_),p||"option"!=n.type?"function"==typeof n.type&&(n.__d=_):e.value=""):_&&g.__e==_&&_.parentNode!=e&&(_=m(g))}if(n.__e=D,null!=r&&"function"!=typeof n.type)for(f=r.length;f--;)null!=r[f]&&d(r[f]);for(f=x;f--;)null!=E[f]&&S(E[f],E[f]);if(k)for(f=0;f<k.length;f++)B(k[f],k[++f],k[++f])}function w(e,t,n,i,o,s,r){var a,l,c;if(void 0!==t.__d)a=t.__d,t.__d=void 0;else if(o==n||s!=r||null==s.parentNode)e:if(null==r||r.parentNode!==e)e.appendChild(s),a=null;else{for(l=r,c=0;(l=l.nextSibling)&&c<i.length;c+=2)if(l==s)break e;e.insertBefore(s,r),a=r}return void 0!==a?a:s.nextSibling}function D(e,t,n){"-"===t[0]?e.setProperty(t,n):e[t]=null==n?"":"number"!=typeof n||c.test(t)?n:n+"px"}function k(e,t,n,i,o){var s,r,a;if(o&&"className"==t&&(t="class"),"style"===t)if("string"==typeof n)e.style.cssText=n;else{if("string"==typeof i&&(e.style.cssText=i=""),i)for(t in i)n&&t in n||D(e.style,t,"");if(n)for(t in n)i&&n[t]===i[t]||D(e.style,t,n[t])}else"o"===t[0]&&"n"===t[1]?(s=t!==(t=t.replace(/Capture$/,"")),(r=t.toLowerCase())in e&&(t=r),t=t.slice(2),e.l||(e.l={}),e.l[t+s]=n,a=s?x:E,n?i||e.addEventListener(t,a,s):e.removeEventListener(t,a,s)):"list"!==t&&"tagName"!==t&&"form"!==t&&"type"!==t&&"size"!==t&&"download"!==t&&"href"!==t&&!o&&t in e?e[t]=null==n?"":n:"function"!=typeof n&&"dangerouslySetInnerHTML"!==t&&(t!==(t=t.replace(/xlink:?/,""))?null==n||!1===n?e.removeAttributeNS("http://www.w3.org/1999/xlink",t.toLowerCase()):e.setAttributeNS("http://www.w3.org/1999/xlink",t.toLowerCase(),n):null==n||!1===n&&!/^ar/.test(t)?e.removeAttribute(t):e.setAttribute(t,n))}function E(e){this.l[e.type+!1](n.event?n.event(e):e)}function x(e){this.l[e.type+!0](n.event?n.event(e):e)}function F(e,t,n){var i,o;for(i=0;i<e.__k.length;i++)(o=e.__k[i])&&(o.__=e,o.__e&&("function"==typeof o.type&&o.__k.length>1&&F(o,t,n),t=w(n,o,o,e.__k,null,o.__e,t),"function"==typeof e.type&&(e.__d=t)))}function z(e,t,i,o,s,r,a,l,c){var d,p,u,m,v,g,b,w,D,k,E,x=t.type;if(void 0!==t.constructor)return null;null!=i.__h&&(c=i.__h,l=t.__e=i.__e,t.__h=null,r=[l]),(d=n.__b)&&d(t);try{e:if("function"==typeof x){if(w=t.props,D=(d=x.contextType)&&o[d.__c],k=d?D?D.props.value:d.__:o,i.__c?b=(p=t.__c=i.__c).__=p.__E:("prototype"in x&&x.prototype.render?t.__c=p=new x(w,k):(t.__c=p=new f(w,k),p.constructor=x,p.render=L),D&&D.sub(p),p.props=w,p.state||(p.state={}),p.context=k,p.__n=o,u=p.__d=!0,p.__h=[]),null==p.__s&&(p.__s=p.state),null!=x.getDerivedStateFromProps&&(p.__s==p.state&&(p.__s=_({},p.__s)),_(p.__s,x.getDerivedStateFromProps(w,p.__s))),m=p.props,v=p.state,u)null==x.getDerivedStateFromProps&&null!=p.componentWillMount&&p.componentWillMount(),null!=p.componentDidMount&&p.__h.push(p.componentDidMount);else{if(null==x.getDerivedStateFromProps&&w!==m&&null!=p.componentWillReceiveProps&&p.componentWillReceiveProps(w,k),!p.__e&&null!=p.shouldComponentUpdate&&!1===p.shouldComponentUpdate(w,p.__s,k)||t.__v===i.__v){p.props=w,p.state=p.__s,t.__v!==i.__v&&(p.__d=!1),p.__v=t,t.__e=i.__e,t.__k=i.__k,p.__h.length&&a.push(p),F(t,l,e);break e}null!=p.componentWillUpdate&&p.componentWillUpdate(w,p.__s,k),null!=p.componentDidUpdate&&p.__h.push((function(){p.componentDidUpdate(m,v,g)}))}p.context=k,p.props=w,p.state=p.__s,(d=n.__r)&&d(t),p.__d=!1,p.__v=t,p.__P=e,d=p.render(p.props,p.state,p.context),p.state=p.__s,null!=p.getChildContext&&(o=_(_({},o),p.getChildContext())),u||null==p.getSnapshotBeforeUpdate||(g=p.getSnapshotBeforeUpdate(m,v)),E=null!=d&&d.type==h&&null==d.key?d.props.children:d,y(e,Array.isArray(E)?E:[E],t,i,o,s,r,a,l,c),p.base=t.__e,t.__h=null,p.__h.length&&a.push(p),b&&(p.__E=p.__=null),p.__e=!1}else null==r&&t.__v===i.__v?(t.__k=i.__k,t.__e=i.__e):t.__e=A(i.__e,t,i,o,s,r,a,c);(d=n.diffed)&&d(t)}catch(e){t.__v=null,(c||null!=r)&&(t.__e=l,t.__h=!!c,r[r.indexOf(l)]=null),n.__e(e,t,i)}return t.__e}function C(e,t){n.__c&&n.__c(t,e),e.some((function(t){try{e=t.__h,t.__h=[],e.some((function(e){e.call(t)}))}catch(e){n.__e(e,t.__v)}}))}function A(e,t,n,i,o,s,r,c){var _,d,p,u,h,f=n.props,m=t.props;if(o="svg"===t.type||o,null!=s)for(_=0;_<s.length;_++)if(null!=(d=s[_])&&((null===t.type?3===d.nodeType:d.localName===t.type)||e==d)){e=d,s[_]=null;break}if(null==e){if(null===t.type)return document.createTextNode(m);e=o?document.createElementNS("http://www.w3.org/2000/svg",t.type):document.createElement(t.type,m.is&&{is:m.is}),s=null,c=!1}if(null===t.type)f===m||c&&e.data===m||(e.data=m);else{if(null!=s&&(s=l.slice.call(e.childNodes)),p=(f=n.props||a).dangerouslySetInnerHTML,u=m.dangerouslySetInnerHTML,!c){if(null!=s)for(f={},h=0;h<e.attributes.length;h++)f[e.attributes[h].name]=e.attributes[h].value;(u||p)&&(u&&(p&&u.__html==p.__html||u.__html===e.innerHTML)||(e.innerHTML=u&&u.__html||""))}(function(e,t,n,i,o){var s;for(s in n)"children"===s||"key"===s||s in t||k(e,s,null,n[s],i);for(s in t)o&&"function"!=typeof t[s]||"children"===s||"key"===s||"value"===s||"checked"===s||n[s]===t[s]||k(e,s,t[s],n[s],i)})(e,m,f,o,c),u?t.__k=[]:(_=t.props.children,y(e,Array.isArray(_)?_:[_],t,n,i,"foreignObject"!==t.type&&o,s,r,a,c)),c||("value"in m&&void 0!==(_=m.value)&&(_!==e.value||"progress"===t.type&&!_)&&k(e,"value",_,f.value,!1),"checked"in m&&void 0!==(_=m.checked)&&_!==e.checked&&k(e,"checked",_,f.checked,!1))}return e}function B(e,t,i){try{"function"==typeof e?e(t):e.current=t}catch(e){n.__e(e,i)}}function S(e,t,i){var o,s,r;if(n.unmount&&n.unmount(e),(o=e.ref)&&(o.current&&o.current!==e.__e||B(o,null,t)),i||"function"==typeof e.type||(i=null!=(s=e.__e)),e.__e=e.__d=void 0,null!=(o=e.__c)){if(o.componentWillUnmount)try{o.componentWillUnmount()}catch(e){n.__e(e,t)}o.base=o.__P=null}if(o=e.__k)for(r=0;r<o.length;r++)o[r]&&S(o[r],t,i);null!=s&&d(s)}function L(e,t,n){return this.constructor(e,n)}function M(e,t){const n=`$$ref_${t}`;let i=e[n];return i||(i=e[n]=n=>{e[t]=n}),i}n={__e:function(e,t){for(var n,i,o,s=t.__h;t=t.__;)if((n=t.__c)&&!n.__)try{if((i=n.constructor)&&null!=i.getDerivedStateFromError&&(n.setState(i.getDerivedStateFromError(e)),o=n.__d),null!=n.componentDidCatch&&(n.componentDidCatch(e),o=n.__d),o)return t.__h=s,n.__E=n}catch(t){e=t}throw e}},f.prototype.setState=function(e,t){var n;n=null!=this.__s&&this.__s!==this.state?this.__s:this.__s=_({},this.state),"function"==typeof e&&(e=e(_({},n),this.props)),e&&_(n,e),null!=e&&this.__v&&(t&&this.__h.push(t),g(this))},f.prototype.forceUpdate=function(e){this.__v&&(this.__e=!0,e&&this.__h.push(e),g(this))},f.prototype.render=h,i=[],o="function"==typeof Promise?Promise.prototype.then.bind(Promise.resolve()):setTimeout,b.__r=0,r=a;function P(e){const t=document.createElement("style");t.textContent=e,document.head.append(t)}function q(e,t,n){const i=Array.from(e);let o;if(""===t)return o=i.filter((e=>"file"===e.kind)),n?o:[o[0]];const s=t.toLowerCase().split(",").map((e=>e.split("/").map((e=>e.trim())))).filter((e=>2===e.length));return o=o=i.filter((e=>{if("file"!==e.kind)return!1;const[t,n]=e.type.toLowerCase().split("/").map((e=>e.trim()));for(const[e,i]of s)if(t===e&&("*"===i||n===i))return!0;return!1})),!1===n&&(o=[o[0]]),o}function I(e,t,n){const i=[];return q(e.items,t,n).forEach((e=>{const t=e.getAsFile();null!==t&&i.push(t)})),i}P('._app_11rsf_1{position:absolute;left:0;top:0;contain:strict}._app_11rsf_1,._drop_11rsf_11{width:100%;height:100%;overflow:hidden}._drop_11rsf_11{touch-action:none}._drop_11rsf_11:after{content:"";position:absolute;display:block;left:10px;top:10px;right:10px;bottom:10px;background-color:rgba(0,0,0,.1);border:2px dashed #fff;border-color:var(--pink);border-radius:10px;opacity:0;transform:scale(.95);transition:all .2s ease-in;transition-property:transform,opacity;pointer-events:none}._drop_11rsf_11.drop-valid:after{opacity:1;transform:scale(1);transition-timing-function:ease-out}._option-pair_11rsf_45{display:flex;justify-content:flex-end;width:100%;height:100%}._option-pair_11rsf_45._horizontal_11rsf_51{justify-content:space-between;align-items:flex-end}._option-pair_11rsf_45._vertical_11rsf_56{flex-direction:column}._app-loader_11rsf_61{position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);--size:225px;--stroke-width:26px}');class U extends Event{constructor(e,t){var n;super(e,t),this instanceof(n=U)||Object.setPrototypeOf(this,n.prototype),this._files=t.files,this._action=t.action}get action(){return this._action}get files(){return this._files}}class T extends HTMLElement{constructor(){super(),this._dragEnterCount=0,this._onDragEnter=this._onDragEnter.bind(this),this._onDragLeave=this._onDragLeave.bind(this),this._onDrop=this._onDrop.bind(this),this._onPaste=this._onPaste.bind(this),this.addEventListener("dragover",(e=>e.preventDefault())),this.addEventListener("drop",this._onDrop),this.addEventListener("dragenter",this._onDragEnter),this.addEventListener("dragend",(()=>this._reset())),this.addEventListener("dragleave",this._onDragLeave),this.addEventListener("paste",this._onPaste)}get accept(){return this.getAttribute("accept")||""}set accept(e){this.setAttribute("accept",e)}get multiple(){return this.getAttribute("multiple")}set multiple(e){this.setAttribute("multiple",e||"")}_onDragEnter(e){if(this._dragEnterCount+=1,this._dragEnterCount>1)return;if(null===e.dataTransfer)return void this.classList.add("drop-invalid");const t=q(e.dataTransfer.items,this.accept,null!==this.multiple);this.classList.add(e.dataTransfer&&e.dataTransfer.items.length&&void 0===t[0]?"drop-invalid":"drop-valid")}_onDragLeave(){this._dragEnterCount-=1,0===this._dragEnterCount&&this._reset()}_onDrop(e){if(e.preventDefault(),null===e.dataTransfer)return;this._reset();const t=I(e.dataTransfer,this.accept,null!==this.multiple);void 0!==t&&this.dispatchEvent(new U("filedrop",{action:"drop",files:t}))}_onPaste(e){if(!e.clipboardData)return;const t=I(e.clipboardData,this.accept,void 0!==this.multiple);void 0!==t&&this.dispatchEvent(new U("filedrop",{action:"paste",files:t}))}_reset(){this._dragEnterCount=0,this.classList.remove("drop-valid"),this.classList.remove("drop-invalid")}}customElements.define("file-drop",T);P('snack-bar{display:block;position:fixed;left:0;bottom:0;width:100%;height:0;overflow:visible}._snackbar_1fpr7_11{position:fixed;display:flex;box-sizing:border-box;left:50%;bottom:24px;width:344px;margin-left:-172px;background:#2a2a2a;border-radius:2px;box-shadow:0 1px 4px rgba(0,0,0,.5);transform-origin:center;color:#eee;z-index:100;cursor:default;will-change:transform;animation:_snackbar-show_1fpr7_1 .3s ease 1 forwards}._snackbar_1fpr7_11[aria-hidden=true]{animation:_snackbar-hide_1fpr7_1 .3s ease 1 forwards}@keyframes _snackbar-show_1fpr7_1{0%{opacity:0;transform:scale(.5)}}@keyframes _snackbar-hide_1fpr7_1{to{opacity:0;transform:translateY(100%)}}@media (max-width:400px){._snackbar_1fpr7_11{width:100%;bottom:0;left:0;margin-left:0;border-radius:0}}._text_1fpr7_55{flex:1 1 auto;padding:16px;font-size:100%}._button_1fpr7_61{position:relative;flex:0 1 auto;padding:8px;height:100%;margin:auto 8px auto -8px;min-width:5em;background:none;border:none;border-radius:3px;color:#90ee90;font-weight:inherit;letter-spacing:.05em;font-size:100%;text-transform:uppercase;text-align:center;cursor:pointer;overflow:hidden;transition:background-color .2s ease;outline:none;text-decoration:none}._button_1fpr7_61:hover{background-color:rgba(0,0,0,.15)}._button_1fpr7_61:focus:before{content:"";position:absolute;left:50%;top:50%;width:120%;height:0;padding:0 0 120%;margin:-60% 0 0 -60%;background:hsla(0,0%,100%,.1);border-radius:50%;transform-origin:center;will-change:transform;animation:_focus-ring_1fpr7_1 .3s ease-out 1 forwards;pointer-events:none}@keyframes _focus-ring_1fpr7_1{0%{transform:scale(.01)}}');const O=HTMLElement;function V(e,t){const{timeout:n=0,actions:i=["dismiss"]}=t,o=document.createElement("div");o.className="_snackbar_1fpr7_11",o.setAttribute("aria-live","assertive"),o.setAttribute("aria-atomic","true"),o.setAttribute("aria-hidden","false");const s=document.createElement("div");s.className="_text_1fpr7_55",s.textContent=e,o.appendChild(s);const r=new Promise((e=>{let t;for(const n of i){const i=document.createElement("button");i.className="_button_1fpr7_61",i.textContent=n,i.addEventListener("click",(()=>{clearTimeout(t),e(n)})),o.appendChild(i)}n&&(t=self.setTimeout((()=>e("")),n))}));return[o,r]}customElements.define("snack-bar",class extends O{constructor(){super(...arguments),this._snackbars=[],this._processingQueue=!1}showSnackbar(e,t={}){return new Promise((n=>{this._snackbars.push([e,t,n]),this._processingQueue||this._processQueue()}))}async _processQueue(){for(this._processingQueue=!0;this._snackbars[0];){const[e,t,n]=this._snackbars[0],[i,o]=V(e,t);n(o),this.appendChild(i),await o,i.setAttribute("aria-hidden","true"),await new Promise((e=>{i.addEventListener("animationend",(()=>e()))})),i.remove(),this._snackbars.shift()}this._processingQueue=!1}});const j="_spinner-circle_18q42_78",N="_spinner-circle-clipper_18q42_114";P("@keyframes _spinner-left-spin_18q42_1{0%{transform:rotate(130deg)}50%{transform:rotate(-5deg)}to{transform:rotate(130deg)}}@keyframes _spinner-right-spin_18q42_1{0%{transform:rotate(-130deg)}50%{transform:rotate(5deg)}to{transform:rotate(-130deg)}}@keyframes _spinner-fade-out_18q42_1{to{opacity:0}}@keyframes _spinner-container-rotate_18q42_1{to{transform:rotate(1turn)}}@keyframes _spinner-fill-unfill-rotate_18q42_1{12.5%{transform:rotate(135deg)}25%{transform:rotate(270deg)}37.5%{transform:rotate(405deg)}50%{transform:rotate(540deg)}62.5%{transform:rotate(675deg)}75%{transform:rotate(810deg)}87.5%{transform:rotate(945deg)}to{transform:rotate(3turn)}}loading-spinner{--size:28px;--color:#4285f4;--stroke-width:3px;--delay:300ms;pointer-events:none;display:inline-block;position:relative;width:var(--size);height:var(--size);border-color:var(--color)}loading-spinner ._spinner-circle_18q42_78{position:absolute;top:0;left:0;right:0;bottom:0;box-sizing:border-box;height:100%;width:200%;border:var(--stroke-width) solid;border-color:inherit;border-bottom-color:transparent!important;border-radius:50%}loading-spinner ._spinner-gap-patch_18q42_98{position:absolute;box-sizing:border-box;top:0;left:45%;width:10%;height:100%;overflow:hidden;border-color:inherit}loading-spinner ._spinner-gap-patch_18q42_98 ._spinner-circle_18q42_78{width:1000%;left:-450%}loading-spinner ._spinner-circle-clipper_18q42_114{display:inline-block;position:relative;width:50%;height:100%;overflow:hidden;border-color:inherit}loading-spinner ._spinner-left_18q42_123 ._spinner-circle_18q42_78{border-right-color:transparent!important;transform:rotate(129deg);animation:_spinner-left-spin_18q42_1 1333ms cubic-bezier(.4,0,.2,1) infinite both}loading-spinner ._spinner-right_18q42_129 ._spinner-circle_18q42_78{left:-100%;border-left-color:transparent!important;transform:rotate(-129deg);animation:_spinner-right-spin_18q42_1 1333ms cubic-bezier(.4,0,.2,1) infinite both}loading-spinner._spinner-fadeout_18q42_137{animation:_spinner-fade-out_18q42_1 .4s cubic-bezier(.4,0,.2,1) forwards}loading-spinner ._spinner-container_18q42_141{width:100%;height:100%;border-color:inherit;animation:_spinner-container-rotate_18q42_1 1568ms linear infinite}loading-spinner ._spinner-layer_18q42_150{position:absolute;width:100%;height:100%;border-color:inherit;animation:_spinner-fill-unfill-rotate_18q42_1 5332ms cubic-bezier(.4,0,.2,1) infinite both}");const Q=HTMLElement;customElements.define("loading-spinner",class extends Q{constructor(){super(...arguments),this._delayTimeout=0}disconnectedCallback(){this.style.display="none",clearTimeout(this._delayTimeout)}connectedCallback(){this.style.display="none",this.innerHTML=`<div class="_spinner-container_18q42_141"><div class="_spinner-layer_18q42_150"><div class="${N} _spinner-left_18q42_123"><div class="${j}"></div></div><div class="_spinner-gap-patch_18q42_98"><div class="${j}"></div></div><div class="${N} _spinner-right_18q42_129"><div class="${j}"></div></div></div></div>`;const e=getComputedStyle(this).getPropertyValue("--delay").trim();let t=parseFloat(e);/\ds$/.test(e)&&(t*=1e3),this._delayTimeout=self.setTimeout((()=>{this.style.display=""}),t)}});const Y="_load-img_vzxu7_46",W="_top-wave_vzxu7_106",G="_info_vzxu7_125",H="_info-container_vzxu7_135",R="_info-content_vzxu7_140",Z="_info-title_vzxu7_160",K="_info-caption_vzxu7_166",J="_info-text-wrapper_vzxu7_183",$="_info-img-wrapper_vzxu7_199",X="_info-img_vzxu7_199",ee=[[[-.232,-1.029,.073,-1.029,.377,-1.029],[.565,-1.098,.755,-.86,.945,-.622],[.917,-.01,.849,.286,.782,.583],[.85,.687,.576,.819,.302,.951],[-.198,1.009,-.472,.877,-.746,.745],[-.98,.513,-1.048,.216,-1.116,-.08],[-.964,-.395,-.774,-.633,-.584,-.871]],[[-.505,-1.109,-.201,-1.109,.104,-1.109],[.641,-.684,.831,-.446,1.02,-.208],[1.041,.034,.973,.331,.905,.628],[.734,.794,.46,.926,.186,1.058],[-.135,.809,-.409,.677,-.684,.545],[-.935,.404,-1.002,.108,-1.07,-.189],[-.883,-.402,-.693,-.64,-.503,-.878]],[[-.376,-1.168,-.071,-1.168,.233,-1.168],[.732,-.956,.922,-.718,1.112,-.48],[1.173,.027,1.105,.324,1.038,.621],[.707,.81,.433,.943,.159,1.075],[-.096,1.135,-.37,1.003,-.644,.871],[-.86,.457,-.927,.161,-.995,-.136],[-.87,-.516,-.68,-.754,-.49,-.992]],[[-.309,-.998,-.004,-.998,.3,-.998],[.535,-.852,.725,-.614,.915,-.376],[1.05,-.09,.982,.207,.915,.504],[.659,.807,.385,.939,.111,1.071],[-.178,1.048,-.452,.916,-.727,.784],[-.942,.582,-1.009,.285,-1.077,-.011],[-1.141,-.335,-.951,-.573,-.761,-.811]]];class te extends f{componentDidMount(){if(window.matchMedia("(prefers-reduced-motion: reduce)").matches)return;const e=this.base;let t=!1;this.observer=new IntersectionObserver(((n,i)=>{for(const o of n){if(!o.isIntersecting)return t=!0,void(e.style.opacity="0");t&&(e.style.opacity="",e.animate({offset:0,opacity:"0",transform:"translateY(40px)"},{duration:300,easing:"ease"})),i.unobserve(o.target)}}),{threshold:.2}),this.observer.observe(e)}componentWillUnmount(){this.observer&&this.observer.disconnect()}render({children:e}){return p("div",null,e)}}const ne=[{description:"Large photo",size:"2.8MB",filename:"photo.jpg",url:"/c/demo-large-photo-a6b23f7b.jpg",iconUrl:"/c/icon-demo-large-photo-18da387a.jpg"},{description:"Artwork",size:"2.9MB",filename:"art.jpg",url:"/c/demo-artwork-c444f915.jpg",iconUrl:"/c/icon-demo-artwork-9eba1655.jpg"},{description:"Device screen",size:"1.6MB",filename:"pixel3.png",url:"/c/demo-device-screen-b9d088e8.png",iconUrl:"/c/icon-demo-device-screen-5d52d8b9.jpg"},{description:"SVG icon",size:"13KB",filename:"compressflow.svg",url:"/c/logo-bcc20dd5.svg",iconUrl:"/c/icon-demo-logo-326ed9b6.png"}],ie=matchMedia("(prefers-reduced-motion: reduce)").matches?void 0:e("./blob-anim-673880c9"),oe="introInstallButton-Purple",se=navigator.clipboard&&navigator.clipboard.read;class re extends f{constructor(){super(...arguments),this.state={showBlobSVG:!0},this.installingViaButton=!1,this.onFileChange=e=>{const t=e.target;try{if(!t.files)return;const e=[...t.files];this.props.onFiles(e)}catch(e){return void console.error(`Something went wrong while picking files: ${e}`)}finally{this.fileInput.value=""}},this.onOpenClick=()=>{this.fileInput.click()},this.onDemoClick=async(e,t)=>{try{this.setState({fetchingDemoIndex:e});const t=ne[e],n=await fetch(t.url).then((e=>e.blob())),i=new File([n],t.filename,{type:n.type});this.props.onFiles([i])}catch(e){this.setState({fetchingDemoIndex:void 0}),this.props.showSnack("Couldn't fetch demo image")}},this.onBeforeInstallPromptEvent=e=>{e.preventDefault(),this.setState({beforeInstallEvent:e});ga("send","event",{eventCategory:"pwa-install",eventAction:"promo-shown",nonInteraction:!0})},this.onInstallClick=async e=>{const t=this.state.beforeInstallEvent;if(!t)return;this.installingViaButton=!0,t.prompt();const{outcome:n}=await t.userChoice;ga("send","event",{eventCategory:"pwa-install",eventAction:"promo-clicked",eventLabel:oe,eventValue:"accepted"===n?1:0}),"dismissed"===n&&(this.installingViaButton=!1)},this.onAppInstalled=()=>{if(this.setState({beforeInstallEvent:void 0}),document.hidden)return;const e=this.installingViaButton?oe:"browser";ga("send","event","pwa-install","installed",e),this.installingViaButton=!1},this.onPasteClick=async()=>{let e;try{e=await navigator.clipboard.read()}catch(e){return void this.props.showSnack("No permission to access clipboard")}const t=await async function(e){for(const t of e){const e=t.types.find((e=>e.startsWith("image/")));if(e)return t.getType(e)}}(e);t?this.props.onFiles([new File([t],"image.unknown")]):this.props.showSnack("No image found in the clipboard")}}componentDidMount(){window.addEventListener("beforeinstallprompt",this.onBeforeInstallPromptEvent),window.addEventListener("appinstalled",this.onAppInstalled),ie&&ie.then((e=>{this.setState({showBlobSVG:!1},(()=>e.startBlobAnim(this.blobCanvas)))}))}componentWillUnmount(){window.removeEventListener("beforeinstallprompt",this.onBeforeInstallPromptEvent),window.removeEventListener("appinstalled",this.onAppInstalled)}render({},{fetchingDemoIndex:e,beforeInstallEvent:t,showBlobSVG:n}){return p("div",{class:"_intro_vzxu7_1 abs-fill"},p("input",{class:"_hide_vzxu7_18",ref:M(this,"fileInput"),type:"file",multiple:!0,onChange:this.onFileChange}),p("div",{class:"_main_vzxu7_22"},p("canvas",{ref:M(this,"blobCanvas"),class:"_blob-canvas_vzxu7_12 abs-fill"}),p("h1",{class:"_logo-container_vzxu7_36"},p("img",{class:"_logo_vzxu7_36",src:"data:image/svg+xml;charset=utf-8,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20viewBox%3D%220%200%20269.5%2081%22%3E%3Cstyle%3E%40font-face%7Bfont-family%3Afont%3Bfont-weight%3A700%3Bsrc%3Aurl(data%3Afont%2Fwoff%3Bbase64%2Cd09GMgABAAAAAASoAA4AAAAACOQAAARUAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAGhYbgnYcgXAGYABkEQwKhHiEFgsUAAE2AiQDJAQgBYJaByAbeQfIxId%2FN%2F1zb0JJk7qwVQyoSyhM6qJf2zN3wnP5MyX%2Fc6mabKKFAVwk4QDd%2B3f7Z41FoYvttryoVK6NsWls0wJjfeO38YjNiCJjtHr%2F1Q8CwAqDCAUBr2QweT2PEoyA1elzSuDRPDnUg8D2odZuRPU0jvQhDaxz5rMh0dmYGFMJs4nu1qE%2BeGjM%2FweALmNBwUEBOxTgRXxFMYpg%2F%2Fe8Ci0oLAD5Z%2Fl7jALyz0eIk0p5wwIKMGmoUVDk%2BI2Voc2UQOpf%2BV8SHykzsc2YglhhpAxij4qYCCDLLiZGpIxaxGLgdEfA8VXagNHBPOEGN4kdxIwSYCOPFjwtuDxUnqS%2BIGdGuWtrAgUsQYZRHpDu7Qiw6ukb7cWFrWzAQoFsxEIZPaFS7IAQDQ4cvEx4DjEL%2BW8XY9w93bi6CVjaTB8XOUO3aIVhv7jXI3UoIVGDVOAYRkFZmv6lSiyebcjPN%2BBqWJ91Zp43fwUwPxAfFchViYiY6etaTAWH6kKIhE7SgEaAL0DMDnZmf6FCMTrQj0EMY1SW726Rv5Tvki%2FJp%2FKJfCwf5XNoQXWWGi%2FLsNXQcgvxSnnJ8wFi43KUbaZAdno0JKEudnYudkHOvy6ZysqvkYTtt8jiu2VxJhNZfP99YVuKLLnyTX7rdSy8cT5WkrBgNPL1AkqEuyzOB%2BWZxXf5LSMoLzmnXHS9%2F9b7ZNHk7wAjFqQ3hMZy%2FnbFtQXbNAIRWP4jQ8kUVX7FG8L2Y4VH4%2FQvOjyltORefuthLJSWBnyUZCKMQBfeEbbfP3h5MvqPEuA12%2F1Or4a5yB8MYpzmv%2BDgZ3O9ntK5NjZdTEis7owRG%2FurcsYir%2B4JU2%2BcUdUY36zxbeoo1RsaBsJTkP7a8gPKCq64OCyytqmvKXVZE5mtjskqTmWW8Y3ttF43lZujm57W5%2BdN63XTObm6qWl9XsDErwGiVhMg%2FhoY%2BGugqNEE6n4NxH280sBFFITFZA%2FoMqby8zKmB3TZMfnqDC5tyrRUm%2FF7gKhWB4i%2FBfj%2FFiCq1QHi7%2F643nboYs9I1K%2FXB%2Fn%2F4eH%2B8COPf1aYl6yOya4KiL8ypq8%2FQso6O%2BHoL9pfsL%2FokJObGJlwoSkkBZds6wfb4pYDYzxCEjIL09ll83QG11IUGgUAAJrLVRQvuOTOlNTbpvzOCcwPZllp%2BcGK%2FbUffKT%2BwRxg%2FtrKyDwPAmVzJTtUmnnR%2FCa2bGU0B5hvtjI2u3ZLmoci%2FIQP0Fz40ipk0Hb402H4k7%2FhScuRQrJj7rXEg%2BBR85QaWsDzADqVUNUxBLbRYp0JnCMELkwBnsiOEWgYqDGjYaHCzRoFPPDyyy3gg68goh8DmMQQOtGODoxAhShEIBKx9DgD%2BtGPdvSgFSrkIAcitEolEz3oIVeRDxy%2B4lvVSiuGMEbLtUDr97BiNKIPw8hCvzLeYjOefMW4mq5hUSguFWNZnL3D6EQ%2F%2BtLPa3ktEYgO08G8O9bbQgkWAA%3D%3D)%20format('woff2')%7D%3C%2Fstyle%3E%3Ctext%20fill%3D%22%231F1F1F%22%20font-family%3D%22font%22%20font-size%3D%2250%22%20letter-spacing%3D%22-5%22%20transform%3D%22translate(92%2056.5)%22%3ECompressFlow%3C%2Ftext%3E%3Cg%20transform%3D%22translate(4%203.7)%22%3E%3Ccircle%20cx%3D%2236.5%22%20cy%3D%2236.8%22%20r%3D%2237%22%20fill%3D%22%2340403F%22%20stroke%3D%22%234A494A%22%20stroke-width%3D%227%22%2F%3E%3Cpath%20fill%3D%22%23E17926%22%20d%3D%22m12.3%2020.6-1.1-1.4%202.2-1.3a6%206%200%200%200-1.1%202.7zm9.6%2016.7h-1.8l.3%201.3%201.8-.5c-.2-.2-.3-.5-.3-.8zm-.7%2012-.3.4-.1%201%20.3-.1%201.5-.4.1-2.1-1.5%201.2zm36.2-36.2A47.7%2047.7%200%200%200%2018.6%2015c1%20.1%202%20.3%202.9.7%201.4-.6%202.8-1.2%204.3-1.6%204.6-1.5%209.4-2.3%2014.3-2.4%205%200%2010%20.9%2014.8%202.5a75.5%2075.5%200%200%200-6.7%2021.3%2045.8%2045.8%200%200%200%201.9%2020.4%2036.3%2036.3%200%200%201-18.3%205.4%2032%2032%200%200%201-10.2-1.7c-.7.3-1.5.4-2.2.4l-.2.6c4%201.6%208.3%202.4%2012.6%202.5%207.1%200%2014-2.4%2020.6-6.4-5.4-13.5-2.6-28%205-43.6zm-40%2014.2-1.1-2.1-.5.9.7%201.3.9-.1z%22%2F%3E%3Cpath%20fill%3D%22%23F78F20%22%20d%3D%22M49.1%2035.5c1.2-7.6%203.6-15%207-21.9a46.2%2046.2%200%200%200-35.9%201.7c2.1.5%203.9%201.6%205.4%203.1l.3.3c.6.5.9%201.4.5%202.1a5.6%205.6%200%200%201-2.7%203.2c-1.5.7-3.3.8-4.8.3-.5-.1-1-.2-1.4-.1-.5.1-.9.5-1.1.9l1.1%202.1%202.2-.1c1.9-.1%203.7-.1%205.5.1.8.1%201.7.3%202.5.6l.9.5c.3.3.5.7.6%201%201.2%203.3-1.2%206.2-4.2%207.2-1.2.4-2.4.6-3.6.7H21l.2%201.1%202.1-.6%203-.6c.8-.1%202-.3%202.6.2.3.3.6.7.7%201.2l.9%202.2c.2.6.3%201.3.2%201.9-.2%201.1-.9%202-1.9%202.5-1.8%201-3.6%201.8-5.5%202.6l-1.4.8-.1%201.7c2.2-.6%204.5-.7%206.7-.3l1.2.4c.6.4%201%201.2%201%202s-.3%201.5-.7%202.2a9%209%200%200%201-4.8%203.8l-2.8.8c-.7.2-1.3.5-2%20.6v.1c3.7%201.4%207.6%202.1%2011.5%202.1A37%2037%200%200%200%2051.3%2056a39.6%2039.6%200%200%201-2.2-20.5z%22%2F%3E%3Cpath%20fill%3D%22%23FDCDA0%22%20d%3D%22M37.4%2029.1c1.4-.2%202.7-2%203.1-4.2s-.9-4-3-3.8-3.3%202.3-3%204.5%201.6%203.7%202.9%203.5z%22%2F%3E%3Cpath%20fill%3D%22%23E17827%22%20d%3D%22M44.6%2041c-3.8-2.5-5.6-6.6-8.6-10.9-2.8%205.1-4%2010.5-7.6%2014.5%206%20.4%2011.3-1.3%2016.2-3.6z%22%2F%3E%3Cpath%20fill%3D%22%23C86228%22%20d%3D%22M40%2048.3C35.1%2043.8%2033.2%2037%2025.9%2033c1.1%206.3%201.3%2011.7-2.7%2016.3a30%2030%200%200%200%2016.8-1z%22%2F%3E%3Cpath%20fill%3D%22none%22%20stroke%3D%22%23E17827%22%20stroke-miterlimit%3D%2210%22%20stroke-width%3D%224%22%20d%3D%22M46%2043a41%2041%200%200%201-23.8%203.9c1.4-8.1.2-15.9-5.3-23.1a53.9%2053.9%200%200%201%2032.6-5c-4%208.6-5.8%2016.7-3.5%2024.2z%22%2F%3E%3Cpath%20fill%3D%22%23C96328%22%20d%3D%22m27.2%2019.6-.1-.3-.1-.2-.7-.2H26l-.1-.2a11.7%2011.7%200%200%200-7-3.5%206%206%200%200%200-1.9%200c-2.7.7-4.9%204.3-4.6%207%20.3%201.8%201.6%202.8%202.9%204l.4.1c.4.1.6-.7.8-1%20.2-.5.6-.8%201.1-1l1.5.1c1.6.5%203.3.4%204.8-.2.5-.2.9-.5%201.2-.9.1%200%20.2%200%20.3-.2l.8-.7.4-.2c.3-.4.5-.9.6-1.4v-1.2zm10.3%2014.5.2-.4c1.2-2.4%202.7-4.6%204.7-6.4%201.8-1.9%203.6-3.5%206-4a11%2011%200%200%201%205.1%200c1%200%202.1.3%203.1.7.9.6%201.7%201.4%202.2%202.3%201.7%202.6%203.2%205.3%204.6%208.1.9%201.7%201.9%203.4%201.9%205.4%200%201.2-.3%202.4-.6%203.6-1%203.1-2.2%206.2-3.7%209.2a16%2016%200%200%201-5.7%207.3c-3.9%202.6-8.8%202.1-13.2%201.5-.7%200-6.9-.7-7-1.3%200-.3%202-1%202.2-1a17%2017%200%200%200%206.7-5.3c.6-.9%201-1.9%201.2-2.9.2-1.5.8-3%201.6-4.4.9-1.3%202-2.2%202-3.9l-.1-1.7c0-.6.3-1%20.9-1.4.6-.2.7%200%20.9-.4l.1-.4c.2-1.5.8-2.9%201.6-4.2a12%2012%200%200%201-6.4%205%209%209%200%200%201-5.1.6%204.6%204.6%200%200%201-3.7-3.3c0-.8.1-1.8.5-2.7zm-7.7-2.8V31l-.2-1.6a1%201%200%200%200-.5-.6h-.2c-.2-.3-.5-.5-.8-.6-.8-.3-1.6-.5-2.5-.6-1.8-.2-3.7-.2-5.5%200-1.9%200-3.5%200-5.1.8a6.4%206.4%200%200%200-2.7%204.3c-.3%201.5%200%203.1.9%204.4%201.5%202%204%202.6%206.3%201.7.1%200%20.3%200%20.4-.2l.3-.6c.3-.5%201.2-.3%201.7-.3%201.2%200%202.4-.3%203.6-.7%201.6-.6%203-1.7%203.9-3.2.1%200%20.2%200%20.3-.3l.1-.3.1-1.2-.1-.6zm2.3%2010c0-.9-.3-1.6-.6-2.2-.3-.4-.4-.9-.9-1l-.9.1-.3-.5c-.6-.6-1.8-.4-2.6-.3a31.3%2031.3%200%200%200-8.8%202.2c-1.7.6-3.5%201.2-4.7%202.6a6%206%200%200%200-1.4%202.1c-.5%202.2.1%205%202.3%206.1%201.4.6%202.9.7%204.4.4.8%200%201.5-.3%202.1-.8l.8-.8c.6-.6%201.3-1%202-1.3%201.9-.7%203.8-1.5%205.6-2.4%201-.5%201.7-1.4%202-2.5l.1-.4.5-.2c.2%200%20.4-.3.6-.5-.2%200-.1-.4-.2-.7zm-.6%2011c-.1-.6-.4-1.3-1-1.4h-.4l-.3-.3-1.2-.4c-2.4-.4-4.9-.3-7.3.4-2%20.6-5.7%202-5.9%204.4-.1%201%20.2%202%20.8%202.7.7%201.2%201.9%202%203.3%202.2%201%200%202.1-.3%203-.7%201-.3%201.9-.4%202.8-.8a9%209%200%200%200%204.5-3.4h.1c.5%200%20.9-.3%201.2-.6l.3-.5c.3-.4.3-1%20.1-1.6z%22%20opacity%3D%22.5%22%2F%3E%3Cpath%20fill%3D%22%23F7DDC4%22%20d%3D%22M25.7%2018.5a12.3%2012.3%200%200%200-7-3.5%204%204%200%200%200-1.9.1c-2.7.7-4.9%204.3-4.6%207%20.3%201.7%201.5%202.8%202.8%203.8l.4.2c.4.1.6-.7.8-1%20.3-.4.7-.8%201.1-.9.5-.1%201%200%201.4.1%201.6.5%203.3.4%204.9-.2a5.3%205.3%200%200%200%202.7-3.2c.2-.4.2-.8.1-1.2l-.6-.9-.1-.3z%22%2F%3E%3Cpath%20fill%3D%22%23EACEB1%22%20d%3D%22m23.7%2024.1.5-.3c-1.6-.2-2.2%200-2.6-.1s-1.7%200-2.2-.2-1.1-.1-1.5-.2l-1.8-.3c-.9-.1-1.7-.5-2.3-.1l-1%20.7a8%208%200%200%200%202.7%202.4c.4.1.6-.7.8-1%20.3-.4.7-.8%201.1-.9.5-.1%201%200%201.4.1%201.7.6%203.4.5%204.9-.1z%22%2F%3E%3Cpath%20fill%3D%22%23F8E9DB%22%20d%3D%22m26.6%2019.7-.6-.9-.3-.3a12.3%2012.3%200%200%200-7-3.5%204%204%200%200%200-1.9.1%204.4%204.4%200%200%200-2.2%204c.2%203.5.9%203.5%202.8%205.1.5-.1%201%200%201.4.1%201.6.5%203.3.4%204.9-.2a5.3%205.3%200%200%200%202.7-3.2c.2-.4.3-.8.2-1.2z%22%20opacity%3D%22.3%22%2F%3E%3Cpath%20fill%3D%22%2334B9EB%22%20d%3D%22M26.9%2019.2c-.3-.2-.5-.3-.8-.3l-.9-.3-1.5-.4c-1.2-.3-2.6-.6-3.7%200a2%202%200%200%200-1.1%201.5c-.1.6.1%201.2.5%201.6a5%205%200%200%200%201.8.9c1.1.5%202.3.8%203.5%201h.4l.2-.2c.4-.2.7-.5%201-.9.3-.4.5-.9.6-1.4l.1-1.1-.1-.4z%22%2F%3E%3Cpath%20fill%3D%22%23F7DDC4%22%20d%3D%22m63.3%2034.3-2.8-5.2-.5-.9-.2-.3-.4-.6-.2-.3-.6-.9c-.5-1-1.3-1.8-2.2-2.3-1-.4-2-.6-3.1-.7-1.7-.4-3.4-.4-5.1-.1a12%2012%200%200%200-6%204.1c-1.9%201.9-3.5%204-4.7%206.4l-.2.4c-.4.9-.5%201.9-.3%202.8.5%201.7%201.9%203%203.7%203.3H43c1-.1%202-.4%202.9-.8%202.6-.9%204.9-2.6%206.4-4.9l-1.1%202.1-.6%202.1-.1.4c-.2.3-.6.3-.9.4l-.3.2c-.3%205.9.7%2011.8%202.9%2017.2a45.5%2045.5%200%200%201-9.4%204.6c4.2.6%208.7.8%2012.4-1.6%202.7-1.8%204.4-4.5%205.7-7.4%201.4-3%202.6-6%203.6-9.2.4-1.2.6-2.4.6-3.6%200-1.8-1-3.5-1.8-5.2z%22%2F%3E%3Cpath%20fill%3D%22%23EACEB1%22%20d%3D%22M49.4%2041.2V44l.1.8v.2l.1%201%20.3%202.1v.2l.1.8.1.3.1.7.1.4.1.6.1.4.2.6.1.4.2.6.1.4.2.6.1.4.2.7.1.3.4%201-1.1.7-.1.1-.9.7-.1.1-1%20.6-.1.1-1%20.5-.2.1-1%20.5-.1.1-1%20.5-.1.1-1.1.4h-.1l-1.1.4c4.2.6%208.7.8%2012.4-1.6%201.1-.7%202.1-1.7%202.9-2.7a27.5%2027.5%200%200%200-9-15.9zm8.7-12c1.1-1.9.9-1.6.2-3.6a5.8%205.8%200%200%200-1.8-1.7%2079%2079%200%200%200-5-1%207.3%207.3%200%200%201%202.9%205.7c0%201.7-1.5%204.4-2.2%205.8s-3.7%207.3.6%201.9c1.3-1.7%202.9-2.7%203.8-4.3l1.5-2.8z%22%2F%3E%3Cpath%20fill%3D%22%23F8E9DB%22%20d%3D%22M53.4%2024.2c-.4-.4-1.2-1.3-1.8-1.5-1-.1-2%200-3%20.2-2.4.6-4.4%202.3-6%204.1-1.9%201.9-3.5%204-4.7%206.4l-.2.4c-.4.9-.5%201.9-.3%202.8a4.4%204.4%200%200%200%203.4%203.2c3.6-.2%206.9-1.8%209.4-4.3%204.1-4.1%205.4-9.1%203.2-11.3z%22%20opacity%3D%22.3%22%2F%3E%3Cpath%20fill%3D%22%239F87BF%22%20d%3D%22m42.2%2031-.9.6-2.1%201.8c-.7.6-1.7%201.2-2%202.2a3%203%200%200%200%20.1%201.6c.2.9.8%201.7%201.5%202.2%201.4.9%203%20.1%204.1-.8s2.4-1.6%203-2.9-.2-2.4-.9-3.3-1.6-1.7-2.7-1.4h-.1z%22%2F%3E%3Cpath%20fill%3D%22%23FFF%22%20d%3D%22M45.3%2032.6%2038%2038.5l.8.8c1.4.9%203%20.1%204.1-.8s2.4-1.6%203-2.9c.6-.9%200-2.1-.6-3z%22%20opacity%3D%22.1%22%2F%3E%3Cpath%20fill%3D%22%23F7DDC4%22%20d%3D%22m28.5%2028.5-.9-.5c-.8-.3-1.6-.5-2.5-.6-1.8-.2-3.7-.2-5.5-.1-1.9%200-3.5%200-5.1.9a6.2%206.2%200%200%200-2.7%204.3c-.3%201.5%200%203.1.9%204.4%201.5%201.9%204%202.6%206.3%201.7l.4-.2.3-.6c.3-.5%201.2-.3%201.7-.3%201.2-.1%202.4-.3%203.6-.7%203-1.1%205.4-3.9%204.2-7.2a3%203%200%200%200-.7-1.1z%22%2F%3E%3Cpath%20fill%3D%22%23EACEB1%22%20d%3D%22M20.6%2039.8c.5-.1-.1-1.4-.5-2.2l-.2-.1c.4-.2%201.1-.1%201.5-.1%201.2-.1%202.4-.3%203.6-.7a7.3%207.3%200%200%200%203.3-2.4c-2.6-.3-3.8%202.2-7.8%201.8a36%2036%200%200%200-4.7.3c-1.5%200-2.7-.7-3.2.2l.1.2a5%205%200%200%200%203.4%202h.1l.9.1%203.2.8.3.1z%22%2F%3E%3Cpath%20fill%3D%22%23F8E9DB%22%20d%3D%22m28.5%2028.5-.9-.5c-.8-.3-1.6-.5-2.5-.6-1.8-.2-3.7-.2-5.5-.1-1.9%200-4.4.9-4.7%203.9-.5%205.7%208.2%205.8%2010.4%205.3a5%205%200%200%200%204.1-5.9l-.3-1a2%202%200%200%200-.6-1.1z%22%20opacity%3D%22.3%22%2F%3E%3Cpath%20fill%3D%22%235D509E%22%20d%3D%22M21.3%2031.4c0-.6.2-1.1.6-1.5a3%203%200%200%201%202-.9l1.2-.2%203.3-.3.5.1c.3.1.4.4.5.6.2.5.3%201.1.2%201.6l-.2%202.2-.1.3c-.2.3-.7.4-1%20.4a20.6%2020.6%200%200%201-5.5-.1c-1-.4-1.5-1.3-1.5-2.2z%22%2F%3E%3Cpath%20fill%3D%22%23DDC1A8%22%20d%3D%22m15.8%2025.6-.7-.2h-.6l.6.5h.2l.2.2.3-.1.2-.3v-.1h-.2zm.2%2013.3c1.4%200%202.8.4%204.2.9h.4c.5-.1-.1-1.4-.5-2.2-.1-.1-.2-.2-.3-.1h-.1c-.6.9-3.3%201.3-3.7%201.4zm33.5%206.3V45zm-.1-2.4v-1%201zm.3-3.4c.3-.1.8-.1.9-.4v-.4a9%209%200%200%201%20.7-2.2l1-2.1a9%209%200%200%201-2.8%203l-.2%202.2.4-.1zm-.3%204.6v1-1zm0-1.2v1-1zm0-3.2z%22%2F%3E%3Cpath%20fill%3D%22%23F7DDC4%22%20d%3D%22M11.6%2044.3a6%206%200%200%201%201.4-2.1c1.2-1.3%203-1.9%204.7-2.6a38.6%2038.6%200%200%201%208.8-2.2c.8-.1%202-.3%202.6.3.3.3.5.7.7%201.2l.8%202.3c.2.6.2%201.3.2%201.9-.3%201.1-1%202-2%202.5-1.8.9-3.7%201.7-5.6%202.4-.7.3-1.4.7-2%201.3a4.2%204.2%200%200%201-2.9%201.6c-1.5.3-3%20.2-4.4-.4-2.2-1.3-2.8-4-2.3-6.2z%22%2F%3E%3Cpath%20fill%3D%22%23EACEB1%22%20d%3D%22M20%2050.1c.6-.4%201.1-.9%201.5-1.4l.1-.1.1.1c.4.3-.7%201.2-.6%201.7v.5c-.1.2-.3.1-.5.1-1.3.2-.7-.5-2-.2l1.4-.7zm8.6-5.8c-.6%200-7.6%202.8-8.6%203.1-1.4.6-2.8.9-4.2%201-1.6%200-2%20.1-2.5.7l-.5.5%201.1.8c1.4.6%202.9.7%204.4.4.8%200%201.5-.3%202.1-.8l.8-.8c.6-.6%201.3-1%202-1.3%201.9-.7%203.8-1.5%205.6-2.4.6-.3%201.1-.8%201.4-1.3l-1.6.1z%22%2F%3E%3Cpath%20fill%3D%22%23F8E9DB%22%20d%3D%22m30.6%2041.1-.8-2.3c-.1-.4-.4-.8-.7-1.2-.6-.6-1.8-.4-2.6-.3-1%20.1-2%20.2-3%20.5-1.5.3-3%20.8-4.5%201.3-2.9%201.8-3.8%204-3.1%205.9.8%202.4%204.6%203%208.6%202%201.5-.6%202.9-.7%204.3-1.5%201-.5%201.7-1.4%202-2.5%200-.7%200-1.3-.2-1.9z%22%20opacity%3D%22.3%22%2F%3E%3Cpath%20fill%3D%22%2341439A%22%20d%3D%22M23.9%2040c.7-.6%201.5-1.1%202.4-1.2l2.8-.6%201.3-.2c.3.1.6.5.9%201%20.4.7.6%201.4.6%202.1l-.1.8-.6.5-1.5.6-2.4.7c-1%20.2-2.1.5-3-.1-.4-.3-.7-.8-.8-1.3a3%203%200%200%201%20.2-2.1l.2-.2z%22%2F%3E%3Cpath%20fill%3D%22%23FFF%22%20d%3D%22m31.8%2040.1-8.1%202.3c.1.4.4.8.7%201.1.8.7%202%20.4%203%20.1l2.4-.7%201.5-.6.6-.5.1-.8-.2-.9z%22%20opacity%3D%22.4%22%2F%3E%3Cpath%20fill%3D%22%23F7DDC4%22%20d%3D%22m29.7%2050.5-1.2-.4c-2.4-.4-4.9-.3-7.3.4-2%20.6-5.7%201.9-5.9%204.4-.1%201%20.2%201.9.8%202.7.7%201.2%201.9%202%203.3%202.2%201%200%202.1-.3%203-.7%201-.3%201.9-.4%202.8-.8%202-.7%203.7-2%204.8-3.7.4-.6.7-1.4.7-2.2%200-.8-.4-1.5-1-1.9z%22%2F%3E%3Cpath%20fill%3D%22%23EACEB1%22%20d%3D%22M26%2056.9c-1.7.6-3.1.7-4.6%201.1s-3-1.2-3.9%201c.5.4%201.2.7%201.9.7%201%200%202.1-.3%203-.7%201-.3%201.9-.4%202.8-.8a9%209%200%200%200%204.2-3c-1.3.8-2.6%201.4-3.4%201.7z%22%2F%3E%3Cpath%20fill%3D%22%23F8E9DB%22%20d%3D%22m29.7%2050.5-1.2-.4c-2.4-.4-4.8-.3-7.1.4-1.6%201.4-2.4%203-1.9%204.4.6%202.1%202.9%204.2%207.6%202.6a9.2%209.2%200%200%200%203-2.9c.4-.6.7-1.4.7-2.2-.1-.7-.5-1.5-1.1-1.9z%22%20opacity%3D%22.3%22%2F%3E%3Cpath%20fill%3D%22%23005C9F%22%20d%3D%22M23.8%2054c.4.8%201.2%201.4%202.1%201.5.6.1%201.2.1%201.8-.1l2.1-.5c.5-.1.9-.3%201.2-.6l.3-.5c.2-.6.2-1.2%200-1.7-.1-.6-.4-1.3-1-1.4h-1c-1%20.1-1.9.5-2.9.7a5%205%200%200%200-1.7.6%202%202%200%200%200-1%201.4l.1.6z%22%2F%3E%3Cpath%20fill%3D%22%23FFF%22%20d%3D%22m19%2019.5-.1.2c-.1.6.1%201.2.5%201.6a5%205%200%200%200%201.8%201l3.5%201h.4l.2-.3c.4-.2.7-.5%201-.9l.3-.6-7.6-2zm2.3%2012.2c.1.8.6%201.5%201.4%202l1.2.1h4.3c.3-.2.8-.2%201-.5l.1-.3.2-2-8.2.7zm2.6%2022.4c.4.8%201.2%201.3%202%201.4h1.8l2.1-.6c.5%200%20.9-.3%201.2-.6l.3-.5c.2-.5.2-1%20.1-1.5l-7.5%201.8z%22%20opacity%3D%22.3%22%2F%3E%3C%2Fg%3E%3C%2Fsvg%3E%0A",alt:"CompressFlow",width:"539",height:"162"})),p("div",{class:Y},n&&p("svg",{class:"_blob-svg_vzxu7_53 abs-fill",viewBox:"-1.25 -1.25 2.5 2.5",preserveAspectRatio:"xMidYMid slice"},ee.map((e=>p("path",{d:e.map(((t,n)=>{const i=n===e.length-1?0:n+1;let o="";return 0===n&&(o+=`M${t[2]} ${t[3]}`),o+`C${t[4]} ${t[5]} ${e[i][0]} ${e[i][1]} ${e[i][2]} ${e[i][3]}`})).join("")})))),p("div",{class:"_load-img-content_vzxu7_64",style:{visibility:""}},p("button",{class:"_load-btn_vzxu7_81 unbutton",onClick:this.onOpenClick},p("svg",{viewBox:"0 0 24 24",class:"_load-icon_vzxu7_85"},p("path",{d:"M19 7v3h-2V7h-3V5h3V2h2v3h3v2h-3zm-3 4V8h-3V5H5a2 2 0 00-2 2v12c0 1.1.9 2 2 2h12a2 2 0 002-2v-8h-3zM5 19l3-4 2 3 3-4 4 5H5z"}))),p("div",null,p("span",{class:"_drop-text_vzxu7_363"},"Drop "),"OR"," ",se?p("button",{class:"_paste-btn_vzxu7_93 unbutton",onClick:this.onPasteClick},"Paste"):"Paste")))),p("div",{class:"_demos-container_vzxu7_100"},p("svg",{viewBox:"0 0 1920 140",class:W},p("path",{d:"M1920 0l-107 28c-106 29-320 85-533 93-213 7-427-36-640-50s-427 0-533 7L0 85v171h1920z",class:"_sub-wave_vzxu7_117"}),p("path",{d:"M0 129l64-26c64-27 192-81 320-75 128 5 256 69 384 64 128-6 256-80 384-91s256 43 384 70c128 26 256 26 320 26h64v96H0z",class:"_main-wave_vzxu7_113"})),p("div",{class:"_content-padding_vzxu7_237"},p("p",{class:"_demo-title_vzxu7_297"},"Or ",p("strong",null,"try one")," of these:"),p("ul",{class:"_demos_vzxu7_100"},ne.map(((t,n)=>p("li",null,p("button",{class:"unbutton",onClick:e=>this.onDemoClick(n,e)},p("div",{class:"_demo-container_vzxu7_324"},p("div",{class:"_demo-icon-container_vzxu7_340"},p("img",{class:"_demo-icon_vzxu7_340",src:t.iconUrl,alt:t.description}),e===n&&p("div",{class:"_demo-loader_vzxu7_350 abs-fill"},p("loading-spinner",null))),p("div",{class:"_demo-size_vzxu7_331"},t.size))))))))),p("div",{class:"_bottom-wave_vzxu7_121"},p("svg",{viewBox:"0 0 1920 79",class:W},p("path",{d:"M0 59l64-11c64-11 192-34 320-43s256-5 384 4 256 23 384 34 256 21 384 14 256-30 320-41l64-11v94H0z",class:"_info-wave_vzxu7_219"}))),p("section",{class:G},p("div",{class:H},p(te,null,p("div",{class:R},p("div",{class:J},p("h2",{class:Z},"Small"),p("p",{class:K},"Smaller images mean faster load times. CompressFlow can reduce file size and maintain high quality.")),p("div",{class:$},p("img",{class:X,src:"/c/small-db1eae6f.svg",alt:"silhouette of a large 1.4 megabyte image shrunk into a smaller 80 kilobyte image",width:"536",height:"522"})))))),p("section",{class:G},p("div",{class:H},p(te,null,p("div",{class:R},p("div",{class:J},p("h2",{class:Z},"Simple"),p("p",{class:K},"Open your image, inspect the differences, then save instantly. Feeling adventurous? Adjust the settings for even smaller files.")),p("div",{class:$},p("img",{class:X,src:"/c/simple-258b6ed5.svg",alt:"grid of multiple shrunk images displaying various options",width:"538",height:"384"})))))),p("section",{class:G},p("div",{class:H},p(te,null,p("div",{class:R},p("div",{class:J},p("h2",{class:Z},"Secure"),p("p",{class:K},"Worried about privacy? Images never leave your device since CompressFlow does all the work locally.")),p("div",{class:$},p("img",{class:X,src:"/c/secure-a66bbdfe.svg",alt:"silhouette of a cloud with a 'no' symbol on top",width:"498",height:"333"})))))),p("footer",{class:"_footer_vzxu7_223"},p("div",{class:"_footer-container_vzxu7_228"},p("svg",{viewBox:"0 0 1920 79",class:W},p("path",{d:"M0 59l64-11c64-11 192-34 320-43s256-5 384 4 256 23 384 34 256 21 384 14 256-30 320-41l64-11v94H0z",class:"_footer-wave_vzxu7_233"})),p("div",{class:"_footer-padding_vzxu7_241"},p("footer",{class:"_footer-items_vzxu7_245"},p("a",{class:"_footer-link_vzxu7_260",href:"https://github.com/GoogleChromeLabs/compressflow/blob/dev/README.md#privacy"},"Privacy"),p("a",{class:"_footer-link-with-logo_vzxu7_265 _footer-link_vzxu7_260",href:"https://github.com/GoogleChromeLabs/compressflow"},p("img",{src:"/c/github-logo-bc05494c.svg",alt:"",width:"10",height:"10"}),"Source on Github"))))),t&&p("button",{class:"_install-btn_vzxu7_284 unbutton",onClick:this.onInstallClick},"Install"))}}const ae="/editor",le=e("./Compress-bb598df5"),ce=e("./sw-bridge-00e498e8");function _e(){window.history.back()}class de extends f{constructor(){super(),this.state={awaitingShareTarget:new URL(location.href).searchParams.has("share-target"),isEditorOpen:!1,files:[],Compress:void 0},this.onFileDrop=({files:e})=>{e&&0!==e.length&&(this.openEditor(),this.setState({files:e}))},this.onIntroPickFile=e=>{this.openEditor(),this.setState({files:e})},this.showSnack=(e,t={})=>{if(!this.snackbar)throw Error("Snackbar missing");return this.snackbar.showSnackbar(e,t)},this.onPopState=()=>{this.setState({isEditorOpen:location.pathname===ae})},this.openEditor=()=>{if(this.state.isEditorOpen)return;const e=new URL(location.href);e.pathname=ae,history.pushState(null,"",e.href),this.setState({isEditorOpen:!0})},le.then((e=>{this.setState({Compress:e.default})})).catch((()=>{this.showSnack("Failed to load app")})),ce.then((async({offliner:e,getSharedImage:t})=>{if(e(this.showSnack),!this.state.awaitingShareTarget)return;const n=await t();history.replaceState("","","/"),this.openEditor(),this.setState({files:[n],awaitingShareTarget:!1})})),document.body.addEventListener("gesturestart",(e=>{e.preventDefault()})),window.addEventListener("popstate",this.onPopState)}render({},{files:e,isEditorOpen:t,Compress:n,awaitingShareTarget:i}){const o=i||t&&!n;return p("div",{class:"_app_11rsf_1"},p("file-drop",{onfiledrop:this.onFileDrop,class:"_drop_11rsf_11",multiple:!0},o?p("loading-spinner",{class:"_app-loader_11rsf_61"}):t?n&&p(n,{files:e,showSnack:this.showSnack,onBack:_e}):p(re,{onFiles:this.onIntroPickFile,showSnack:this.showSnack}),p("snack-bar",{ref:M(this,"snackbar")})))}}const pe=document.getElementById("app");!async function(){!function(e,t,i){var o,s,c;n.__&&n.__(e,t),s=(o=i===r)?null:i&&i.__k||t.__k,e=p(h,null,[e]),c=[],z(t,(o?t:i||t).__k=e,s||a,a,void 0!==t.ownerSVGElement,i&&!o?[i]:s?null:t.childNodes.length?l.slice.call(t.childNodes):null,c,i||a,o),C(c,e)}(p(de,null),pe)}();{const e=navigator.standalone||window.matchMedia("(display-mode: standalone)").matches?"standalone":"browser";window.ga=window.ga||((...e)=>(ga.q=ga.q||[]).push(e)),ga("create","UA-128752250-1","auto"),ga("set","transport","beacon"),ga("set","dimension1",e),ga("send","pageview","/index.html",{title:"CompressFlow"}),addEventListener("load",(()=>{const e=document.createElement("script");e.src="https://www.google-analytics.com/analytics.js",document.head.appendChild(e)}))}t.appendCss=P,t.d=f,t.h=p,t.linkRef=M,t.loadImg=Y,t.p=h,t.startBlobs=ee,Object.defineProperty(t,"__esModule",{value:!0})}));
