define(["exports"],(function(e){const t=Symbol("Comlink.proxy"),n=Symbol("Comlink.endpoint"),r=Symbol("Comlink.releaseProxy"),a=Symbol("Comlink.thrown"),s=e=>"object"==typeof e&&null!==e||"function"==typeof e,o=new Map([["proxy",{canHandle:e=>s(e)&&e[t],serialize(e){const{port1:t,port2:n}=new MessageChannel;return i(e,t),[n,[n]]},deserialize:e=>(e.start(),u(e))}],["throw",{canHandle:e=>s(e)&&a in e,serialize({value:e}){let t;return t=e instanceof Error?{isError:!0,value:{message:e.message,name:e.name,stack:e.stack}}:{isError:!1,value:e},[t,[]]},deserialize(e){if(e.isError)throw Object.assign(new Error(e.value.message),e.value);throw e.value}}]]);function i(e,n=self){n.addEventListener("message",(function r(s){if(!s||!s.data)return;const{id:o,type:u,path:l}=Object.assign({path:[]},s.data),p=(s.data.argumentList||[]).map(d);let f;try{const n=l.slice(0,-1).reduce(((e,t)=>e[t]),e),r=l.reduce(((e,t)=>e[t]),e);switch(u){case 0:f=r;break;case 1:n[l.slice(-1)[0]]=d(s.data.value),f=!0;break;case 2:f=r.apply(n,p);break;case 3:f=function(e){return Object.assign(e,{[t]:!0})}(new r(...p));break;case 4:{const{port1:t,port2:n}=new MessageChannel;i(e,n),f=function(e,t){return m.set(e,t),e}(t,[t])}break;case 5:f=void 0}}catch(e){f={value:e,[a]:0}}Promise.resolve(f).catch((e=>({value:e,[a]:0}))).then((e=>{const[t,a]=h(e);n.postMessage(Object.assign(Object.assign({},t),{id:o}),a),5===u&&(n.removeEventListener("message",r),c(n))}))})),n.start&&n.start()}function c(e){(function(e){return"MessagePort"===e.constructor.name})(e)&&e.close()}function u(e,t){return p(e,[],t)}function l(e){if(e)throw new Error("Proxy has been released and is not useable")}function p(e,t=[],a=function(){}){let s=!1;const o=new Proxy(a,{get(n,a){if(l(s),a===r)return()=>g(e,{type:5,path:t.map((e=>e.toString()))}).then((()=>{c(e),s=!0}));if("then"===a){if(0===t.length)return{then:()=>o};const n=g(e,{type:0,path:t.map((e=>e.toString()))}).then(d);return n.then.bind(n)}return p(e,[...t,a])},set(n,r,a){l(s);const[o,i]=h(a);return g(e,{type:1,path:[...t,r].map((e=>e.toString())),value:o},i).then(d)},apply(r,a,o){l(s);const i=t[t.length-1];if(i===n)return g(e,{type:4}).then(d);if("bind"===i)return p(e,t.slice(0,-1));const[c,u]=f(o);return g(e,{type:2,path:t.map((e=>e.toString())),argumentList:c},u).then(d)},construct(n,r){l(s);const[a,o]=f(r);return g(e,{type:3,path:t.map((e=>e.toString())),argumentList:a},o).then(d)}});return o}function f(e){const t=e.map(h);return[t.map((e=>e[0])),(n=t.map((e=>e[1])),Array.prototype.concat.apply([],n))];var n}const m=new WeakMap;function h(e){for(const[t,n]of o)if(n.canHandle(e)){const[r,a]=n.serialize(e);return[{type:3,name:t,value:r},a]}return[{type:0,value:e},m.get(e)||[]]}function d(e){switch(e.type){case 3:return o.get(e.name).deserialize(e.value);case 0:return e.value}}function g(e,t,n){return new Promise((r=>{const a=new Array(4).fill(0).map((()=>Math.floor(Math.random()*Number.MAX_SAFE_INTEGER).toString(16))).join("-");e.addEventListener("message",(function t(n){n.data&&n.data.id&&n.data.id===a&&(e.removeEventListener("message",t),r(n.data))})),e.start&&e.start(),e.postMessage(Object.assign({id:a},t),n)}))}e.expose=i,e.getContainOffsets=function(e,t,n,r){const a=n/r;if(a>e/t){const n=e/a;return{sw:e,sh:n,sx:0,sy:(t-n)/2}}const s=t*a;return{sh:t,sw:s,sx:(e-s)/2,sy:0}},e.wrap=u}));
