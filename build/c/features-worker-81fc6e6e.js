if(!self.define){let n={};const r=(r,e)=>(r=r.startsWith(location.origin)?r:new URL(r+".js",e).href,n[r]||new Promise((n=>{if("document"in self){const e=document.createElement("link");e.rel="preload",e.as="script",e.href=r,e.onload=()=>{const e=document.createElement("script");e.src=r,e.onload=n,document.head.appendChild(e)},document.head.appendChild(e)}else self.nextDefineUri=r,importScripts(r),n()})).then((()=>{let e=n[r];if(!e)throw new Error(`Module ${r} didn’t register its module`);return e})));self.define=(e,t)=>{const o=self.nextDefineUri||("document"in self?document.currentScript.src:"")||location.href;if(n[o])return;let a={};const i=n=>r(n,o),u={module:{uri:o},exports:a,require:i};n[o]=Promise.resolve().then((()=>Promise.all(e.map((n=>u[n]||i(n)))))).then((n=>(t(...n),a)))}}define(["module","require","./util-06ce6ead","./supports-wasm-threads-b0989f01"],(function(n,r,e,t){function o(n,r){return console.time(n),r().finally((()=>console.timeEnd(n)))}function a(n){return n({noInitialRun:!0})}function i(n){return new Response(n).arrayBuffer()}let u;var c,f=(c=n.uri,function(r){var e,t;(r=void 0!==(r=r||{})?r:{}).ready=new Promise((function(n,r){e=n,t=r}));var o,a={};for(o in r)r.hasOwnProperty(o)&&(a[o]=r[o]);var i,u=!0,f="";f=self.location.href,c&&(f=c),f=0!==f.indexOf("blob:")?f.substr(0,f.lastIndexOf("/")+1):"",i=function(n){var r=new XMLHttpRequest;return r.open("GET",n,!1),r.responseType="arraybuffer",r.send(null),new Uint8Array(r.response)};var s,l,p=r.print||console.log.bind(console),d=r.printErr||console.warn.bind(console);for(o in a)a.hasOwnProperty(o)&&(r[o]=a[o]);a=null,r.arguments&&r.arguments,r.thisProgram&&r.thisProgram,r.quit&&r.quit,r.wasmBinary&&(s=r.wasmBinary),r.noExitRuntime,"object"!=typeof WebAssembly&&H("no native wasm support detected");var h=!1,v=new TextDecoder("utf8");function y(n,r){if(!n)return"";for(var e=n+r,t=n;!(t>=e)&&w[t];)++t;return v.decode(w.subarray(n,t))}var g,m,w,b,T,_,A,E,P,C,W=new TextDecoder("utf-16le");function F(n,r){for(var e=n,t=e>>1,o=t+r/2;!(t>=o)&&T[t];)++t;return e=t<<1,W.decode(w.subarray(n,e))}function R(n,r,e){if(void 0===e&&(e=2147483647),e<2)return 0;for(var t=r,o=(e-=2)<2*n.length?e/2:n.length,a=0;a<o;++a){var i=n.charCodeAt(a);b[r>>1]=i,r+=2}return b[r>>1]=0,r-t}function k(n){return 2*n.length}function U(n,r){for(var e=0,t="";!(e>=r/4);){var o=_[n+4*e>>2];if(0==o)break;if(++e,o>=65536){var a=o-65536;t+=String.fromCharCode(55296|a>>10,56320|1023&a)}else t+=String.fromCharCode(o)}return t}function S(n,r,e){if(void 0===e&&(e=2147483647),e<4)return 0;for(var t=r,o=t+e-4,a=0;a<n.length;++a){var i=n.charCodeAt(a);if(i>=55296&&i<=57343&&(i=65536+((1023&i)<<10)|1023&n.charCodeAt(++a)),_[r>>2]=i,(r+=4)+4>o)break}return _[r>>2]=0,r-t}function I(n){for(var r=0,e=0;e<n.length;++e){var t=n.charCodeAt(e);t>=55296&&t<=57343&&++e,r+=4}return r}function O(n){g=n,r.HEAP8=m=new Int8Array(n),r.HEAP16=b=new Int16Array(n),r.HEAP32=_=new Int32Array(n),r.HEAPU8=w=new Uint8Array(n),r.HEAPU16=T=new Uint16Array(n),r.HEAPU32=A=new Uint32Array(n),r.HEAPF32=E=new Float32Array(n),r.HEAPF64=P=new Float64Array(n)}r.INITIAL_MEMORY;var x=[],j=[],D=[],M=0,B=null;function H(n){r.onAbort&&r.onAbort(n),d(n+=""),h=!0,n="abort("+n+"). Build with -s ASSERTIONS=1 for more info.";var e=new WebAssembly.RuntimeError(n);throw t(e),e}r.preloadedImages={},r.preloadedAudios={};var q,V="data:application/octet-stream;base64,";function z(n){return n.startsWith(V)}if(r.locateFile)z(N="jxl_dec.wasm")||(q=N,N=r.locateFile?r.locateFile(q,f):f+q);else var N=new URL("/c/jxl_dec-e90a5afa.wasm",n.uri).toString();function L(n){try{if(n==N&&s)return new Uint8Array(s);if(i)return i(n);throw"both async and sync fetching of the wasm failed"}catch(n){H(n)}}function G(n){for(;n.length>0;){var e=n.shift();if("function"!=typeof e){var t=e.func;"number"==typeof t?void 0===e.arg?C.get(t)():C.get(t)(e.arg):t(void 0===e.arg?null:e.arg)}else e(r)}}function Z(n){switch(n){case 1:return 0;case 2:return 1;case 4:return 2;case 8:return 3;default:throw new TypeError("Unknown type size: "+n)}}var Y=void 0;function J(n){for(var r="",e=n;w[e];)r+=Y[w[e++]];return r}var X={},$={},K={},Q=48,nn=57;function rn(n){if(void 0===n)return"_unknown";var r=(n=n.replace(/[^a-zA-Z0-9_]/g,"$")).charCodeAt(0);return r>=Q&&r<=nn?"_"+n:n}function en(n,r){return n=rn(n),new Function("body","return function "+n+'() {\n    "use strict";    return body.apply(this, arguments);\n};\n')(r)}function tn(n,r){var e=en(r,(function(n){this.name=r,this.message=n;var e=new Error(n).stack;void 0!==e&&(this.stack=this.toString()+"\n"+e.replace(/^Error(:[^\n]*)?\n/,""))}));return e.prototype=Object.create(n.prototype),e.prototype.constructor=e,e.prototype.toString=function(){return void 0===this.message?this.name:this.name+": "+this.message},e}var on=void 0;function an(n){throw new on(n)}var un=void 0;function cn(n){throw new un(n)}function fn(n,r,e){if(e=e||{},!("argPackAdvance"in r))throw new TypeError("registerType registeredInstance requires argPackAdvance");var t=r.name;if(n||an('type "'+t+'" must have a positive integer typeid pointer'),$.hasOwnProperty(n)){if(e.ignoreDuplicateRegistrations)return;an("Cannot register type '"+t+"' twice")}if($[n]=r,delete K[n],X.hasOwnProperty(n)){var o=X[n];delete X[n],o.forEach((function(n){n()}))}}var sn=[],ln=[{},{value:void 0},{value:null},{value:!0},{value:!1}];function pn(n){n>4&&0==--ln[n].refcount&&(ln[n]=void 0,sn.push(n))}function dn(){for(var n=0,r=5;r<ln.length;++r)void 0!==ln[r]&&++n;return n}function hn(){for(var n=5;n<ln.length;++n)if(void 0!==ln[n])return ln[n];return null}function vn(n){switch(n){case void 0:return 1;case null:return 2;case!0:return 3;case!1:return 4;default:var r=sn.length?sn.pop():ln.length;return ln[r]={refcount:1,value:n},r}}function yn(n){return this.fromWireType(A[n>>2])}function gn(n){if(null===n)return"null";var r=typeof n;return"object"===r||"array"===r||"function"===r?n.toString():""+n}function mn(n,r){switch(r){case 2:return function(n){return this.fromWireType(E[n>>2])};case 3:return function(n){return this.fromWireType(P[n>>3])};default:throw new TypeError("Unknown float type: "+n)}}function wn(n){for(;n.length;){var r=n.pop();n.pop()(r)}}function bn(n,r,e,t,o){var a=r.length;a<2&&an("argTypes array size mismatch! Must at least get return value and 'this' types!");for(var i=null!==r[1]&&null!==e,u=!1,c=1;c<r.length;++c)if(null!==r[c]&&void 0===r[c].destructorFunction){u=!0;break}var f="void"!==r[0].name,s="",l="";for(c=0;c<a-2;++c)s+=(0!==c?", ":"")+"arg"+c,l+=(0!==c?", ":"")+"arg"+c+"Wired";var p="return function "+rn(n)+"("+s+") {\nif (arguments.length !== "+(a-2)+") {\nthrowBindingError('function "+n+" called with ' + arguments.length + ' arguments, expected "+(a-2)+" args!');\n}\n";u&&(p+="var destructors = [];\n");var d=u?"destructors":"null",h=["throwBindingError","invoker","fn","runDestructors","retType","classParam"],v=[an,t,o,wn,r[0],r[1]];for(i&&(p+="var thisWired = classParam.toWireType("+d+", this);\n"),c=0;c<a-2;++c)p+="var arg"+c+"Wired = argType"+c+".toWireType("+d+", arg"+c+"); // "+r[c+2].name+"\n",h.push("argType"+c),v.push(r[c+2]);if(i&&(l="thisWired"+(l.length>0?", ":"")+l),p+=(f?"var rv = ":"")+"invoker(fn"+(l.length>0?", ":"")+l+");\n",u)p+="runDestructors(destructors);\n";else for(c=i?1:2;c<r.length;++c){var y=1===c?"thisWired":"arg"+(c-2)+"Wired";null!==r[c].destructorFunction&&(p+=y+"_dtor("+y+"); // "+r[c].name+"\n",h.push(y+"_dtor"),v.push(r[c].destructorFunction))}return f&&(p+="var ret = retType.fromWireType(rv);\nreturn ret;\n"),p+="}\n",h.push(p),function(n,r){if(!(n instanceof Function))throw new TypeError("new_ called with constructor type "+typeof n+" which is not a function");var e=en(n.name||"unknownFunctionName",(function(){}));e.prototype=n.prototype;var t=new e,o=n.apply(t,r);return o instanceof Object?o:t}(Function,h).apply(null,v)}function Tn(n,e,t){r.hasOwnProperty(n)?((void 0===t||void 0!==r[n].overloadTable&&void 0!==r[n].overloadTable[t])&&an("Cannot register public name '"+n+"' twice"),function(n,r,e){if(void 0===n[r].overloadTable){var t=n[r];n[r]=function(){return n[r].overloadTable.hasOwnProperty(arguments.length)||an("Function '"+e+"' called with an invalid number of arguments ("+arguments.length+") - expects one of ("+n[r].overloadTable+")!"),n[r].overloadTable[arguments.length].apply(this,arguments)},n[r].overloadTable=[],n[r].overloadTable[t.argCount]=t}}(r,n,n),r.hasOwnProperty(t)&&an("Cannot register multiple overloads of a function with the same number of arguments ("+t+")!"),r[n].overloadTable[t]=e):(r[n]=e,void 0!==t&&(r[n].numArguments=t))}function _n(n,e,t){return n.includes("j")?function(n,e,t){var o=r["dynCall_"+n];return t&&t.length?o.apply(null,[e].concat(t)):o.call(null,e)}(n,e,t):C.get(e).apply(null,t)}function An(n,r){var e,t,o,a=(n=J(n)).includes("j")?(e=n,t=r,o=[],function(){o.length=arguments.length;for(var n=0;n<arguments.length;n++)o[n]=arguments[n];return _n(e,t,o)}):C.get(r);return"function"!=typeof a&&an("unknown function pointer with signature "+n+": "+r),a}var En=void 0;function Pn(n){var r=Dn(n),e=J(r);return jn(r),e}function Cn(n,r,e){switch(r){case 0:return e?function(n){return m[n]}:function(n){return w[n]};case 1:return e?function(n){return b[n>>1]}:function(n){return T[n>>1]};case 2:return e?function(n){return _[n>>2]}:function(n){return A[n>>2]};default:throw new TypeError("Unknown integer type: "+n)}}var Wn={};function Fn(){return"object"==typeof globalThis?globalThis:Function("return this")()}function Rn(n,r){var e=$[n];return void 0===e&&an(r+" has unknown type "+Pn(n)),e}var kn={};function Un(n){try{return l.grow(n-g.byteLength+65535>>>16),O(l.buffer),1}catch(n){}}var Sn={mappings:{},buffers:[null,[],[]],printChar:function(n,r){var e=Sn.buffers[n];0===r||10===r?((1===n?p:d)(function(n,r,e){for(var t=r+e,o=r;n[o]&&!(o>=t);)++o;return v.decode(n.subarray?n.subarray(r,o):new Uint8Array(n.slice(r,o)))}(e,0)),e.length=0):e.push(r)},varargs:void 0,get:function(){return Sn.varargs+=4,_[Sn.varargs-4>>2]},getStr:function(n){return y(n)},get64:function(n,r){return n}};!function(){for(var n=new Array(256),r=0;r<256;++r)n[r]=String.fromCharCode(r);Y=n}(),on=r.BindingError=tn(Error,"BindingError"),un=r.InternalError=tn(Error,"InternalError"),r.count_emval_handles=dn,r.get_first_emval=hn,En=r.UnboundTypeError=tn(Error,"UnboundTypeError");var In={b:function(n,r,e,t){H("Assertion failed: "+y(n)+", at: "+[r?y(r):"unknown filename",e,t?y(t):"unknown function"])},m:function(n,r){},r:function(n,r,e,t,o){},n:function(n,r,e,t,o){var a=Z(e);fn(n,{name:r=J(r),fromWireType:function(n){return!!n},toWireType:function(n,r){return r?t:o},argPackAdvance:8,readValueFromPointer:function(n){var t;if(1===e)t=m;else if(2===e)t=b;else{if(4!==e)throw new TypeError("Unknown boolean type size: "+r);t=_}return this.fromWireType(t[n>>a])},destructorFunction:null})},w:function(n,r){fn(n,{name:r=J(r),fromWireType:function(n){var r=ln[n].value;return pn(n),r},toWireType:function(n,r){return vn(r)},argPackAdvance:8,readValueFromPointer:yn,destructorFunction:null})},j:function(n,r,e){var t=Z(e);fn(n,{name:r=J(r),fromWireType:function(n){return n},toWireType:function(n,r){if("number"!=typeof r&&"boolean"!=typeof r)throw new TypeError('Cannot convert "'+gn(r)+'" to '+this.name);return r},argPackAdvance:8,readValueFromPointer:mn(r,t),destructorFunction:null})},p:function(n,e,t,o,a,i){var u=function(n,r){for(var e=[],t=0;t<n;t++)e.push(_[(r>>2)+t]);return e}(e,t);n=J(n),a=An(o,a),Tn(n,(function(){!function(n,r){var e=[],t={};throw r.forEach((function n(r){t[r]||$[r]||(K[r]?K[r].forEach(n):(e.push(r),t[r]=!0))})),new En(n+": "+e.map(Pn).join([", "]))}("Cannot call "+n+" due to unbound types",u)}),e-1),function(n,r,e){function t(r){var t=e(r);t.length!==n.length&&cn("Mismatched type converter count");for(var o=0;o<n.length;++o)fn(n[o],t[o])}n.forEach((function(n){K[n]=r}));var o=new Array(r.length),a=[],i=0;r.forEach((function(n,r){$.hasOwnProperty(n)?o[r]=$[n]:(a.push(n),X.hasOwnProperty(n)||(X[n]=[]),X[n].push((function(){o[r]=$[n],++i===a.length&&t(o)})))})),0===a.length&&t(o)}([],u,(function(t){var o=[t[0],null].concat(t.slice(1));return function(n,e,t){r.hasOwnProperty(n)||cn("Replacing nonexistant public symbol"),void 0!==r[n].overloadTable&&void 0!==t?r[n].overloadTable[t]=e:(r[n]=e,r[n].argCount=t)}(n,bn(n,o,null,a,i),e-1),[]}))},d:function(n,r,e,t,o){r=J(r),-1===o&&(o=4294967295);var a=Z(e),i=function(n){return n};if(0===t){var u=32-8*e;i=function(n){return n<<u>>>u}}var c=r.includes("unsigned");fn(n,{name:r,fromWireType:i,toWireType:function(n,e){if("number"!=typeof e&&"boolean"!=typeof e)throw new TypeError('Cannot convert "'+gn(e)+'" to '+this.name);if(e<t||e>o)throw new TypeError('Passing a number "'+gn(e)+'" from JS side to C/C++ side to an argument of type "'+r+'", which is outside the valid range ['+t+", "+o+"]!");return c?e>>>0:0|e},argPackAdvance:8,readValueFromPointer:Cn(r,a,0!==t),destructorFunction:null})},c:function(n,r,e){var t=[Int8Array,Uint8Array,Int16Array,Uint16Array,Int32Array,Uint32Array,Float32Array,Float64Array][r];function o(n){var r=A,e=r[n>>=2],o=r[n+1];return new t(g,o,e)}fn(n,{name:e=J(e),fromWireType:o,argPackAdvance:8,readValueFromPointer:o},{ignoreDuplicateRegistrations:!0})},k:function(n,r){var e="std::string"===(r=J(r));fn(n,{name:r,fromWireType:function(n){var r,t=A[n>>2];if(e)for(var o=n+4,a=0;a<=t;++a){var i=n+4+a;if(a==t||0==w[i]){var u=y(o,i-o);void 0===r?r=u:(r+=String.fromCharCode(0),r+=u),o=i+1}}else{var c=new Array(t);for(a=0;a<t;++a)c[a]=String.fromCharCode(w[n+4+a]);r=c.join("")}return jn(n),r},toWireType:function(n,r){var t;r instanceof ArrayBuffer&&(r=new Uint8Array(r));var o="string"==typeof r;o||r instanceof Uint8Array||r instanceof Uint8ClampedArray||r instanceof Int8Array||an("Cannot pass non-string to std::string"),t=e&&o?function(){return function(n){for(var r=0,e=0;e<n.length;++e){var t=n.charCodeAt(e);t>=55296&&t<=57343&&(t=65536+((1023&t)<<10)|1023&n.charCodeAt(++e)),t<=127?++r:r+=t<=2047?2:t<=65535?3:4}return r}(r)}:function(){return r.length};var a=t(),i=xn(4+a+1);if(A[i>>2]=a,e&&o)!function(n,r,e,t){if(!(t>0))return 0;for(var o=e+t-1,a=0;a<n.length;++a){var i=n.charCodeAt(a);if(i>=55296&&i<=57343&&(i=65536+((1023&i)<<10)|1023&n.charCodeAt(++a)),i<=127){if(e>=o)break;r[e++]=i}else if(i<=2047){if(e+1>=o)break;r[e++]=192|i>>6,r[e++]=128|63&i}else if(i<=65535){if(e+2>=o)break;r[e++]=224|i>>12,r[e++]=128|i>>6&63,r[e++]=128|63&i}else{if(e+3>=o)break;r[e++]=240|i>>18,r[e++]=128|i>>12&63,r[e++]=128|i>>6&63,r[e++]=128|63&i}}r[e]=0}(r,w,i+4,a+1);else if(o)for(var u=0;u<a;++u){var c=r.charCodeAt(u);c>255&&(jn(i),an("String has UTF-16 code units that do not fit in 8 bits")),w[i+4+u]=c}else for(u=0;u<a;++u)w[i+4+u]=r[u];return null!==n&&n.push(jn,i),i},argPackAdvance:8,readValueFromPointer:yn,destructorFunction:function(n){jn(n)}})},g:function(n,r,e){var t,o,a,i,u;e=J(e),2===r?(t=F,o=R,i=k,a=function(){return T},u=1):4===r&&(t=U,o=S,i=I,a=function(){return A},u=2),fn(n,{name:e,fromWireType:function(n){for(var e,o=A[n>>2],i=a(),c=n+4,f=0;f<=o;++f){var s=n+4+f*r;if(f==o||0==i[s>>u]){var l=t(c,s-c);void 0===e?e=l:(e+=String.fromCharCode(0),e+=l),c=s+r}}return jn(n),e},toWireType:function(n,t){"string"!=typeof t&&an("Cannot pass non-string to C++ string type "+e);var a=i(t),c=xn(4+a+r);return A[c>>2]=a>>u,o(t,c+4,a+r),null!==n&&n.push(jn,c),c},argPackAdvance:8,readValueFromPointer:yn,destructorFunction:function(n){jn(n)}})},o:function(n,r){fn(n,{isVoid:!0,name:r=J(r),argPackAdvance:0,fromWireType:function(){},toWireType:function(n,r){}})},e:pn,l:function(n){return 0===n?vn(Fn()):(n=void 0===(e=Wn[r=n])?J(r):e,vn(Fn()[n]));var r,e},h:function(n){n>4&&(ln[n].refcount+=1)},i:function(n,e,t,o){n=function(n){return n||an("Cannot use deleted val. handle = "+n),ln[n].value}(n);var a=kn[e];return a||(a=function(n){for(var e="",t=0;t<n;++t)e+=(0!==t?", ":"")+"arg"+t;var o="return function emval_allocator_"+n+"(constructor, argTypes, args) {\n";for(t=0;t<n;++t)o+="var argType"+t+" = requireRegisteredType(Module['HEAP32'][(argTypes >>> 2) + "+t+'], "parameter '+t+'");\nvar arg'+t+" = argType"+t+".readValueFromPointer(args);\nargs += argType"+t+"['argPackAdvance'];\n";return o+="var obj = new constructor("+e+");\nreturn __emval_register(obj);\n}\n",new Function("requireRegisteredType","Module","__emval_register",o)(Rn,r,vn)}(e),kn[e]=a),a(n,t,o)},a:function(){H()},t:function(n,r,e){w.copyWithin(n,r,r+e)},f:function(n){var r,e,t=w.length,o=2147483648;if((n>>>=0)>o)return!1;for(var a=1;a<=4;a*=2){var i=t*(1+.2/a);if(i=Math.min(i,n+100663296),Un(Math.min(o,((r=Math.max(n,i))%(e=65536)>0&&(r+=e-r%e),r))))return!0}return!1},u:function(n){return 0},q:function(n,r,e,t,o){},v:function(n,r,e,t){for(var o=0,a=0;a<e;a++){for(var i=_[r+8*a>>2],u=_[r+(8*a+4)>>2],c=0;c<u;c++)Sn.printChar(n,w[i+c]);o+=u}return _[t>>2]=o,0},s:function(n){}};!function(){var n={a:In};function e(n,e){var t,o=n.exports;r.asm=o,O((l=r.asm.x).buffer),C=r.asm.D,t=r.asm.y,j.unshift(t),function(){if(M--,r.monitorRunDependencies&&r.monitorRunDependencies(M),0==M&&B){var n=B;B=null,n()}}()}function o(n){e(n.instance)}function a(r){return(!s&&u&&"function"==typeof fetch?fetch(N,{credentials:"same-origin"}).then((function(n){if(!n.ok)throw"failed to load wasm binary file at '"+N+"'";return n.arrayBuffer()})).catch((function(){return L(N)})):Promise.resolve().then((function(){return L(N)}))).then((function(r){return WebAssembly.instantiate(r,n)})).then(r,(function(n){d("failed to asynchronously prepare wasm: "+n),H(n)}))}if(M++,r.monitorRunDependencies&&r.monitorRunDependencies(M),r.instantiateWasm)try{return r.instantiateWasm(n,e)}catch(n){return d("Module.instantiateWasm callback failed with error: "+n),!1}(s||"function"!=typeof WebAssembly.instantiateStreaming||z(N)||"function"!=typeof fetch?a(o):fetch(N,{credentials:"same-origin"}).then((function(r){return WebAssembly.instantiateStreaming(r,n).then(o,(function(n){return d("wasm streaming compile failed: "+n),d("falling back to ArrayBuffer instantiation"),a(o)}))}))).catch(t)}(),r.___wasm_call_ctors=function(){return(r.___wasm_call_ctors=r.asm.y).apply(null,arguments)};var On,xn=r._malloc=function(){return(xn=r._malloc=r.asm.z).apply(null,arguments)},jn=r._free=function(){return(jn=r._free=r.asm.A).apply(null,arguments)},Dn=r.___getTypeName=function(){return(Dn=r.___getTypeName=r.asm.B).apply(null,arguments)};function Mn(n){function t(){On||(On=!0,r.calledRun=!0,h||(G(j),e(r),r.onRuntimeInitialized&&r.onRuntimeInitialized(),function(){if(r.postRun)for("function"==typeof r.postRun&&(r.postRun=[r.postRun]);r.postRun.length;)n=r.postRun.shift(),D.unshift(n);var n;G(D)}()))}M>0||(function(){if(r.preRun)for("function"==typeof r.preRun&&(r.preRun=[r.preRun]);r.preRun.length;)n=r.preRun.shift(),x.unshift(n);var n;G(x)}(),M>0||(r.setStatus?(r.setStatus("Running..."),setTimeout((function(){setTimeout((function(){r.setStatus("")}),1),t()}),1)):t()))}if(r.___embind_register_native_and_builtin_types=function(){return(r.___embind_register_native_and_builtin_types=r.asm.C).apply(null,arguments)},r.dynCall_iiji=function(){return(r.dynCall_iiji=r.asm.E).apply(null,arguments)},r.dynCall_jiji=function(){return(r.dynCall_jiji=r.asm.F).apply(null,arguments)},B=function n(){On||Mn(),On||(B=n)},r.run=Mn,r.preInit)for("function"==typeof r.preInit&&(r.preInit=[r.preInit]);r.preInit.length>0;)r.preInit.pop()();return Mn(),r.ready});let s;var l=function(){var r=n.uri;return function(e){var t,o;(e=void 0!==(e=e||{})?e:{}).ready=new Promise((function(n,r){t=n,o=r}));var a,i={};for(a in e)e.hasOwnProperty(a)&&(i[a]=e[a]);var u,c=!0,f="";f=self.location.href,r&&(f=r),f=0!==f.indexOf("blob:")?f.substr(0,f.lastIndexOf("/")+1):"",u=function(n){var r=new XMLHttpRequest;return r.open("GET",n,!1),r.responseType="arraybuffer",r.send(null),new Uint8Array(r.response)},e.print||console.log.bind(console);var s,l,p=e.printErr||console.warn.bind(console);for(a in i)i.hasOwnProperty(a)&&(e[a]=i[a]);i=null,e.arguments&&e.arguments,e.thisProgram&&e.thisProgram,e.quit&&e.quit,e.wasmBinary&&(s=e.wasmBinary),e.noExitRuntime,"object"!=typeof WebAssembly&&B("no native wasm support detected");var d=!1,h=new TextDecoder("utf8");function v(n,r){if(!n)return"";for(var e=n+r,t=n;!(t>=e)&&m[t];)++t;return h.decode(m.subarray(n,t))}var y,g,m,w,b,T,_,A,E,P,C=new TextDecoder("utf-16le");function W(n,r){for(var e=n,t=e>>1,o=t+r/2;!(t>=o)&&b[t];)++t;return e=t<<1,C.decode(m.subarray(n,e))}function F(n,r,e){if(void 0===e&&(e=2147483647),e<2)return 0;for(var t=r,o=(e-=2)<2*n.length?e/2:n.length,a=0;a<o;++a){var i=n.charCodeAt(a);w[r>>1]=i,r+=2}return w[r>>1]=0,r-t}function R(n){return 2*n.length}function k(n,r){for(var e=0,t="";!(e>=r/4);){var o=T[n+4*e>>2];if(0==o)break;if(++e,o>=65536){var a=o-65536;t+=String.fromCharCode(55296|a>>10,56320|1023&a)}else t+=String.fromCharCode(o)}return t}function U(n,r,e){if(void 0===e&&(e=2147483647),e<4)return 0;for(var t=r,o=t+e-4,a=0;a<n.length;++a){var i=n.charCodeAt(a);if(i>=55296&&i<=57343)i=65536+((1023&i)<<10)|1023&n.charCodeAt(++a);if(T[r>>2]=i,(r+=4)+4>o)break}return T[r>>2]=0,r-t}function S(n){for(var r=0,e=0;e<n.length;++e){var t=n.charCodeAt(e);t>=55296&&t<=57343&&++e,r+=4}return r}function I(n){y=n,e.HEAP8=g=new Int8Array(n),e.HEAP16=w=new Int16Array(n),e.HEAP32=T=new Int32Array(n),e.HEAPU8=m=new Uint8Array(n),e.HEAPU16=b=new Uint16Array(n),e.HEAPU32=_=new Uint32Array(n),e.HEAPF32=A=new Float32Array(n),e.HEAPF64=E=new Float64Array(n)}e.INITIAL_MEMORY;var O=[],x=[],j=[];var D=0,M=null;function B(n){e.onAbort&&e.onAbort(n),p(n+=""),d=!0,n="abort("+n+"). Build with -s ASSERTIONS=1 for more info.";var r=new WebAssembly.RuntimeError(n);throw o(r),r}e.preloadedImages={},e.preloadedAudios={};var H,q="data:application/octet-stream;base64,";function V(n){return n.startsWith(q)}if(e.locateFile)V(z="qoi_dec.wasm")||(H=z,z=e.locateFile?e.locateFile(H,f):f+H);else var z=new URL("/c/qoi_dec-3728a8ee.wasm",n.uri).toString();function N(n){try{if(n==z&&s)return new Uint8Array(s);if(u)return u(n);throw"both async and sync fetching of the wasm failed"}catch(n){B(n)}}function L(n){for(;n.length>0;){var r=n.shift();if("function"!=typeof r){var t=r.func;"number"==typeof t?void 0===r.arg?P.get(t)():P.get(t)(r.arg):t(void 0===r.arg?null:r.arg)}else r(e)}}function G(n){switch(n){case 1:return 0;case 2:return 1;case 4:return 2;case 8:return 3;default:throw new TypeError("Unknown type size: "+n)}}var Z=void 0;function Y(n){for(var r="",e=n;m[e];)r+=Z[m[e++]];return r}var J={},X={},$={},K=48,Q=57;function nn(n){if(void 0===n)return"_unknown";var r=(n=n.replace(/[^a-zA-Z0-9_]/g,"$")).charCodeAt(0);return r>=K&&r<=Q?"_"+n:n}function rn(n,r){return n=nn(n),new Function("body","return function "+n+'() {\n    "use strict";    return body.apply(this, arguments);\n};\n')(r)}function en(n,r){var e=rn(r,(function(n){this.name=r,this.message=n;var e=new Error(n).stack;void 0!==e&&(this.stack=this.toString()+"\n"+e.replace(/^Error(:[^\n]*)?\n/,""))}));return e.prototype=Object.create(n.prototype),e.prototype.constructor=e,e.prototype.toString=function(){return void 0===this.message?this.name:this.name+": "+this.message},e}var tn=void 0;function on(n){throw new tn(n)}var an=void 0;function un(n){throw new an(n)}function cn(n,r,e){if(e=e||{},!("argPackAdvance"in r))throw new TypeError("registerType registeredInstance requires argPackAdvance");var t=r.name;if(n||on('type "'+t+'" must have a positive integer typeid pointer'),X.hasOwnProperty(n)){if(e.ignoreDuplicateRegistrations)return;on("Cannot register type '"+t+"' twice")}if(X[n]=r,delete $[n],J.hasOwnProperty(n)){var o=J[n];delete J[n],o.forEach((function(n){n()}))}}var fn=[],sn=[{},{value:void 0},{value:null},{value:!0},{value:!1}];function ln(n){n>4&&0==--sn[n].refcount&&(sn[n]=void 0,fn.push(n))}function pn(){for(var n=0,r=5;r<sn.length;++r)void 0!==sn[r]&&++n;return n}function dn(){for(var n=5;n<sn.length;++n)if(void 0!==sn[n])return sn[n];return null}function hn(n){switch(n){case void 0:return 1;case null:return 2;case!0:return 3;case!1:return 4;default:var r=fn.length?fn.pop():sn.length;return sn[r]={refcount:1,value:n},r}}function vn(n){return this.fromWireType(_[n>>2])}function yn(n){if(null===n)return"null";var r=typeof n;return"object"===r||"array"===r||"function"===r?n.toString():""+n}function gn(n,r){switch(r){case 2:return function(n){return this.fromWireType(A[n>>2])};case 3:return function(n){return this.fromWireType(E[n>>3])};default:throw new TypeError("Unknown float type: "+n)}}function mn(n){for(;n.length;){var r=n.pop();n.pop()(r)}}function wn(n,r,e,t,o){var a=r.length;a<2&&on("argTypes array size mismatch! Must at least get return value and 'this' types!");for(var i=null!==r[1]&&null!==e,u=!1,c=1;c<r.length;++c)if(null!==r[c]&&void 0===r[c].destructorFunction){u=!0;break}var f="void"!==r[0].name,s="",l="";for(c=0;c<a-2;++c)s+=(0!==c?", ":"")+"arg"+c,l+=(0!==c?", ":"")+"arg"+c+"Wired";var p="return function "+nn(n)+"("+s+") {\nif (arguments.length !== "+(a-2)+") {\nthrowBindingError('function "+n+" called with ' + arguments.length + ' arguments, expected "+(a-2)+" args!');\n}\n";u&&(p+="var destructors = [];\n");var d=u?"destructors":"null",h=["throwBindingError","invoker","fn","runDestructors","retType","classParam"],v=[on,t,o,mn,r[0],r[1]];i&&(p+="var thisWired = classParam.toWireType("+d+", this);\n");for(c=0;c<a-2;++c)p+="var arg"+c+"Wired = argType"+c+".toWireType("+d+", arg"+c+"); // "+r[c+2].name+"\n",h.push("argType"+c),v.push(r[c+2]);if(i&&(l="thisWired"+(l.length>0?", ":"")+l),p+=(f?"var rv = ":"")+"invoker(fn"+(l.length>0?", ":"")+l+");\n",u)p+="runDestructors(destructors);\n";else for(c=i?1:2;c<r.length;++c){var y=1===c?"thisWired":"arg"+(c-2)+"Wired";null!==r[c].destructorFunction&&(p+=y+"_dtor("+y+"); // "+r[c].name+"\n",h.push(y+"_dtor"),v.push(r[c].destructorFunction))}return f&&(p+="var ret = retType.fromWireType(rv);\nreturn ret;\n"),p+="}\n",h.push(p),function(n,r){if(!(n instanceof Function))throw new TypeError("new_ called with constructor type "+typeof n+" which is not a function");var e=rn(n.name||"unknownFunctionName",(function(){}));e.prototype=n.prototype;var t=new e,o=n.apply(t,r);return o instanceof Object?o:t}(Function,h).apply(null,v)}function bn(n,r,t){e.hasOwnProperty(n)?((void 0===t||void 0!==e[n].overloadTable&&void 0!==e[n].overloadTable[t])&&on("Cannot register public name '"+n+"' twice"),function(n,r,e){if(void 0===n[r].overloadTable){var t=n[r];n[r]=function(){return n[r].overloadTable.hasOwnProperty(arguments.length)||on("Function '"+e+"' called with an invalid number of arguments ("+arguments.length+") - expects one of ("+n[r].overloadTable+")!"),n[r].overloadTable[arguments.length].apply(this,arguments)},n[r].overloadTable=[],n[r].overloadTable[t.argCount]=t}}(e,n,n),e.hasOwnProperty(t)&&on("Cannot register multiple overloads of a function with the same number of arguments ("+t+")!"),e[n].overloadTable[t]=r):(e[n]=r,void 0!==t&&(e[n].numArguments=t))}function Tn(n,r,t){return n.includes("j")?function(n,r,t){var o=e["dynCall_"+n];return t&&t.length?o.apply(null,[r].concat(t)):o.call(null,r)}(n,r,t):P.get(r).apply(null,t)}function _n(n,r){var e,t,o,a=(n=Y(n)).includes("j")?(e=n,t=r,o=[],function(){o.length=arguments.length;for(var n=0;n<arguments.length;n++)o[n]=arguments[n];return Tn(e,t,o)}):P.get(r);return"function"!=typeof a&&on("unknown function pointer with signature "+n+": "+r),a}var An=void 0;function En(n){var r=xn(n),e=Y(r);return On(r),e}function Pn(n,r,e){switch(r){case 0:return e?function(n){return g[n]}:function(n){return m[n]};case 1:return e?function(n){return w[n>>1]}:function(n){return b[n>>1]};case 2:return e?function(n){return T[n>>2]}:function(n){return _[n>>2]};default:throw new TypeError("Unknown integer type: "+n)}}var Cn={};function Wn(){return"object"==typeof globalThis?globalThis:Function("return this")()}function Fn(n,r){var e=X[n];return void 0===e&&on(r+" has unknown type "+En(n)),e}var Rn={};function kn(n){try{return l.grow(n-y.byteLength+65535>>>16),I(l.buffer),1}catch(n){}}!function(){for(var n=new Array(256),r=0;r<256;++r)n[r]=String.fromCharCode(r);Z=n}(),tn=e.BindingError=en(Error,"BindingError"),an=e.InternalError=en(Error,"InternalError"),e.count_emval_handles=pn,e.get_first_emval=dn,An=e.UnboundTypeError=en(Error,"UnboundTypeError");var Un={e:function(n,r){},p:function(n,r,e,t,o){},m:function(n,r,e,t,o){var a=G(e);cn(n,{name:r=Y(r),fromWireType:function(n){return!!n},toWireType:function(n,r){return r?t:o},argPackAdvance:8,readValueFromPointer:function(n){var t;if(1===e)t=g;else if(2===e)t=w;else{if(4!==e)throw new TypeError("Unknown boolean type size: "+r);t=T}return this.fromWireType(t[n>>a])},destructorFunction:null})},r:function(n,r){cn(n,{name:r=Y(r),fromWireType:function(n){var r=sn[n].value;return ln(n),r},toWireType:function(n,r){return hn(r)},argPackAdvance:8,readValueFromPointer:vn,destructorFunction:null})},l:function(n,r,e){var t=G(e);cn(n,{name:r=Y(r),fromWireType:function(n){return n},toWireType:function(n,r){if("number"!=typeof r&&"boolean"!=typeof r)throw new TypeError('Cannot convert "'+yn(r)+'" to '+this.name);return r},argPackAdvance:8,readValueFromPointer:gn(r,t),destructorFunction:null})},o:function(n,r,t,o,a,i){var u=function(n,r){for(var e=[],t=0;t<n;t++)e.push(T[(r>>2)+t]);return e}(r,t);n=Y(n),a=_n(o,a),bn(n,(function(){!function(n,r){var e=[],t={};throw r.forEach((function n(r){t[r]||X[r]||($[r]?$[r].forEach(n):(e.push(r),t[r]=!0))})),new An(n+": "+e.map(En).join([", "]))}("Cannot call "+n+" due to unbound types",u)}),r-1),function(n,r,e){function t(r){var t=e(r);t.length!==n.length&&un("Mismatched type converter count");for(var o=0;o<n.length;++o)cn(n[o],t[o])}n.forEach((function(n){$[n]=r}));var o=new Array(r.length),a=[],i=0;r.forEach((function(n,r){X.hasOwnProperty(n)?o[r]=X[n]:(a.push(n),J.hasOwnProperty(n)||(J[n]=[]),J[n].push((function(){o[r]=X[n],++i===a.length&&t(o)})))})),0===a.length&&t(o)}([],u,(function(t){var o=[t[0],null].concat(t.slice(1));return function(n,r,t){e.hasOwnProperty(n)||un("Replacing nonexistant public symbol"),void 0!==e[n].overloadTable&&void 0!==t?e[n].overloadTable[t]=r:(e[n]=r,e[n].argCount=t)}(n,wn(n,o,null,a,i),r-1),[]}))},b:function(n,r,e,t,o){r=Y(r),-1===o&&(o=4294967295);var a=G(e),i=function(n){return n};if(0===t){var u=32-8*e;i=function(n){return n<<u>>>u}}var c=r.includes("unsigned");cn(n,{name:r,fromWireType:i,toWireType:function(n,e){if("number"!=typeof e&&"boolean"!=typeof e)throw new TypeError('Cannot convert "'+yn(e)+'" to '+this.name);if(e<t||e>o)throw new TypeError('Passing a number "'+yn(e)+'" from JS side to C/C++ side to an argument of type "'+r+'", which is outside the valid range ['+t+", "+o+"]!");return c?e>>>0:0|e},argPackAdvance:8,readValueFromPointer:Pn(r,a,0!==t),destructorFunction:null})},a:function(n,r,e){var t=[Int8Array,Uint8Array,Int16Array,Uint16Array,Int32Array,Uint32Array,Float32Array,Float64Array][r];function o(n){var r=_,e=r[n>>=2],o=r[n+1];return new t(y,o,e)}cn(n,{name:e=Y(e),fromWireType:o,argPackAdvance:8,readValueFromPointer:o},{ignoreDuplicateRegistrations:!0})},h:function(n,r){var e="std::string"===(r=Y(r));cn(n,{name:r,fromWireType:function(n){var r,t=_[n>>2];if(e)for(var o=n+4,a=0;a<=t;++a){var i=n+4+a;if(a==t||0==m[i]){var u=v(o,i-o);void 0===r?r=u:(r+=String.fromCharCode(0),r+=u),o=i+1}}else{var c=new Array(t);for(a=0;a<t;++a)c[a]=String.fromCharCode(m[n+4+a]);r=c.join("")}return On(n),r},toWireType:function(n,r){var t;r instanceof ArrayBuffer&&(r=new Uint8Array(r));var o="string"==typeof r;o||r instanceof Uint8Array||r instanceof Uint8ClampedArray||r instanceof Int8Array||on("Cannot pass non-string to std::string"),t=e&&o?function(){return function(n){for(var r=0,e=0;e<n.length;++e){var t=n.charCodeAt(e);t>=55296&&t<=57343&&(t=65536+((1023&t)<<10)|1023&n.charCodeAt(++e)),t<=127?++r:r+=t<=2047?2:t<=65535?3:4}return r}(r)}:function(){return r.length};var a=t(),i=In(4+a+1);if(_[i>>2]=a,e&&o)(function(n,r,e,t){if(!(t>0))return 0;for(var o=e,a=e+t-1,i=0;i<n.length;++i){var u=n.charCodeAt(i);if(u>=55296&&u<=57343&&(u=65536+((1023&u)<<10)|1023&n.charCodeAt(++i)),u<=127){if(e>=a)break;r[e++]=u}else if(u<=2047){if(e+1>=a)break;r[e++]=192|u>>6,r[e++]=128|63&u}else if(u<=65535){if(e+2>=a)break;r[e++]=224|u>>12,r[e++]=128|u>>6&63,r[e++]=128|63&u}else{if(e+3>=a)break;r[e++]=240|u>>18,r[e++]=128|u>>12&63,r[e++]=128|u>>6&63,r[e++]=128|63&u}}r[e]=0})(r,m,i+4,a+1);else if(o)for(var u=0;u<a;++u){var c=r.charCodeAt(u);c>255&&(On(i),on("String has UTF-16 code units that do not fit in 8 bits")),m[i+4+u]=c}else for(u=0;u<a;++u)m[i+4+u]=r[u];return null!==n&&n.push(On,i),i},argPackAdvance:8,readValueFromPointer:vn,destructorFunction:function(n){On(n)}})},f:function(n,r,e){var t,o,a,i,u;e=Y(e),2===r?(t=W,o=F,i=R,a=function(){return b},u=1):4===r&&(t=k,o=U,i=S,a=function(){return _},u=2),cn(n,{name:e,fromWireType:function(n){for(var e,o=_[n>>2],i=a(),c=n+4,f=0;f<=o;++f){var s=n+4+f*r;if(f==o||0==i[s>>u]){var l=t(c,s-c);void 0===e?e=l:(e+=String.fromCharCode(0),e+=l),c=s+r}}return On(n),e},toWireType:function(n,t){"string"!=typeof t&&on("Cannot pass non-string to C++ string type "+e);var a=i(t),c=In(4+a+r);return _[c>>2]=a>>u,o(t,c+4,a+r),null!==n&&n.push(On,c),c},argPackAdvance:8,readValueFromPointer:vn,destructorFunction:function(n){On(n)}})},n:function(n,r){cn(n,{isVoid:!0,name:r=Y(r),argPackAdvance:0,fromWireType:function(){},toWireType:function(n,r){}})},c:ln,d:function(n){return 0===n?hn(Wn()):(n=void 0===(e=Cn[r=n])?Y(r):e,hn(Wn()[n]));var r,e},i:function(n){n>4&&(sn[n].refcount+=1)},j:function(n,r,t,o){n=function(n){return n||on("Cannot use deleted val. handle = "+n),sn[n].value}(n);var a=Rn[r];return a||(a=function(n){for(var r="",t=0;t<n;++t)r+=(0!==t?", ":"")+"arg"+t;var o="return function emval_allocator_"+n+"(constructor, argTypes, args) {\n";for(t=0;t<n;++t)o+="var argType"+t+" = requireRegisteredType(Module['HEAP32'][(argTypes >>> 2) + "+t+'], "parameter '+t+'");\nvar arg'+t+" = argType"+t+".readValueFromPointer(args);\nargs += argType"+t+"['argPackAdvance'];\n";return o+="var obj = new constructor("+r+");\nreturn __emval_register(obj);\n}\n",new Function("requireRegisteredType","Module","__emval_register",o)(Fn,e,hn)}(r),Rn[r]=a),a(n,t,o)},k:function(){B()},q:function(n,r,e){m.copyWithin(n,r,r+e)},g:function(n){var r,e,t=m.length,o=2147483648;if((n>>>=0)>o)return!1;for(var a=1;a<=4;a*=2){var i=t*(1+.2/a);if(i=Math.min(i,n+100663296),kn(Math.min(o,((r=Math.max(n,i))%(e=65536)>0&&(r+=e-r%e),r))))return!0}return!1}};!function(){var n={a:Un};function r(n,r){var t,o=n.exports;e.asm=o,I((l=e.asm.s).buffer),P=e.asm.y,t=e.asm.t,x.unshift(t),function(){if(D--,e.monitorRunDependencies&&e.monitorRunDependencies(D),0==D&&M){var n=M;M=null,n()}}()}function t(n){r(n.instance)}function a(r){return(!s&&c&&"function"==typeof fetch?fetch(z,{credentials:"same-origin"}).then((function(n){if(!n.ok)throw"failed to load wasm binary file at '"+z+"'";return n.arrayBuffer()})).catch((function(){return N(z)})):Promise.resolve().then((function(){return N(z)}))).then((function(r){return WebAssembly.instantiate(r,n)})).then(r,(function(n){p("failed to asynchronously prepare wasm: "+n),B(n)}))}if(D++,e.monitorRunDependencies&&e.monitorRunDependencies(D),e.instantiateWasm)try{return e.instantiateWasm(n,r)}catch(n){return p("Module.instantiateWasm callback failed with error: "+n),!1}(s||"function"!=typeof WebAssembly.instantiateStreaming||V(z)||"function"!=typeof fetch?a(t):fetch(z,{credentials:"same-origin"}).then((function(r){return WebAssembly.instantiateStreaming(r,n).then(t,(function(n){return p("wasm streaming compile failed: "+n),p("falling back to ArrayBuffer instantiation"),a(t)}))}))).catch(o)}(),e.___wasm_call_ctors=function(){return(e.___wasm_call_ctors=e.asm.t).apply(null,arguments)};var Sn,In=e._malloc=function(){return(In=e._malloc=e.asm.u).apply(null,arguments)},On=e._free=function(){return(On=e._free=e.asm.v).apply(null,arguments)},xn=e.___getTypeName=function(){return(xn=e.___getTypeName=e.asm.w).apply(null,arguments)};function jn(n){function r(){Sn||(Sn=!0,e.calledRun=!0,d||(L(x),t(e),e.onRuntimeInitialized&&e.onRuntimeInitialized(),function(){if(e.postRun)for("function"==typeof e.postRun&&(e.postRun=[e.postRun]);e.postRun.length;)n=e.postRun.shift(),j.unshift(n);var n;L(j)}()))}D>0||(!function(){if(e.preRun)for("function"==typeof e.preRun&&(e.preRun=[e.preRun]);e.preRun.length;)n=e.preRun.shift(),O.unshift(n);var n;L(O)}(),D>0||(e.setStatus?(e.setStatus("Running..."),setTimeout((function(){setTimeout((function(){e.setStatus("")}),1),r()}),1)):r()))}if(e.___embind_register_native_and_builtin_types=function(){return(e.___embind_register_native_and_builtin_types=e.asm.x).apply(null,arguments)},M=function n(){Sn||jn(),Sn||(M=n)},e.run=jn,e.preInit)for("function"==typeof e.preInit&&(e.preInit=[e.preInit]);e.preInit.length>0;)e.preInit.pop()();return jn(),e.ready}}();let p,d;var h=function(){var r=n.uri;return function(e){var t,o;(e=void 0!==(e=e||{})?e:{}).ready=new Promise((function(n,r){t=n,o=r}));var a,i={};for(a in e)e.hasOwnProperty(a)&&(i[a]=e[a]);var u,c=!0,f="";f=self.location.href,r&&(f=r),f=0!==f.indexOf("blob:")?f.substr(0,f.lastIndexOf("/")+1):"",u=function(n){var r=new XMLHttpRequest;return r.open("GET",n,!1),r.responseType="arraybuffer",r.send(null),new Uint8Array(r.response)};var s,l,p=e.print||console.log.bind(console),d=e.printErr||console.warn.bind(console);for(a in i)i.hasOwnProperty(a)&&(e[a]=i[a]);i=null,e.arguments&&e.arguments,e.thisProgram&&e.thisProgram,e.quit&&e.quit,e.wasmBinary&&(s=e.wasmBinary),e.noExitRuntime,"object"!=typeof WebAssembly&&H("no native wasm support detected");var h=!1,v=new TextDecoder("utf8");function y(n,r){if(!n)return"";for(var e=n+r,t=n;!(t>=e)&&w[t];)++t;return v.decode(w.subarray(n,t))}var g,m,w,b,T,_,A,E,P,C,W=new TextDecoder("utf-16le");function F(n,r){for(var e=n,t=e>>1,o=t+r/2;!(t>=o)&&T[t];)++t;return e=t<<1,W.decode(w.subarray(n,e))}function R(n,r,e){if(void 0===e&&(e=2147483647),e<2)return 0;for(var t=r,o=(e-=2)<2*n.length?e/2:n.length,a=0;a<o;++a){var i=n.charCodeAt(a);b[r>>1]=i,r+=2}return b[r>>1]=0,r-t}function k(n){return 2*n.length}function U(n,r){for(var e=0,t="";!(e>=r/4);){var o=_[n+4*e>>2];if(0==o)break;if(++e,o>=65536){var a=o-65536;t+=String.fromCharCode(55296|a>>10,56320|1023&a)}else t+=String.fromCharCode(o)}return t}function S(n,r,e){if(void 0===e&&(e=2147483647),e<4)return 0;for(var t=r,o=t+e-4,a=0;a<n.length;++a){var i=n.charCodeAt(a);if(i>=55296&&i<=57343)i=65536+((1023&i)<<10)|1023&n.charCodeAt(++a);if(_[r>>2]=i,(r+=4)+4>o)break}return _[r>>2]=0,r-t}function I(n){for(var r=0,e=0;e<n.length;++e){var t=n.charCodeAt(e);t>=55296&&t<=57343&&++e,r+=4}return r}function O(n){g=n,e.HEAP8=m=new Int8Array(n),e.HEAP16=b=new Int16Array(n),e.HEAP32=_=new Int32Array(n),e.HEAPU8=w=new Uint8Array(n),e.HEAPU16=T=new Uint16Array(n),e.HEAPU32=A=new Uint32Array(n),e.HEAPF32=E=new Float32Array(n),e.HEAPF64=P=new Float64Array(n)}e.INITIAL_MEMORY;var x=[],j=[],D=[];var M=0,B=null;function H(n){e.onAbort&&e.onAbort(n),d(n+=""),h=!0,n="abort("+n+"). Build with -s ASSERTIONS=1 for more info.";var r=new WebAssembly.RuntimeError(n);throw o(r),r}e.preloadedImages={},e.preloadedAudios={};var q,V="data:application/octet-stream;base64,";function z(n){return n.startsWith(V)}if(e.locateFile)z(N="wp2_dec.wasm")||(q=N,N=e.locateFile?e.locateFile(q,f):f+q);else var N=new URL("/c/wp2_dec-9a40adf1.wasm",n.uri).toString();function L(n){try{if(n==N&&s)return new Uint8Array(s);if(u)return u(n);throw"both async and sync fetching of the wasm failed"}catch(n){H(n)}}function G(n){for(;n.length>0;){var r=n.shift();if("function"!=typeof r){var t=r.func;"number"==typeof t?void 0===r.arg?C.get(t)():C.get(t)(r.arg):t(void 0===r.arg?null:r.arg)}else r(e)}}var Z={DESTRUCTOR_OFFSET:0,REFCOUNT_OFFSET:4,TYPE_OFFSET:8,CAUGHT_OFFSET:12,RETHROWN_OFFSET:13,SIZE:16};function Y(n){this.excPtr=n,this.ptr=n-Z.SIZE,this.set_type=function(n){_[this.ptr+Z.TYPE_OFFSET>>2]=n},this.get_type=function(){return _[this.ptr+Z.TYPE_OFFSET>>2]},this.set_destructor=function(n){_[this.ptr+Z.DESTRUCTOR_OFFSET>>2]=n},this.get_destructor=function(){return _[this.ptr+Z.DESTRUCTOR_OFFSET>>2]},this.set_refcount=function(n){_[this.ptr+Z.REFCOUNT_OFFSET>>2]=n},this.set_caught=function(n){n=n?1:0,m[this.ptr+Z.CAUGHT_OFFSET|0]=n},this.get_caught=function(){return 0!=m[this.ptr+Z.CAUGHT_OFFSET|0]},this.set_rethrown=function(n){n=n?1:0,m[this.ptr+Z.RETHROWN_OFFSET|0]=n},this.get_rethrown=function(){return 0!=m[this.ptr+Z.RETHROWN_OFFSET|0]},this.init=function(n,r){this.set_type(n),this.set_destructor(r),this.set_refcount(0),this.set_caught(!1),this.set_rethrown(!1)},this.add_ref=function(){var n=_[this.ptr+Z.REFCOUNT_OFFSET>>2];_[this.ptr+Z.REFCOUNT_OFFSET>>2]=n+1},this.release_ref=function(){var n=_[this.ptr+Z.REFCOUNT_OFFSET>>2];return _[this.ptr+Z.REFCOUNT_OFFSET>>2]=n-1,1===n}}function J(n){switch(n){case 1:return 0;case 2:return 1;case 4:return 2;case 8:return 3;default:throw new TypeError("Unknown type size: "+n)}}var X=void 0;function $(n){for(var r="",e=n;w[e];)r+=X[w[e++]];return r}var K={},Q={},nn={},rn=48,en=57;function tn(n){if(void 0===n)return"_unknown";var r=(n=n.replace(/[^a-zA-Z0-9_]/g,"$")).charCodeAt(0);return r>=rn&&r<=en?"_"+n:n}function on(n,r){return n=tn(n),new Function("body","return function "+n+'() {\n    "use strict";    return body.apply(this, arguments);\n};\n')(r)}function an(n,r){var e=on(r,(function(n){this.name=r,this.message=n;var e=new Error(n).stack;void 0!==e&&(this.stack=this.toString()+"\n"+e.replace(/^Error(:[^\n]*)?\n/,""))}));return e.prototype=Object.create(n.prototype),e.prototype.constructor=e,e.prototype.toString=function(){return void 0===this.message?this.name:this.name+": "+this.message},e}var un=void 0;function cn(n){throw new un(n)}var fn=void 0;function sn(n){throw new fn(n)}function ln(n,r,e){if(e=e||{},!("argPackAdvance"in r))throw new TypeError("registerType registeredInstance requires argPackAdvance");var t=r.name;if(n||cn('type "'+t+'" must have a positive integer typeid pointer'),Q.hasOwnProperty(n)){if(e.ignoreDuplicateRegistrations)return;cn("Cannot register type '"+t+"' twice")}if(Q[n]=r,delete nn[n],K.hasOwnProperty(n)){var o=K[n];delete K[n],o.forEach((function(n){n()}))}}var pn=[],dn=[{},{value:void 0},{value:null},{value:!0},{value:!1}];function hn(n){n>4&&0==--dn[n].refcount&&(dn[n]=void 0,pn.push(n))}function vn(){for(var n=0,r=5;r<dn.length;++r)void 0!==dn[r]&&++n;return n}function yn(){for(var n=5;n<dn.length;++n)if(void 0!==dn[n])return dn[n];return null}function gn(n){switch(n){case void 0:return 1;case null:return 2;case!0:return 3;case!1:return 4;default:var r=pn.length?pn.pop():dn.length;return dn[r]={refcount:1,value:n},r}}function mn(n){return this.fromWireType(A[n>>2])}function wn(n){if(null===n)return"null";var r=typeof n;return"object"===r||"array"===r||"function"===r?n.toString():""+n}function bn(n,r){switch(r){case 2:return function(n){return this.fromWireType(E[n>>2])};case 3:return function(n){return this.fromWireType(P[n>>3])};default:throw new TypeError("Unknown float type: "+n)}}function Tn(n){for(;n.length;){var r=n.pop();n.pop()(r)}}function _n(n,r,e,t,o){var a=r.length;a<2&&cn("argTypes array size mismatch! Must at least get return value and 'this' types!");for(var i=null!==r[1]&&null!==e,u=!1,c=1;c<r.length;++c)if(null!==r[c]&&void 0===r[c].destructorFunction){u=!0;break}var f="void"!==r[0].name,s="",l="";for(c=0;c<a-2;++c)s+=(0!==c?", ":"")+"arg"+c,l+=(0!==c?", ":"")+"arg"+c+"Wired";var p="return function "+tn(n)+"("+s+") {\nif (arguments.length !== "+(a-2)+") {\nthrowBindingError('function "+n+" called with ' + arguments.length + ' arguments, expected "+(a-2)+" args!');\n}\n";u&&(p+="var destructors = [];\n");var d=u?"destructors":"null",h=["throwBindingError","invoker","fn","runDestructors","retType","classParam"],v=[cn,t,o,Tn,r[0],r[1]];i&&(p+="var thisWired = classParam.toWireType("+d+", this);\n");for(c=0;c<a-2;++c)p+="var arg"+c+"Wired = argType"+c+".toWireType("+d+", arg"+c+"); // "+r[c+2].name+"\n",h.push("argType"+c),v.push(r[c+2]);if(i&&(l="thisWired"+(l.length>0?", ":"")+l),p+=(f?"var rv = ":"")+"invoker(fn"+(l.length>0?", ":"")+l+");\n",u)p+="runDestructors(destructors);\n";else for(c=i?1:2;c<r.length;++c){var y=1===c?"thisWired":"arg"+(c-2)+"Wired";null!==r[c].destructorFunction&&(p+=y+"_dtor("+y+"); // "+r[c].name+"\n",h.push(y+"_dtor"),v.push(r[c].destructorFunction))}return f&&(p+="var ret = retType.fromWireType(rv);\nreturn ret;\n"),p+="}\n",h.push(p),function(n,r){if(!(n instanceof Function))throw new TypeError("new_ called with constructor type "+typeof n+" which is not a function");var e=on(n.name||"unknownFunctionName",(function(){}));e.prototype=n.prototype;var t=new e,o=n.apply(t,r);return o instanceof Object?o:t}(Function,h).apply(null,v)}function An(n,r,t){e.hasOwnProperty(n)?((void 0===t||void 0!==e[n].overloadTable&&void 0!==e[n].overloadTable[t])&&cn("Cannot register public name '"+n+"' twice"),function(n,r,e){if(void 0===n[r].overloadTable){var t=n[r];n[r]=function(){return n[r].overloadTable.hasOwnProperty(arguments.length)||cn("Function '"+e+"' called with an invalid number of arguments ("+arguments.length+") - expects one of ("+n[r].overloadTable+")!"),n[r].overloadTable[arguments.length].apply(this,arguments)},n[r].overloadTable=[],n[r].overloadTable[t.argCount]=t}}(e,n,n),e.hasOwnProperty(t)&&cn("Cannot register multiple overloads of a function with the same number of arguments ("+t+")!"),e[n].overloadTable[t]=r):(e[n]=r,void 0!==t&&(e[n].numArguments=t))}function En(n,r,t){return n.includes("j")?function(n,r,t){var o=e["dynCall_"+n];return t&&t.length?o.apply(null,[r].concat(t)):o.call(null,r)}(n,r,t):C.get(r).apply(null,t)}function Pn(n,r){var e,t,o,a=(n=$(n)).includes("j")?(e=n,t=r,o=[],function(){o.length=arguments.length;for(var n=0;n<arguments.length;n++)o[n]=arguments[n];return En(e,t,o)}):C.get(r);return"function"!=typeof a&&cn("unknown function pointer with signature "+n+": "+r),a}var Cn=void 0;function Wn(n){var r=Bn(n),e=$(r);return Dn(r),e}function Fn(n,r,e){switch(r){case 0:return e?function(n){return m[n]}:function(n){return w[n]};case 1:return e?function(n){return b[n>>1]}:function(n){return T[n>>1]};case 2:return e?function(n){return _[n>>2]}:function(n){return A[n>>2]};default:throw new TypeError("Unknown integer type: "+n)}}var Rn={};function kn(){return"object"==typeof globalThis?globalThis:Function("return this")()}function Un(n,r){var e=Q[n];return void 0===e&&cn(r+" has unknown type "+Wn(n)),e}var Sn={};function In(n){try{return l.grow(n-g.byteLength+65535>>>16),O(l.buffer),1}catch(n){}}var On={mappings:{},buffers:[null,[],[]],printChar:function(n,r){var e=On.buffers[n];0===r||10===r?((1===n?p:d)(function(n,r,e){for(var t=r+e,o=r;n[o]&&!(o>=t);)++o;return v.decode(n.subarray?n.subarray(r,o):new Uint8Array(n.slice(r,o)))}(e,0)),e.length=0):e.push(r)},varargs:void 0,get:function(){return On.varargs+=4,_[On.varargs-4>>2]},getStr:function(n){return y(n)},get64:function(n,r){return n}};!function(){for(var n=new Array(256),r=0;r<256;++r)n[r]=String.fromCharCode(r);X=n}(),un=e.BindingError=an(Error,"BindingError"),fn=e.InternalError=an(Error,"InternalError"),e.count_emval_handles=vn,e.get_first_emval=yn,Cn=e.UnboundTypeError=an(Error,"UnboundTypeError");var xn={p:function(n){return Mn(n+Z.SIZE)+Z.SIZE},e:function(n,r){},o:function(n,r,e){throw new Y(n).init(r,e),n},r:function(n,r,e,t,o){},m:function(n,r,e,t,o){var a=J(e);ln(n,{name:r=$(r),fromWireType:function(n){return!!n},toWireType:function(n,r){return r?t:o},argPackAdvance:8,readValueFromPointer:function(n){var t;if(1===e)t=m;else if(2===e)t=b;else{if(4!==e)throw new TypeError("Unknown boolean type size: "+r);t=_}return this.fromWireType(t[n>>a])},destructorFunction:null})},v:function(n,r){ln(n,{name:r=$(r),fromWireType:function(n){var r=dn[n].value;return hn(n),r},toWireType:function(n,r){return gn(r)},argPackAdvance:8,readValueFromPointer:mn,destructorFunction:null})},k:function(n,r,e){var t=J(e);ln(n,{name:r=$(r),fromWireType:function(n){return n},toWireType:function(n,r){if("number"!=typeof r&&"boolean"!=typeof r)throw new TypeError('Cannot convert "'+wn(r)+'" to '+this.name);return r},argPackAdvance:8,readValueFromPointer:bn(r,t),destructorFunction:null})},q:function(n,r,t,o,a,i){var u=function(n,r){for(var e=[],t=0;t<n;t++)e.push(_[(r>>2)+t]);return e}(r,t);n=$(n),a=Pn(o,a),An(n,(function(){!function(n,r){var e=[],t={};throw r.forEach((function n(r){t[r]||Q[r]||(nn[r]?nn[r].forEach(n):(e.push(r),t[r]=!0))})),new Cn(n+": "+e.map(Wn).join([", "]))}("Cannot call "+n+" due to unbound types",u)}),r-1),function(n,r,e){function t(r){var t=e(r);t.length!==n.length&&sn("Mismatched type converter count");for(var o=0;o<n.length;++o)ln(n[o],t[o])}n.forEach((function(n){nn[n]=r}));var o=new Array(r.length),a=[],i=0;r.forEach((function(n,r){Q.hasOwnProperty(n)?o[r]=Q[n]:(a.push(n),K.hasOwnProperty(n)||(K[n]=[]),K[n].push((function(){o[r]=Q[n],++i===a.length&&t(o)})))})),0===a.length&&t(o)}([],u,(function(t){var o=[t[0],null].concat(t.slice(1));return function(n,r,t){e.hasOwnProperty(n)||sn("Replacing nonexistant public symbol"),void 0!==e[n].overloadTable&&void 0!==t?e[n].overloadTable[t]=r:(e[n]=r,e[n].argCount=t)}(n,_n(n,o,null,a,i),r-1),[]}))},b:function(n,r,e,t,o){r=$(r),-1===o&&(o=4294967295);var a=J(e),i=function(n){return n};if(0===t){var u=32-8*e;i=function(n){return n<<u>>>u}}var c=r.includes("unsigned");ln(n,{name:r,fromWireType:i,toWireType:function(n,e){if("number"!=typeof e&&"boolean"!=typeof e)throw new TypeError('Cannot convert "'+wn(e)+'" to '+this.name);if(e<t||e>o)throw new TypeError('Passing a number "'+wn(e)+'" from JS side to C/C++ side to an argument of type "'+r+'", which is outside the valid range ['+t+", "+o+"]!");return c?e>>>0:0|e},argPackAdvance:8,readValueFromPointer:Fn(r,a,0!==t),destructorFunction:null})},a:function(n,r,e){var t=[Int8Array,Uint8Array,Int16Array,Uint16Array,Int32Array,Uint32Array,Float32Array,Float64Array][r];function o(n){var r=A,e=r[n>>=2],o=r[n+1];return new t(g,o,e)}ln(n,{name:e=$(e),fromWireType:o,argPackAdvance:8,readValueFromPointer:o},{ignoreDuplicateRegistrations:!0})},l:function(n,r){var e="std::string"===(r=$(r));ln(n,{name:r,fromWireType:function(n){var r,t=A[n>>2];if(e)for(var o=n+4,a=0;a<=t;++a){var i=n+4+a;if(a==t||0==w[i]){var u=y(o,i-o);void 0===r?r=u:(r+=String.fromCharCode(0),r+=u),o=i+1}}else{var c=new Array(t);for(a=0;a<t;++a)c[a]=String.fromCharCode(w[n+4+a]);r=c.join("")}return Dn(n),r},toWireType:function(n,r){var t;r instanceof ArrayBuffer&&(r=new Uint8Array(r));var o="string"==typeof r;o||r instanceof Uint8Array||r instanceof Uint8ClampedArray||r instanceof Int8Array||cn("Cannot pass non-string to std::string"),t=e&&o?function(){return function(n){for(var r=0,e=0;e<n.length;++e){var t=n.charCodeAt(e);t>=55296&&t<=57343&&(t=65536+((1023&t)<<10)|1023&n.charCodeAt(++e)),t<=127?++r:r+=t<=2047?2:t<=65535?3:4}return r}(r)}:function(){return r.length};var a=t(),i=Mn(4+a+1);if(A[i>>2]=a,e&&o)(function(n,r,e,t){if(!(t>0))return 0;for(var o=e,a=e+t-1,i=0;i<n.length;++i){var u=n.charCodeAt(i);if(u>=55296&&u<=57343&&(u=65536+((1023&u)<<10)|1023&n.charCodeAt(++i)),u<=127){if(e>=a)break;r[e++]=u}else if(u<=2047){if(e+1>=a)break;r[e++]=192|u>>6,r[e++]=128|63&u}else if(u<=65535){if(e+2>=a)break;r[e++]=224|u>>12,r[e++]=128|u>>6&63,r[e++]=128|63&u}else{if(e+3>=a)break;r[e++]=240|u>>18,r[e++]=128|u>>12&63,r[e++]=128|u>>6&63,r[e++]=128|63&u}}r[e]=0})(r,w,i+4,a+1);else if(o)for(var u=0;u<a;++u){var c=r.charCodeAt(u);c>255&&(Dn(i),cn("String has UTF-16 code units that do not fit in 8 bits")),w[i+4+u]=c}else for(u=0;u<a;++u)w[i+4+u]=r[u];return null!==n&&n.push(Dn,i),i},argPackAdvance:8,readValueFromPointer:mn,destructorFunction:function(n){Dn(n)}})},g:function(n,r,e){var t,o,a,i,u;e=$(e),2===r?(t=F,o=R,i=k,a=function(){return T},u=1):4===r&&(t=U,o=S,i=I,a=function(){return A},u=2),ln(n,{name:e,fromWireType:function(n){for(var e,o=A[n>>2],i=a(),c=n+4,f=0;f<=o;++f){var s=n+4+f*r;if(f==o||0==i[s>>u]){var l=t(c,s-c);void 0===e?e=l:(e+=String.fromCharCode(0),e+=l),c=s+r}}return Dn(n),e},toWireType:function(n,t){"string"!=typeof t&&cn("Cannot pass non-string to C++ string type "+e);var a=i(t),c=Mn(4+a+r);return A[c>>2]=a>>u,o(t,c+4,a+r),null!==n&&n.push(Dn,c),c},argPackAdvance:8,readValueFromPointer:mn,destructorFunction:function(n){Dn(n)}})},n:function(n,r){ln(n,{isVoid:!0,name:r=$(r),argPackAdvance:0,fromWireType:function(){},toWireType:function(n,r){}})},c:hn,d:function(n){return 0===n?gn(kn()):(n=void 0===(e=Rn[r=n])?$(r):e,gn(kn()[n]));var r,e},h:function(n){n>4&&(dn[n].refcount+=1)},i:function(n,r,t,o){n=function(n){return n||cn("Cannot use deleted val. handle = "+n),dn[n].value}(n);var a=Sn[r];return a||(a=function(n){for(var r="",t=0;t<n;++t)r+=(0!==t?", ":"")+"arg"+t;var o="return function emval_allocator_"+n+"(constructor, argTypes, args) {\n";for(t=0;t<n;++t)o+="var argType"+t+" = requireRegisteredType(Module['HEAP32'][(argTypes >>> 2) + "+t+'], "parameter '+t+'");\nvar arg'+t+" = argType"+t+".readValueFromPointer(args);\nargs += argType"+t+"['argPackAdvance'];\n";return o+="var obj = new constructor("+r+");\nreturn __emval_register(obj);\n}\n",new Function("requireRegisteredType","Module","__emval_register",o)(Un,e,gn)}(r),Sn[r]=a),a(n,t,o)},j:function(){H()},t:function(n,r,e){w.copyWithin(n,r,r+e)},f:function(n){var r,e,t=w.length,o=2147483648;if((n>>>=0)>o)return!1;for(var a=1;a<=4;a*=2){var i=t*(1+.2/a);if(i=Math.min(i,n+100663296),In(Math.min(o,((r=Math.max(n,i))%(e=65536)>0&&(r+=e-r%e),r))))return!0}return!1},u:function(n,r,e,t){for(var o=0,a=0;a<e;a++){for(var i=_[r+8*a>>2],u=_[r+(8*a+4)>>2],c=0;c<u;c++)On.printChar(n,w[i+c]);o+=u}return _[t>>2]=o,0},s:function(n){}};!function(){var n={a:xn};function r(n,r){var t,o=n.exports;e.asm=o,O((l=e.asm.w).buffer),C=e.asm.C,t=e.asm.x,j.unshift(t),function(){if(M--,e.monitorRunDependencies&&e.monitorRunDependencies(M),0==M&&B){var n=B;B=null,n()}}()}function t(n){r(n.instance)}function a(r){return(!s&&c&&"function"==typeof fetch?fetch(N,{credentials:"same-origin"}).then((function(n){if(!n.ok)throw"failed to load wasm binary file at '"+N+"'";return n.arrayBuffer()})).catch((function(){return L(N)})):Promise.resolve().then((function(){return L(N)}))).then((function(r){return WebAssembly.instantiate(r,n)})).then(r,(function(n){d("failed to asynchronously prepare wasm: "+n),H(n)}))}if(M++,e.monitorRunDependencies&&e.monitorRunDependencies(M),e.instantiateWasm)try{return e.instantiateWasm(n,r)}catch(n){return d("Module.instantiateWasm callback failed with error: "+n),!1}(s||"function"!=typeof WebAssembly.instantiateStreaming||z(N)||"function"!=typeof fetch?a(t):fetch(N,{credentials:"same-origin"}).then((function(r){return WebAssembly.instantiateStreaming(r,n).then(t,(function(n){return d("wasm streaming compile failed: "+n),d("falling back to ArrayBuffer instantiation"),a(t)}))}))).catch(o)}(),e.___wasm_call_ctors=function(){return(e.___wasm_call_ctors=e.asm.x).apply(null,arguments)};var jn,Dn=e._free=function(){return(Dn=e._free=e.asm.y).apply(null,arguments)},Mn=e._malloc=function(){return(Mn=e._malloc=e.asm.z).apply(null,arguments)},Bn=e.___getTypeName=function(){return(Bn=e.___getTypeName=e.asm.A).apply(null,arguments)};function Hn(n){function r(){jn||(jn=!0,e.calledRun=!0,h||(G(j),t(e),e.onRuntimeInitialized&&e.onRuntimeInitialized(),function(){if(e.postRun)for("function"==typeof e.postRun&&(e.postRun=[e.postRun]);e.postRun.length;)n=e.postRun.shift(),D.unshift(n);var n;G(D)}()))}M>0||(!function(){if(e.preRun)for("function"==typeof e.preRun&&(e.preRun=[e.preRun]);e.preRun.length;)n=e.preRun.shift(),x.unshift(n);var n;G(x)}(),M>0||(e.setStatus?(e.setStatus("Running..."),setTimeout((function(){setTimeout((function(){e.setStatus("")}),1),r()}),1)):r()))}if(e.___embind_register_native_and_builtin_types=function(){return(e.___embind_register_native_and_builtin_types=e.asm.B).apply(null,arguments)},e.dynCall_jiji=function(){return(e.dynCall_jiji=e.asm.D).apply(null,arguments)},B=function n(){jn||Hn(),jn||(B=n)},e.run=Hn,e.preInit)for("function"==typeof e.preInit&&(e.preInit=[e.preInit]);e.preInit.length>0;)e.preInit.pop()();return Hn(),e.ready}}();let v,y,g;async function m(n,e){y||(y=async function(){if(await t.checkThreadsSupport())return a((await r("./avif_enc_mt-f67faaa7")).default);return a((await r("./avif_enc-04767266")).default)}());const o=(await y).encode(n.data,n.width,n.height,e);if(!o)throw new Error("Encoding error");return o.buffer}async function w(n,e){g||(g=async function(){if(await t.checkThreadsSupport())return await t.simd()?a((await r("./jxl_enc_mt_simd-7f2fa652")).default):a((await r("./jxl_enc_mt-9f48262c")).default);return a((await r("./jxl_enc-fe8a44c4")).default)}());const o=(await g).encode(n.data,n.width,n.height,e);if(!o)throw new Error("Encoding error.");return o.buffer}var b=function(){var r=n.uri;return function(e){var t,o;(e=void 0!==(e=e||{})?e:{}).ready=new Promise((function(n,r){t=n,o=r}));var a,i={};for(a in e)e.hasOwnProperty(a)&&(i[a]=e[a]);var u,c="./this.program",f=function(n,r){throw r},s=!0,l="";l=self.location.href,r&&(l=r),l=0!==l.indexOf("blob:")?l.substr(0,l.lastIndexOf("/")+1):"",u=function(n){var r=new XMLHttpRequest;return r.open("GET",n,!1),r.responseType="arraybuffer",r.send(null),new Uint8Array(r.response)};var p,d=e.print||console.log.bind(console),h=e.printErr||console.warn.bind(console);for(a in i)i.hasOwnProperty(a)&&(e[a]=i[a]);i=null,e.arguments&&e.arguments,e.thisProgram&&(c=e.thisProgram),e.quit&&(f=e.quit),e.wasmBinary&&(p=e.wasmBinary);var v,y=e.noExitRuntime||!0;"object"!=typeof WebAssembly&&z("no native wasm support detected");var g=!1,m=new TextDecoder("utf8");function w(n,r){if(!n)return"";for(var e=n+r,t=n;!(t>=e)&&_[t];)++t;return m.decode(_.subarray(n,t))}var b,T,_,A,E,P,C,W,F,R,k=new TextDecoder("utf-16le");function U(n,r){for(var e=n,t=e>>1,o=t+r/2;!(t>=o)&&E[t];)++t;return e=t<<1,k.decode(_.subarray(n,e))}function S(n,r,e){if(void 0===e&&(e=2147483647),e<2)return 0;for(var t=r,o=(e-=2)<2*n.length?e/2:n.length,a=0;a<o;++a){var i=n.charCodeAt(a);A[r>>1]=i,r+=2}return A[r>>1]=0,r-t}function I(n){return 2*n.length}function O(n,r){for(var e=0,t="";!(e>=r/4);){var o=P[n+4*e>>2];if(0==o)break;if(++e,o>=65536){var a=o-65536;t+=String.fromCharCode(55296|a>>10,56320|1023&a)}else t+=String.fromCharCode(o)}return t}function x(n,r,e){if(void 0===e&&(e=2147483647),e<4)return 0;for(var t=r,o=t+e-4,a=0;a<n.length;++a){var i=n.charCodeAt(a);if(i>=55296&&i<=57343)i=65536+((1023&i)<<10)|1023&n.charCodeAt(++a);if(P[r>>2]=i,(r+=4)+4>o)break}return P[r>>2]=0,r-t}function j(n){for(var r=0,e=0;e<n.length;++e){var t=n.charCodeAt(e);t>=55296&&t<=57343&&++e,r+=4}return r}function D(n){b=n,e.HEAP8=T=new Int8Array(n),e.HEAP16=A=new Int16Array(n),e.HEAP32=P=new Int32Array(n),e.HEAPU8=_=new Uint8Array(n),e.HEAPU16=E=new Uint16Array(n),e.HEAPU32=C=new Uint32Array(n),e.HEAPF32=W=new Float32Array(n),e.HEAPF64=F=new Float64Array(n)}e.INITIAL_MEMORY;var M=[],B=[],H=[];var q=0,V=null;function z(n){e.onAbort&&e.onAbort(n),h(n+=""),g=!0,n="abort("+n+"). Build with -s ASSERTIONS=1 for more info.";var r=new WebAssembly.RuntimeError(n);throw o(r),r}e.preloadedImages={},e.preloadedAudios={};var N,L="data:application/octet-stream;base64,";function G(n){return n.startsWith(L)}if(e.locateFile)G(Z="mozjpeg_enc.wasm")||(N=Z,Z=e.locateFile?e.locateFile(N,l):l+N);else var Z=new URL("/c/mozjpeg_enc-f6bf569c.wasm",n.uri).toString();function Y(n){try{if(n==Z&&p)return new Uint8Array(p);if(u)return u(n);throw"both async and sync fetching of the wasm failed"}catch(n){z(n)}}function J(n){for(;n.length>0;){var r=n.shift();if("function"!=typeof r){var t=r.func;"number"==typeof t?void 0===r.arg?R.get(t)():R.get(t)(r.arg):t(void 0===r.arg?null:r.arg)}else r(e)}}var X=0;function $(){return y||X>0}var K={};function Q(n){for(;n.length;){var r=n.pop();n.pop()(r)}}function nn(n){return this.fromWireType(C[n>>2])}var rn={},en={},tn={},on=48,an=57;function un(n){if(void 0===n)return"_unknown";var r=(n=n.replace(/[^a-zA-Z0-9_]/g,"$")).charCodeAt(0);return r>=on&&r<=an?"_"+n:n}function cn(n,r){return n=un(n),new Function("body","return function "+n+'() {\n    "use strict";    return body.apply(this, arguments);\n};\n')(r)}function fn(n,r){var e=cn(r,(function(n){this.name=r,this.message=n;var e=new Error(n).stack;void 0!==e&&(this.stack=this.toString()+"\n"+e.replace(/^Error(:[^\n]*)?\n/,""))}));return e.prototype=Object.create(n.prototype),e.prototype.constructor=e,e.prototype.toString=function(){return void 0===this.message?this.name:this.name+": "+this.message},e}var sn=void 0;function ln(n){throw new sn(n)}function pn(n,r,e){function t(r){var t=e(r);t.length!==n.length&&ln("Mismatched type converter count");for(var o=0;o<n.length;++o)mn(n[o],t[o])}n.forEach((function(n){tn[n]=r}));var o=new Array(r.length),a=[],i=0;r.forEach((function(n,r){en.hasOwnProperty(n)?o[r]=en[n]:(a.push(n),rn.hasOwnProperty(n)||(rn[n]=[]),rn[n].push((function(){o[r]=en[n],++i===a.length&&t(o)})))})),0===a.length&&t(o)}function dn(n){switch(n){case 1:return 0;case 2:return 1;case 4:return 2;case 8:return 3;default:throw new TypeError("Unknown type size: "+n)}}var hn=void 0;function vn(n){for(var r="",e=n;_[e];)r+=hn[_[e++]];return r}var yn=void 0;function gn(n){throw new yn(n)}function mn(n,r,e){if(e=e||{},!("argPackAdvance"in r))throw new TypeError("registerType registeredInstance requires argPackAdvance");var t=r.name;if(n||gn('type "'+t+'" must have a positive integer typeid pointer'),en.hasOwnProperty(n)){if(e.ignoreDuplicateRegistrations)return;gn("Cannot register type '"+t+"' twice")}if(en[n]=r,delete tn[n],rn.hasOwnProperty(n)){var o=rn[n];delete rn[n],o.forEach((function(n){n()}))}}var wn=[],bn=[{},{value:void 0},{value:null},{value:!0},{value:!1}];function Tn(n){n>4&&0==--bn[n].refcount&&(bn[n]=void 0,wn.push(n))}function _n(){for(var n=0,r=5;r<bn.length;++r)void 0!==bn[r]&&++n;return n}function An(){for(var n=5;n<bn.length;++n)if(void 0!==bn[n])return bn[n];return null}function En(n){switch(n){case void 0:return 1;case null:return 2;case!0:return 3;case!1:return 4;default:var r=wn.length?wn.pop():bn.length;return bn[r]={refcount:1,value:n},r}}function Pn(n){if(null===n)return"null";var r=typeof n;return"object"===r||"array"===r||"function"===r?n.toString():""+n}function Cn(n,r){switch(r){case 2:return function(n){return this.fromWireType(W[n>>2])};case 3:return function(n){return this.fromWireType(F[n>>3])};default:throw new TypeError("Unknown float type: "+n)}}function Wn(n,r,e,t,o){var a=r.length;a<2&&gn("argTypes array size mismatch! Must at least get return value and 'this' types!");for(var i=null!==r[1]&&null!==e,u=!1,c=1;c<r.length;++c)if(null!==r[c]&&void 0===r[c].destructorFunction){u=!0;break}var f="void"!==r[0].name,s="",l="";for(c=0;c<a-2;++c)s+=(0!==c?", ":"")+"arg"+c,l+=(0!==c?", ":"")+"arg"+c+"Wired";var p="return function "+un(n)+"("+s+") {\nif (arguments.length !== "+(a-2)+") {\nthrowBindingError('function "+n+" called with ' + arguments.length + ' arguments, expected "+(a-2)+" args!');\n}\n";u&&(p+="var destructors = [];\n");var d=u?"destructors":"null",h=["throwBindingError","invoker","fn","runDestructors","retType","classParam"],v=[gn,t,o,Q,r[0],r[1]];i&&(p+="var thisWired = classParam.toWireType("+d+", this);\n");for(c=0;c<a-2;++c)p+="var arg"+c+"Wired = argType"+c+".toWireType("+d+", arg"+c+"); // "+r[c+2].name+"\n",h.push("argType"+c),v.push(r[c+2]);if(i&&(l="thisWired"+(l.length>0?", ":"")+l),p+=(f?"var rv = ":"")+"invoker(fn"+(l.length>0?", ":"")+l+");\n",u)p+="runDestructors(destructors);\n";else for(c=i?1:2;c<r.length;++c){var y=1===c?"thisWired":"arg"+(c-2)+"Wired";null!==r[c].destructorFunction&&(p+=y+"_dtor("+y+"); // "+r[c].name+"\n",h.push(y+"_dtor"),v.push(r[c].destructorFunction))}return f&&(p+="var ret = retType.fromWireType(rv);\nreturn ret;\n"),p+="}\n",h.push(p),function(n,r){if(!(n instanceof Function))throw new TypeError("new_ called with constructor type "+typeof n+" which is not a function");var e=cn(n.name||"unknownFunctionName",(function(){}));e.prototype=n.prototype;var t=new e,o=n.apply(t,r);return o instanceof Object?o:t}(Function,h).apply(null,v)}function Fn(n,r,t){e.hasOwnProperty(n)?((void 0===t||void 0!==e[n].overloadTable&&void 0!==e[n].overloadTable[t])&&gn("Cannot register public name '"+n+"' twice"),function(n,r,e){if(void 0===n[r].overloadTable){var t=n[r];n[r]=function(){return n[r].overloadTable.hasOwnProperty(arguments.length)||gn("Function '"+e+"' called with an invalid number of arguments ("+arguments.length+") - expects one of ("+n[r].overloadTable+")!"),n[r].overloadTable[arguments.length].apply(this,arguments)},n[r].overloadTable=[],n[r].overloadTable[t.argCount]=t}}(e,n,n),e.hasOwnProperty(t)&&gn("Cannot register multiple overloads of a function with the same number of arguments ("+t+")!"),e[n].overloadTable[t]=r):(e[n]=r,void 0!==t&&(e[n].numArguments=t))}function Rn(n,r,t){return n.includes("j")?function(n,r,t){var o=e["dynCall_"+n];return t&&t.length?o.apply(null,[r].concat(t)):o.call(null,r)}(n,r,t):R.get(r).apply(null,t)}function kn(n,r){var e,t,o,a=(n=vn(n)).includes("j")?(e=n,t=r,o=[],function(){o.length=arguments.length;for(var n=0;n<arguments.length;n++)o[n]=arguments[n];return Rn(e,t,o)}):R.get(r);return"function"!=typeof a&&gn("unknown function pointer with signature "+n+": "+r),a}var Un=void 0;function Sn(n){var r=Gn(n),e=vn(r);return Ln(r),e}function In(n,r,e){switch(r){case 0:return e?function(n){return T[n]}:function(n){return _[n]};case 1:return e?function(n){return A[n>>1]}:function(n){return E[n>>1]};case 2:return e?function(n){return P[n>>2]}:function(n){return C[n>>2]};default:throw new TypeError("Unknown integer type: "+n)}}var On={};function xn(){return"object"==typeof globalThis?globalThis:Function("return this")()}function jn(n,r){var e=en[n];return void 0===e&&gn(r+" has unknown type "+Sn(n)),e}var Dn={};function Mn(n){try{return v.grow(n-b.byteLength+65535>>>16),D(v.buffer),1}catch(n){}}var Bn={};function Hn(){if(!Hn.strings){var n={USER:"web_user",LOGNAME:"web_user",PATH:"/",PWD:"/",HOME:"/home/<USER>",LANG:("object"==typeof navigator&&navigator.languages&&navigator.languages[0]||"C").replace("-","_")+".UTF-8",_:c||"./this.program"};for(var r in Bn)n[r]=Bn[r];var e=[];for(var r in n)e.push(r+"="+n[r]);Hn.strings=e}return Hn.strings}var qn={mappings:{},buffers:[null,[],[]],printChar:function(n,r){var e=qn.buffers[n];0===r||10===r?((1===n?d:h)(function(n,r,e){for(var t=r+e,o=r;n[o]&&!(o>=t);)++o;return m.decode(n.subarray?n.subarray(r,o):new Uint8Array(n.slice(r,o)))}(e,0)),e.length=0):e.push(r)},varargs:void 0,get:function(){return qn.varargs+=4,P[qn.varargs-4>>2]},getStr:function(n){return w(n)},get64:function(n,r){return n}};sn=e.InternalError=fn(Error,"InternalError"),function(){for(var n=new Array(256),r=0;r<256;++r)n[r]=String.fromCharCode(r);hn=n}(),yn=e.BindingError=fn(Error,"BindingError"),e.count_emval_handles=_n,e.get_first_emval=An,Un=e.UnboundTypeError=fn(Error,"UnboundTypeError");var Vn={B:function(n,r){},l:function(n){var r=K[n];delete K[n];var e=r.rawConstructor,t=r.rawDestructor,o=r.fields;pn([n],o.map((function(n){return n.getterReturnType})).concat(o.map((function(n){return n.setterArgumentType}))),(function(n){var a={};return o.forEach((function(r,e){var t=r.fieldName,i=n[e],u=r.getter,c=r.getterContext,f=n[e+o.length],s=r.setter,l=r.setterContext;a[t]={read:function(n){return i.fromWireType(u(c,n))},write:function(n,r){var e=[];s(l,n,f.toWireType(e,r)),Q(e)}}})),[{name:r.name,fromWireType:function(n){var r={};for(var e in a)r[e]=a[e].read(n);return t(n),r},toWireType:function(n,r){for(var o in a)if(!(o in r))throw new TypeError('Missing field:  "'+o+'"');var i=e();for(o in a)a[o].write(i,r[o]);return null!==n&&n.push(t,i),i},argPackAdvance:8,readValueFromPointer:nn,destructorFunction:t}]}))},p:function(n,r,e,t,o){},y:function(n,r,e,t,o){var a=dn(e);mn(n,{name:r=vn(r),fromWireType:function(n){return!!n},toWireType:function(n,r){return r?t:o},argPackAdvance:8,readValueFromPointer:function(n){var t;if(1===e)t=T;else if(2===e)t=A;else{if(4!==e)throw new TypeError("Unknown boolean type size: "+r);t=P}return this.fromWireType(t[n>>a])},destructorFunction:null})},x:function(n,r){mn(n,{name:r=vn(r),fromWireType:function(n){var r=bn[n].value;return Tn(n),r},toWireType:function(n,r){return En(r)},argPackAdvance:8,readValueFromPointer:nn,destructorFunction:null})},i:function(n,r,e){var t=dn(e);mn(n,{name:r=vn(r),fromWireType:function(n){return n},toWireType:function(n,r){if("number"!=typeof r&&"boolean"!=typeof r)throw new TypeError('Cannot convert "'+Pn(r)+'" to '+this.name);return r},argPackAdvance:8,readValueFromPointer:Cn(r,t),destructorFunction:null})},f:function(n,r,t,o,a,i){var u=function(n,r){for(var e=[],t=0;t<n;t++)e.push(P[(r>>2)+t]);return e}(r,t);n=vn(n),a=kn(o,a),Fn(n,(function(){!function(n,r){var e=[],t={};throw r.forEach((function n(r){t[r]||en[r]||(tn[r]?tn[r].forEach(n):(e.push(r),t[r]=!0))})),new Un(n+": "+e.map(Sn).join([", "]))}("Cannot call "+n+" due to unbound types",u)}),r-1),pn([],u,(function(t){var o=[t[0],null].concat(t.slice(1));return function(n,r,t){e.hasOwnProperty(n)||ln("Replacing nonexistant public symbol"),void 0!==e[n].overloadTable&&void 0!==t?e[n].overloadTable[t]=r:(e[n]=r,e[n].argCount=t)}(n,Wn(n,o,null,a,i),r-1),[]}))},c:function(n,r,e,t,o){r=vn(r),-1===o&&(o=4294967295);var a=dn(e),i=function(n){return n};if(0===t){var u=32-8*e;i=function(n){return n<<u>>>u}}var c=r.includes("unsigned");mn(n,{name:r,fromWireType:i,toWireType:function(n,e){if("number"!=typeof e&&"boolean"!=typeof e)throw new TypeError('Cannot convert "'+Pn(e)+'" to '+this.name);if(e<t||e>o)throw new TypeError('Passing a number "'+Pn(e)+'" from JS side to C/C++ side to an argument of type "'+r+'", which is outside the valid range ['+t+", "+o+"]!");return c?e>>>0:0|e},argPackAdvance:8,readValueFromPointer:In(r,a,0!==t),destructorFunction:null})},b:function(n,r,e){var t=[Int8Array,Uint8Array,Int16Array,Uint16Array,Int32Array,Uint32Array,Float32Array,Float64Array][r];function o(n){var r=C,e=r[n>>=2],o=r[n+1];return new t(b,o,e)}mn(n,{name:e=vn(e),fromWireType:o,argPackAdvance:8,readValueFromPointer:o},{ignoreDuplicateRegistrations:!0})},j:function(n,r){var e="std::string"===(r=vn(r));mn(n,{name:r,fromWireType:function(n){var r,t=C[n>>2];if(e)for(var o=n+4,a=0;a<=t;++a){var i=n+4+a;if(a==t||0==_[i]){var u=w(o,i-o);void 0===r?r=u:(r+=String.fromCharCode(0),r+=u),o=i+1}}else{var c=new Array(t);for(a=0;a<t;++a)c[a]=String.fromCharCode(_[n+4+a]);r=c.join("")}return Ln(n),r},toWireType:function(n,r){var t;r instanceof ArrayBuffer&&(r=new Uint8Array(r));var o="string"==typeof r;o||r instanceof Uint8Array||r instanceof Uint8ClampedArray||r instanceof Int8Array||gn("Cannot pass non-string to std::string"),t=e&&o?function(){return function(n){for(var r=0,e=0;e<n.length;++e){var t=n.charCodeAt(e);t>=55296&&t<=57343&&(t=65536+((1023&t)<<10)|1023&n.charCodeAt(++e)),t<=127?++r:r+=t<=2047?2:t<=65535?3:4}return r}(r)}:function(){return r.length};var a=t(),i=Nn(4+a+1);if(C[i>>2]=a,e&&o)(function(n,r,e,t){if(!(t>0))return 0;for(var o=e,a=e+t-1,i=0;i<n.length;++i){var u=n.charCodeAt(i);if(u>=55296&&u<=57343&&(u=65536+((1023&u)<<10)|1023&n.charCodeAt(++i)),u<=127){if(e>=a)break;r[e++]=u}else if(u<=2047){if(e+1>=a)break;r[e++]=192|u>>6,r[e++]=128|63&u}else if(u<=65535){if(e+2>=a)break;r[e++]=224|u>>12,r[e++]=128|u>>6&63,r[e++]=128|63&u}else{if(e+3>=a)break;r[e++]=240|u>>18,r[e++]=128|u>>12&63,r[e++]=128|u>>6&63,r[e++]=128|63&u}}r[e]=0})(r,_,i+4,a+1);else if(o)for(var u=0;u<a;++u){var c=r.charCodeAt(u);c>255&&(Ln(i),gn("String has UTF-16 code units that do not fit in 8 bits")),_[i+4+u]=c}else for(u=0;u<a;++u)_[i+4+u]=r[u];return null!==n&&n.push(Ln,i),i},argPackAdvance:8,readValueFromPointer:nn,destructorFunction:function(n){Ln(n)}})},e:function(n,r,e){var t,o,a,i,u;e=vn(e),2===r?(t=U,o=S,i=I,a=function(){return E},u=1):4===r&&(t=O,o=x,i=j,a=function(){return C},u=2),mn(n,{name:e,fromWireType:function(n){for(var e,o=C[n>>2],i=a(),c=n+4,f=0;f<=o;++f){var s=n+4+f*r;if(f==o||0==i[s>>u]){var l=t(c,s-c);void 0===e?e=l:(e+=String.fromCharCode(0),e+=l),c=s+r}}return Ln(n),e},toWireType:function(n,t){"string"!=typeof t&&gn("Cannot pass non-string to C++ string type "+e);var a=i(t),c=Nn(4+a+r);return C[c>>2]=a>>u,o(t,c+4,a+r),null!==n&&n.push(Ln,c),c},argPackAdvance:8,readValueFromPointer:nn,destructorFunction:function(n){Ln(n)}})},m:function(n,r,e,t,o,a){K[n]={name:vn(r),rawConstructor:kn(e,t),rawDestructor:kn(o,a),fields:[]}},a:function(n,r,e,t,o,a,i,u,c,f){K[n].fields.push({fieldName:vn(r),getterReturnType:e,getter:kn(t,o),getterContext:a,setterArgumentType:i,setter:kn(u,c),setterContext:f})},z:function(n,r){mn(n,{isVoid:!0,name:r=vn(r),argPackAdvance:0,fromWireType:function(){},toWireType:function(n,r){}})},g:Tn,u:function(n){return 0===n?En(xn()):(n=void 0===(e=On[r=n])?vn(r):e,En(xn()[n]));var r,e},k:function(n){n>4&&(bn[n].refcount+=1)},n:function(n,r,t,o){n=function(n){return n||gn("Cannot use deleted val. handle = "+n),bn[n].value}(n);var a=Dn[r];return a||(a=function(n){for(var r="",t=0;t<n;++t)r+=(0!==t?", ":"")+"arg"+t;var o="return function emval_allocator_"+n+"(constructor, argTypes, args) {\n";for(t=0;t<n;++t)o+="var argType"+t+" = requireRegisteredType(Module['HEAP32'][(argTypes >>> 2) + "+t+'], "parameter '+t+'");\nvar arg'+t+" = argType"+t+".readValueFromPointer(args);\nargs += argType"+t+"['argPackAdvance'];\n";return o+="var obj = new constructor("+r+");\nreturn __emval_register(obj);\n}\n",new Function("requireRegisteredType","Module","__emval_register",o)(jn,e,En)}(r),Dn[r]=a),a(n,t,o)},h:function(){z()},r:function(n,r,e){_.copyWithin(n,r,r+e)},d:function(n){var r,e,t=_.length,o=2147483648;if((n>>>=0)>o)return!1;for(var a=1;a<=4;a*=2){var i=t*(1+.2/a);if(i=Math.min(i,n+100663296),Mn(Math.min(o,((r=Math.max(n,i))%(e=65536)>0&&(r+=e-r%e),r))))return!0}return!1},s:function(n,r){var e=0;return Hn().forEach((function(t,o){var a=r+e;P[n+4*o>>2]=a,function(n,r,e){for(var t=0;t<n.length;++t)T[0|r++]=n.charCodeAt(t);e||(T[0|r]=0)}(t,a),e+=t.length+1})),0},t:function(n,r){var e=Hn();P[n>>2]=e.length;var t=0;return e.forEach((function(n){t+=n.length+1})),P[r>>2]=t,0},A:function(n){!function(n,r){if(r&&$()&&0===n)return;$()||(e.onExit&&e.onExit(n),g=!0);f(n,new Zn(n))}(n)},w:function(n){return 0},o:function(n,r,e,t,o){},v:function(n,r,e,t){for(var o=0,a=0;a<e;a++){for(var i=P[r+8*a>>2],u=P[r+(8*a+4)>>2],c=0;c<u;c++)qn.printChar(n,_[i+c]);o+=u}return P[t>>2]=o,0},q:function(n){}};!function(){var n={a:Vn};function r(n,r){var t,o=n.exports;e.asm=o,D((v=e.asm.C).buffer),R=e.asm.I,t=e.asm.D,B.unshift(t),function(){if(q--,e.monitorRunDependencies&&e.monitorRunDependencies(q),0==q&&V){var n=V;V=null,n()}}()}function t(n){r(n.instance)}function a(r){return(!p&&s&&"function"==typeof fetch?fetch(Z,{credentials:"same-origin"}).then((function(n){if(!n.ok)throw"failed to load wasm binary file at '"+Z+"'";return n.arrayBuffer()})).catch((function(){return Y(Z)})):Promise.resolve().then((function(){return Y(Z)}))).then((function(r){return WebAssembly.instantiate(r,n)})).then(r,(function(n){h("failed to asynchronously prepare wasm: "+n),z(n)}))}if(q++,e.monitorRunDependencies&&e.monitorRunDependencies(q),e.instantiateWasm)try{return e.instantiateWasm(n,r)}catch(n){return h("Module.instantiateWasm callback failed with error: "+n),!1}(p||"function"!=typeof WebAssembly.instantiateStreaming||G(Z)||"function"!=typeof fetch?a(t):fetch(Z,{credentials:"same-origin"}).then((function(r){return WebAssembly.instantiateStreaming(r,n).then(t,(function(n){return h("wasm streaming compile failed: "+n),h("falling back to ArrayBuffer instantiation"),a(t)}))}))).catch(o)}(),e.___wasm_call_ctors=function(){return(e.___wasm_call_ctors=e.asm.D).apply(null,arguments)};var zn,Nn=e._malloc=function(){return(Nn=e._malloc=e.asm.E).apply(null,arguments)},Ln=e._free=function(){return(Ln=e._free=e.asm.F).apply(null,arguments)},Gn=e.___getTypeName=function(){return(Gn=e.___getTypeName=e.asm.G).apply(null,arguments)};function Zn(n){this.name="ExitStatus",this.message="Program terminated with exit("+n+")",this.status=n}function Yn(n){function r(){zn||(zn=!0,e.calledRun=!0,g||(J(B),t(e),e.onRuntimeInitialized&&e.onRuntimeInitialized(),function(){if(e.postRun)for("function"==typeof e.postRun&&(e.postRun=[e.postRun]);e.postRun.length;)n=e.postRun.shift(),H.unshift(n);var n;J(H)}()))}q>0||(!function(){if(e.preRun)for("function"==typeof e.preRun&&(e.preRun=[e.preRun]);e.preRun.length;)n=e.preRun.shift(),M.unshift(n);var n;J(M)}(),q>0||(e.setStatus?(e.setStatus("Running..."),setTimeout((function(){setTimeout((function(){e.setStatus("")}),1),r()}),1)):r()))}if(e.___embind_register_native_and_builtin_types=function(){return(e.___embind_register_native_and_builtin_types=e.asm.H).apply(null,arguments)},e.dynCall_jiji=function(){return(e.dynCall_jiji=e.asm.J).apply(null,arguments)},V=function n(){zn||Yn(),zn||(V=n)},e.run=Yn,e.preInit)for("function"==typeof e.preInit&&(e.preInit=[e.preInit]);e.preInit.length>0;)e.preInit.pop()();return Yn(),e.ready}}();let T,_;async function A(n,e){_||(_=t.checkThreadsSupport().then((n=>n?async function(){const{default:n,initThreadPool:e,optimise:t}=await r("./squoosh_oxipng-b619b9e2");return await n(),await e(navigator.hardwareConcurrency),t}():async function(){const{default:n,optimise:e}=await r("./squoosh_oxipng-78efb8fb");return await n(),e}())));return(await _)(n.data,n.width,n.height,e.level,e.interlace).buffer}var E=function(){var r=n.uri;return function(e){var t,o;(e=void 0!==(e=e||{})?e:{}).ready=new Promise((function(n,r){t=n,o=r}));var a,i={};for(a in e)e.hasOwnProperty(a)&&(i[a]=e[a]);var u,c=!0,f="";f=self.location.href,r&&(f=r),f=0!==f.indexOf("blob:")?f.substr(0,f.lastIndexOf("/")+1):"",u=function(n){var r=new XMLHttpRequest;return r.open("GET",n,!1),r.responseType="arraybuffer",r.send(null),new Uint8Array(r.response)},e.print||console.log.bind(console);var s,l,p=e.printErr||console.warn.bind(console);for(a in i)i.hasOwnProperty(a)&&(e[a]=i[a]);i=null,e.arguments&&e.arguments,e.thisProgram&&e.thisProgram,e.quit&&e.quit,e.wasmBinary&&(s=e.wasmBinary),e.noExitRuntime,"object"!=typeof WebAssembly&&B("no native wasm support detected");var d=!1,h=new TextDecoder("utf8");function v(n,r){if(!n)return"";for(var e=n+r,t=n;!(t>=e)&&m[t];)++t;return h.decode(m.subarray(n,t))}var y,g,m,w,b,T,_,A,E,P,C=new TextDecoder("utf-16le");function W(n,r){for(var e=n,t=e>>1,o=t+r/2;!(t>=o)&&b[t];)++t;return e=t<<1,C.decode(m.subarray(n,e))}function F(n,r,e){if(void 0===e&&(e=2147483647),e<2)return 0;for(var t=r,o=(e-=2)<2*n.length?e/2:n.length,a=0;a<o;++a){var i=n.charCodeAt(a);w[r>>1]=i,r+=2}return w[r>>1]=0,r-t}function R(n){return 2*n.length}function k(n,r){for(var e=0,t="";!(e>=r/4);){var o=T[n+4*e>>2];if(0==o)break;if(++e,o>=65536){var a=o-65536;t+=String.fromCharCode(55296|a>>10,56320|1023&a)}else t+=String.fromCharCode(o)}return t}function U(n,r,e){if(void 0===e&&(e=2147483647),e<4)return 0;for(var t=r,o=t+e-4,a=0;a<n.length;++a){var i=n.charCodeAt(a);if(i>=55296&&i<=57343)i=65536+((1023&i)<<10)|1023&n.charCodeAt(++a);if(T[r>>2]=i,(r+=4)+4>o)break}return T[r>>2]=0,r-t}function S(n){for(var r=0,e=0;e<n.length;++e){var t=n.charCodeAt(e);t>=55296&&t<=57343&&++e,r+=4}return r}function I(n){y=n,e.HEAP8=g=new Int8Array(n),e.HEAP16=w=new Int16Array(n),e.HEAP32=T=new Int32Array(n),e.HEAPU8=m=new Uint8Array(n),e.HEAPU16=b=new Uint16Array(n),e.HEAPU32=_=new Uint32Array(n),e.HEAPF32=A=new Float32Array(n),e.HEAPF64=E=new Float64Array(n)}e.INITIAL_MEMORY;var O=[],x=[],j=[];var D=0,M=null;function B(n){e.onAbort&&e.onAbort(n),p(n+=""),d=!0,n="abort("+n+"). Build with -s ASSERTIONS=1 for more info.";var r=new WebAssembly.RuntimeError(n);throw o(r),r}e.preloadedImages={},e.preloadedAudios={};var H,q="data:application/octet-stream;base64,";function V(n){return n.startsWith(q)}if(e.locateFile)V(z="qoi_enc.wasm")||(H=z,z=e.locateFile?e.locateFile(H,f):f+H);else var z=new URL("/c/qoi_enc-9285b08c.wasm",n.uri).toString();function N(n){try{if(n==z&&s)return new Uint8Array(s);if(u)return u(n);throw"both async and sync fetching of the wasm failed"}catch(n){B(n)}}function L(n){for(;n.length>0;){var r=n.shift();if("function"!=typeof r){var t=r.func;"number"==typeof t?void 0===r.arg?P.get(t)():P.get(t)(r.arg):t(void 0===r.arg?null:r.arg)}else r(e)}}var G={};function Z(n){for(;n.length;){var r=n.pop();n.pop()(r)}}function Y(n){return this.fromWireType(_[n>>2])}var J={},X={},$={},K=48,Q=57;function nn(n){if(void 0===n)return"_unknown";var r=(n=n.replace(/[^a-zA-Z0-9_]/g,"$")).charCodeAt(0);return r>=K&&r<=Q?"_"+n:n}function rn(n,r){return n=nn(n),new Function("body","return function "+n+'() {\n    "use strict";    return body.apply(this, arguments);\n};\n')(r)}function en(n,r){var e=rn(r,(function(n){this.name=r,this.message=n;var e=new Error(n).stack;void 0!==e&&(this.stack=this.toString()+"\n"+e.replace(/^Error(:[^\n]*)?\n/,""))}));return e.prototype=Object.create(n.prototype),e.prototype.constructor=e,e.prototype.toString=function(){return void 0===this.message?this.name:this.name+": "+this.message},e}var tn=void 0;function on(n){throw new tn(n)}function an(n,r,e){function t(r){var t=e(r);t.length!==n.length&&on("Mismatched type converter count");for(var o=0;o<n.length;++o)pn(n[o],t[o])}n.forEach((function(n){$[n]=r}));var o=new Array(r.length),a=[],i=0;r.forEach((function(n,r){X.hasOwnProperty(n)?o[r]=X[n]:(a.push(n),J.hasOwnProperty(n)||(J[n]=[]),J[n].push((function(){o[r]=X[n],++i===a.length&&t(o)})))})),0===a.length&&t(o)}function un(n){switch(n){case 1:return 0;case 2:return 1;case 4:return 2;case 8:return 3;default:throw new TypeError("Unknown type size: "+n)}}var cn=void 0;function fn(n){for(var r="",e=n;m[e];)r+=cn[m[e++]];return r}var sn=void 0;function ln(n){throw new sn(n)}function pn(n,r,e){if(e=e||{},!("argPackAdvance"in r))throw new TypeError("registerType registeredInstance requires argPackAdvance");var t=r.name;if(n||ln('type "'+t+'" must have a positive integer typeid pointer'),X.hasOwnProperty(n)){if(e.ignoreDuplicateRegistrations)return;ln("Cannot register type '"+t+"' twice")}if(X[n]=r,delete $[n],J.hasOwnProperty(n)){var o=J[n];delete J[n],o.forEach((function(n){n()}))}}var dn=[],hn=[{},{value:void 0},{value:null},{value:!0},{value:!1}];function vn(n){n>4&&0==--hn[n].refcount&&(hn[n]=void 0,dn.push(n))}function yn(){for(var n=0,r=5;r<hn.length;++r)void 0!==hn[r]&&++n;return n}function gn(){for(var n=5;n<hn.length;++n)if(void 0!==hn[n])return hn[n];return null}function mn(n){switch(n){case void 0:return 1;case null:return 2;case!0:return 3;case!1:return 4;default:var r=dn.length?dn.pop():hn.length;return hn[r]={refcount:1,value:n},r}}function wn(n){if(null===n)return"null";var r=typeof n;return"object"===r||"array"===r||"function"===r?n.toString():""+n}function bn(n,r){switch(r){case 2:return function(n){return this.fromWireType(A[n>>2])};case 3:return function(n){return this.fromWireType(E[n>>3])};default:throw new TypeError("Unknown float type: "+n)}}function Tn(n,r,e,t,o){var a=r.length;a<2&&ln("argTypes array size mismatch! Must at least get return value and 'this' types!");for(var i=null!==r[1]&&null!==e,u=!1,c=1;c<r.length;++c)if(null!==r[c]&&void 0===r[c].destructorFunction){u=!0;break}var f="void"!==r[0].name,s="",l="";for(c=0;c<a-2;++c)s+=(0!==c?", ":"")+"arg"+c,l+=(0!==c?", ":"")+"arg"+c+"Wired";var p="return function "+nn(n)+"("+s+") {\nif (arguments.length !== "+(a-2)+") {\nthrowBindingError('function "+n+" called with ' + arguments.length + ' arguments, expected "+(a-2)+" args!');\n}\n";u&&(p+="var destructors = [];\n");var d=u?"destructors":"null",h=["throwBindingError","invoker","fn","runDestructors","retType","classParam"],v=[ln,t,o,Z,r[0],r[1]];i&&(p+="var thisWired = classParam.toWireType("+d+", this);\n");for(c=0;c<a-2;++c)p+="var arg"+c+"Wired = argType"+c+".toWireType("+d+", arg"+c+"); // "+r[c+2].name+"\n",h.push("argType"+c),v.push(r[c+2]);if(i&&(l="thisWired"+(l.length>0?", ":"")+l),p+=(f?"var rv = ":"")+"invoker(fn"+(l.length>0?", ":"")+l+");\n",u)p+="runDestructors(destructors);\n";else for(c=i?1:2;c<r.length;++c){var y=1===c?"thisWired":"arg"+(c-2)+"Wired";null!==r[c].destructorFunction&&(p+=y+"_dtor("+y+"); // "+r[c].name+"\n",h.push(y+"_dtor"),v.push(r[c].destructorFunction))}return f&&(p+="var ret = retType.fromWireType(rv);\nreturn ret;\n"),p+="}\n",h.push(p),function(n,r){if(!(n instanceof Function))throw new TypeError("new_ called with constructor type "+typeof n+" which is not a function");var e=rn(n.name||"unknownFunctionName",(function(){}));e.prototype=n.prototype;var t=new e,o=n.apply(t,r);return o instanceof Object?o:t}(Function,h).apply(null,v)}function _n(n,r,t){e.hasOwnProperty(n)?((void 0===t||void 0!==e[n].overloadTable&&void 0!==e[n].overloadTable[t])&&ln("Cannot register public name '"+n+"' twice"),function(n,r,e){if(void 0===n[r].overloadTable){var t=n[r];n[r]=function(){return n[r].overloadTable.hasOwnProperty(arguments.length)||ln("Function '"+e+"' called with an invalid number of arguments ("+arguments.length+") - expects one of ("+n[r].overloadTable+")!"),n[r].overloadTable[arguments.length].apply(this,arguments)},n[r].overloadTable=[],n[r].overloadTable[t.argCount]=t}}(e,n,n),e.hasOwnProperty(t)&&ln("Cannot register multiple overloads of a function with the same number of arguments ("+t+")!"),e[n].overloadTable[t]=r):(e[n]=r,void 0!==t&&(e[n].numArguments=t))}function An(n,r,t){return n.includes("j")?function(n,r,t){var o=e["dynCall_"+n];return t&&t.length?o.apply(null,[r].concat(t)):o.call(null,r)}(n,r,t):P.get(r).apply(null,t)}function En(n,r){var e,t,o,a=(n=fn(n)).includes("j")?(e=n,t=r,o=[],function(){o.length=arguments.length;for(var n=0;n<arguments.length;n++)o[n]=arguments[n];return An(e,t,o)}):P.get(r);return"function"!=typeof a&&ln("unknown function pointer with signature "+n+": "+r),a}var Pn=void 0;function Cn(n){var r=Dn(n),e=fn(r);return jn(r),e}function Wn(n,r,e){switch(r){case 0:return e?function(n){return g[n]}:function(n){return m[n]};case 1:return e?function(n){return w[n>>1]}:function(n){return b[n>>1]};case 2:return e?function(n){return T[n>>2]}:function(n){return _[n>>2]};default:throw new TypeError("Unknown integer type: "+n)}}var Fn={};function Rn(){return"object"==typeof globalThis?globalThis:Function("return this")()}function kn(n,r){var e=X[n];return void 0===e&&ln(r+" has unknown type "+Cn(n)),e}var Un={};function Sn(n){try{return l.grow(n-y.byteLength+65535>>>16),I(l.buffer),1}catch(n){}}tn=e.InternalError=en(Error,"InternalError"),function(){for(var n=new Array(256),r=0;r<256;++r)n[r]=String.fromCharCode(r);cn=n}(),sn=e.BindingError=en(Error,"BindingError"),e.count_emval_handles=yn,e.get_first_emval=gn,Pn=e.UnboundTypeError=en(Error,"UnboundTypeError");var In={t:function(n,r){},m:function(n){var r=G[n];delete G[n];var e=r.rawConstructor,t=r.rawDestructor,o=r.fields;an([n],o.map((function(n){return n.getterReturnType})).concat(o.map((function(n){return n.setterArgumentType}))),(function(n){var a={};return o.forEach((function(r,e){var t=r.fieldName,i=n[e],u=r.getter,c=r.getterContext,f=n[e+o.length],s=r.setter,l=r.setterContext;a[t]={read:function(n){return i.fromWireType(u(c,n))},write:function(n,r){var e=[];s(l,n,f.toWireType(e,r)),Z(e)}}})),[{name:r.name,fromWireType:function(n){var r={};for(var e in a)r[e]=a[e].read(n);return t(n),r},toWireType:function(n,r){for(var o in a)if(!(o in r))throw new TypeError('Missing field:  "'+o+'"');var i=e();for(o in a)a[o].write(i,r[o]);return null!==n&&n.push(t,i),i},argPackAdvance:8,readValueFromPointer:Y,destructorFunction:t}]}))},p:function(n,r,e,t,o){},i:function(n,r,e,t,o){var a=un(e);pn(n,{name:r=fn(r),fromWireType:function(n){return!!n},toWireType:function(n,r){return r?t:o},argPackAdvance:8,readValueFromPointer:function(n){var t;if(1===e)t=g;else if(2===e)t=w;else{if(4!==e)throw new TypeError("Unknown boolean type size: "+r);t=T}return this.fromWireType(t[n>>a])},destructorFunction:null})},r:function(n,r){pn(n,{name:r=fn(r),fromWireType:function(n){var r=hn[n].value;return vn(n),r},toWireType:function(n,r){return mn(r)},argPackAdvance:8,readValueFromPointer:Y,destructorFunction:null})},g:function(n,r,e){var t=un(e);pn(n,{name:r=fn(r),fromWireType:function(n){return n},toWireType:function(n,r){if("number"!=typeof r&&"boolean"!=typeof r)throw new TypeError('Cannot convert "'+wn(r)+'" to '+this.name);return r},argPackAdvance:8,readValueFromPointer:bn(r,t),destructorFunction:null})},l:function(n,r,t,o,a,i){var u=function(n,r){for(var e=[],t=0;t<n;t++)e.push(T[(r>>2)+t]);return e}(r,t);n=fn(n),a=En(o,a),_n(n,(function(){!function(n,r){var e=[],t={};throw r.forEach((function n(r){t[r]||X[r]||($[r]?$[r].forEach(n):(e.push(r),t[r]=!0))})),new Pn(n+": "+e.map(Cn).join([", "]))}("Cannot call "+n+" due to unbound types",u)}),r-1),an([],u,(function(t){var o=[t[0],null].concat(t.slice(1));return function(n,r,t){e.hasOwnProperty(n)||on("Replacing nonexistant public symbol"),void 0!==e[n].overloadTable&&void 0!==t?e[n].overloadTable[t]=r:(e[n]=r,e[n].argCount=t)}(n,Tn(n,o,null,a,i),r-1),[]}))},b:function(n,r,e,t,o){r=fn(r),-1===o&&(o=4294967295);var a=un(e),i=function(n){return n};if(0===t){var u=32-8*e;i=function(n){return n<<u>>>u}}var c=r.includes("unsigned");pn(n,{name:r,fromWireType:i,toWireType:function(n,e){if("number"!=typeof e&&"boolean"!=typeof e)throw new TypeError('Cannot convert "'+wn(e)+'" to '+this.name);if(e<t||e>o)throw new TypeError('Passing a number "'+wn(e)+'" from JS side to C/C++ side to an argument of type "'+r+'", which is outside the valid range ['+t+", "+o+"]!");return c?e>>>0:0|e},argPackAdvance:8,readValueFromPointer:Wn(r,a,0!==t),destructorFunction:null})},a:function(n,r,e){var t=[Int8Array,Uint8Array,Int16Array,Uint16Array,Int32Array,Uint32Array,Float32Array,Float64Array][r];function o(n){var r=_,e=r[n>>=2],o=r[n+1];return new t(y,o,e)}pn(n,{name:e=fn(e),fromWireType:o,argPackAdvance:8,readValueFromPointer:o},{ignoreDuplicateRegistrations:!0})},h:function(n,r){var e="std::string"===(r=fn(r));pn(n,{name:r,fromWireType:function(n){var r,t=_[n>>2];if(e)for(var o=n+4,a=0;a<=t;++a){var i=n+4+a;if(a==t||0==m[i]){var u=v(o,i-o);void 0===r?r=u:(r+=String.fromCharCode(0),r+=u),o=i+1}}else{var c=new Array(t);for(a=0;a<t;++a)c[a]=String.fromCharCode(m[n+4+a]);r=c.join("")}return jn(n),r},toWireType:function(n,r){var t;r instanceof ArrayBuffer&&(r=new Uint8Array(r));var o="string"==typeof r;o||r instanceof Uint8Array||r instanceof Uint8ClampedArray||r instanceof Int8Array||ln("Cannot pass non-string to std::string"),t=e&&o?function(){return function(n){for(var r=0,e=0;e<n.length;++e){var t=n.charCodeAt(e);t>=55296&&t<=57343&&(t=65536+((1023&t)<<10)|1023&n.charCodeAt(++e)),t<=127?++r:r+=t<=2047?2:t<=65535?3:4}return r}(r)}:function(){return r.length};var a=t(),i=xn(4+a+1);if(_[i>>2]=a,e&&o)(function(n,r,e,t){if(!(t>0))return 0;for(var o=e,a=e+t-1,i=0;i<n.length;++i){var u=n.charCodeAt(i);if(u>=55296&&u<=57343&&(u=65536+((1023&u)<<10)|1023&n.charCodeAt(++i)),u<=127){if(e>=a)break;r[e++]=u}else if(u<=2047){if(e+1>=a)break;r[e++]=192|u>>6,r[e++]=128|63&u}else if(u<=65535){if(e+2>=a)break;r[e++]=224|u>>12,r[e++]=128|u>>6&63,r[e++]=128|63&u}else{if(e+3>=a)break;r[e++]=240|u>>18,r[e++]=128|u>>12&63,r[e++]=128|u>>6&63,r[e++]=128|63&u}}r[e]=0})(r,m,i+4,a+1);else if(o)for(var u=0;u<a;++u){var c=r.charCodeAt(u);c>255&&(jn(i),ln("String has UTF-16 code units that do not fit in 8 bits")),m[i+4+u]=c}else for(u=0;u<a;++u)m[i+4+u]=r[u];return null!==n&&n.push(jn,i),i},argPackAdvance:8,readValueFromPointer:Y,destructorFunction:function(n){jn(n)}})},e:function(n,r,e){var t,o,a,i,u;e=fn(e),2===r?(t=W,o=F,i=R,a=function(){return b},u=1):4===r&&(t=k,o=U,i=S,a=function(){return _},u=2),pn(n,{name:e,fromWireType:function(n){for(var e,o=_[n>>2],i=a(),c=n+4,f=0;f<=o;++f){var s=n+4+f*r;if(f==o||0==i[s>>u]){var l=t(c,s-c);void 0===e?e=l:(e+=String.fromCharCode(0),e+=l),c=s+r}}return jn(n),e},toWireType:function(n,t){"string"!=typeof t&&ln("Cannot pass non-string to C++ string type "+e);var a=i(t),c=xn(4+a+r);return _[c>>2]=a>>u,o(t,c+4,a+r),null!==n&&n.push(jn,c),c},argPackAdvance:8,readValueFromPointer:Y,destructorFunction:function(n){jn(n)}})},n:function(n,r,e,t,o,a){G[n]={name:fn(r),rawConstructor:En(e,t),rawDestructor:En(o,a),fields:[]}},j:function(n,r){pn(n,{isVoid:!0,name:r=fn(r),argPackAdvance:0,fromWireType:function(){},toWireType:function(n,r){}})},c:vn,s:function(n){return 0===n?mn(Rn()):(n=void 0===(e=Fn[r=n])?fn(r):e,mn(Rn()[n]));var r,e},k:function(n){n>4&&(hn[n].refcount+=1)},o:function(n,r,t,o){n=function(n){return n||ln("Cannot use deleted val. handle = "+n),hn[n].value}(n);var a=Un[r];return a||(a=function(n){for(var r="",t=0;t<n;++t)r+=(0!==t?", ":"")+"arg"+t;var o="return function emval_allocator_"+n+"(constructor, argTypes, args) {\n";for(t=0;t<n;++t)o+="var argType"+t+" = requireRegisteredType(Module['HEAP32'][(argTypes >>> 2) + "+t+'], "parameter '+t+'");\nvar arg'+t+" = argType"+t+".readValueFromPointer(args);\nargs += argType"+t+"['argPackAdvance'];\n";return o+="var obj = new constructor("+r+");\nreturn __emval_register(obj);\n}\n",new Function("requireRegisteredType","Module","__emval_register",o)(kn,e,mn)}(r),Un[r]=a),a(n,t,o)},f:function(){B()},q:function(n,r,e){m.copyWithin(n,r,r+e)},d:function(n){var r,e,t=m.length,o=2147483648;if((n>>>=0)>o)return!1;for(var a=1;a<=4;a*=2){var i=t*(1+.2/a);if(i=Math.min(i,n+100663296),Sn(Math.min(o,((r=Math.max(n,i))%(e=65536)>0&&(r+=e-r%e),r))))return!0}return!1}};!function(){var n={a:In};function r(n,r){var t,o=n.exports;e.asm=o,I((l=e.asm.u).buffer),P=e.asm.A,t=e.asm.v,x.unshift(t),function(){if(D--,e.monitorRunDependencies&&e.monitorRunDependencies(D),0==D&&M){var n=M;M=null,n()}}()}function t(n){r(n.instance)}function a(r){return(!s&&c&&"function"==typeof fetch?fetch(z,{credentials:"same-origin"}).then((function(n){if(!n.ok)throw"failed to load wasm binary file at '"+z+"'";return n.arrayBuffer()})).catch((function(){return N(z)})):Promise.resolve().then((function(){return N(z)}))).then((function(r){return WebAssembly.instantiate(r,n)})).then(r,(function(n){p("failed to asynchronously prepare wasm: "+n),B(n)}))}if(D++,e.monitorRunDependencies&&e.monitorRunDependencies(D),e.instantiateWasm)try{return e.instantiateWasm(n,r)}catch(n){return p("Module.instantiateWasm callback failed with error: "+n),!1}(s||"function"!=typeof WebAssembly.instantiateStreaming||V(z)||"function"!=typeof fetch?a(t):fetch(z,{credentials:"same-origin"}).then((function(r){return WebAssembly.instantiateStreaming(r,n).then(t,(function(n){return p("wasm streaming compile failed: "+n),p("falling back to ArrayBuffer instantiation"),a(t)}))}))).catch(o)}(),e.___wasm_call_ctors=function(){return(e.___wasm_call_ctors=e.asm.v).apply(null,arguments)};var On,xn=e._malloc=function(){return(xn=e._malloc=e.asm.w).apply(null,arguments)},jn=e._free=function(){return(jn=e._free=e.asm.x).apply(null,arguments)},Dn=e.___getTypeName=function(){return(Dn=e.___getTypeName=e.asm.y).apply(null,arguments)};function Mn(n){function r(){On||(On=!0,e.calledRun=!0,d||(L(x),t(e),e.onRuntimeInitialized&&e.onRuntimeInitialized(),function(){if(e.postRun)for("function"==typeof e.postRun&&(e.postRun=[e.postRun]);e.postRun.length;)n=e.postRun.shift(),j.unshift(n);var n;L(j)}()))}D>0||(!function(){if(e.preRun)for("function"==typeof e.preRun&&(e.preRun=[e.preRun]);e.preRun.length;)n=e.preRun.shift(),O.unshift(n);var n;L(O)}(),D>0||(e.setStatus?(e.setStatus("Running..."),setTimeout((function(){setTimeout((function(){e.setStatus("")}),1),r()}),1)):r()))}if(e.___embind_register_native_and_builtin_types=function(){return(e.___embind_register_native_and_builtin_types=e.asm.z).apply(null,arguments)},M=function n(){On||Mn(),On||(M=n)},e.run=Mn,e.preInit)for("function"==typeof e.preInit&&(e.preInit=[e.preInit]);e.preInit.length>0;)e.preInit.pop()();return Mn(),e.ready}}();let P,C,W;async function F(n,r){P||(P=async function(){return a(E)}());return(await P).encode(n.data,n.width,n.height,r).buffer}async function R(n,e){C||(C=async function(){if(await t.simd())return a((await r("./webp_enc_simd-ad66a6ff")).default);return a((await r("./webp_enc-de8661ab")).default)}());const o=(await C).encode(n.data,n.width,n.height,e);if(!o)throw new Error("Encoding error.");return o.buffer}async function k(n,e){W||(W=async function(){if(await t.checkThreadsSupport())return await t.simd()?a((await r("./wp2_enc_mt_simd-41fccdcf")).default):a((await r("./wp2_enc_mt-114443a2")).default);return a((await r("./wp2_enc-f854d67c")).default)}());const o=(await W).encode(n.data,n.width,n.height,e);if(!o)throw new Error("Encoding error.");return o.buffer}const U=fetch("/c/rotate-e8fb6784.wasm").then((n=>n.arrayBuffer())).then((n=>WebAssembly.instantiate(n)));var S=function(){var r=n.uri;return function(e){var t,o;(e=void 0!==(e=e||{})?e:{}).ready=new Promise((function(n,r){t=n,o=r}));var a,i={};for(a in e)e.hasOwnProperty(a)&&(i[a]=e[a]);var u,c=!0,f="";f=self.location.href,r&&(f=r),f=0!==f.indexOf("blob:")?f.substr(0,f.lastIndexOf("/")+1):"",u=function(n){var r=new XMLHttpRequest;return r.open("GET",n,!1),r.responseType="arraybuffer",r.send(null),new Uint8Array(r.response)};var s,l,p=e.print||console.log.bind(console),d=e.printErr||console.warn.bind(console);for(a in i)i.hasOwnProperty(a)&&(e[a]=i[a]);i=null,e.arguments&&e.arguments,e.thisProgram&&e.thisProgram,e.quit&&e.quit,e.wasmBinary&&(s=e.wasmBinary),e.noExitRuntime,"object"!=typeof WebAssembly&&H("no native wasm support detected");var h=!1,v=new TextDecoder("utf8");function y(n,r){if(!n)return"";for(var e=n+r,t=n;!(t>=e)&&w[t];)++t;return v.decode(w.subarray(n,t))}var g,m,w,b,T,_,A,E,P,C,W=new TextDecoder("utf-16le");function F(n,r){for(var e=n,t=e>>1,o=t+r/2;!(t>=o)&&T[t];)++t;return e=t<<1,W.decode(w.subarray(n,e))}function R(n,r,e){if(void 0===e&&(e=2147483647),e<2)return 0;for(var t=r,o=(e-=2)<2*n.length?e/2:n.length,a=0;a<o;++a){var i=n.charCodeAt(a);b[r>>1]=i,r+=2}return b[r>>1]=0,r-t}function k(n){return 2*n.length}function U(n,r){for(var e=0,t="";!(e>=r/4);){var o=_[n+4*e>>2];if(0==o)break;if(++e,o>=65536){var a=o-65536;t+=String.fromCharCode(55296|a>>10,56320|1023&a)}else t+=String.fromCharCode(o)}return t}function S(n,r,e){if(void 0===e&&(e=2147483647),e<4)return 0;for(var t=r,o=t+e-4,a=0;a<n.length;++a){var i=n.charCodeAt(a);if(i>=55296&&i<=57343)i=65536+((1023&i)<<10)|1023&n.charCodeAt(++a);if(_[r>>2]=i,(r+=4)+4>o)break}return _[r>>2]=0,r-t}function I(n){for(var r=0,e=0;e<n.length;++e){var t=n.charCodeAt(e);t>=55296&&t<=57343&&++e,r+=4}return r}function O(n){g=n,e.HEAP8=m=new Int8Array(n),e.HEAP16=b=new Int16Array(n),e.HEAP32=_=new Int32Array(n),e.HEAPU8=w=new Uint8Array(n),e.HEAPU16=T=new Uint16Array(n),e.HEAPU32=A=new Uint32Array(n),e.HEAPF32=E=new Float32Array(n),e.HEAPF64=P=new Float64Array(n)}e.INITIAL_MEMORY;var x=[],j=[],D=[];var M=0,B=null;function H(n){e.onAbort&&e.onAbort(n),d(n+=""),h=!0,n="abort("+n+"). Build with -s ASSERTIONS=1 for more info.";var r=new WebAssembly.RuntimeError(n);throw o(r),r}e.preloadedImages={},e.preloadedAudios={};var q,V="data:application/octet-stream;base64,";function z(n){return n.startsWith(V)}if(e.locateFile)z(N="imagequant.wasm")||(q=N,N=e.locateFile?e.locateFile(q,f):f+q);else var N=new URL("/c/imagequant-a10bbe1a.wasm",n.uri).toString();function L(n){try{if(n==N&&s)return new Uint8Array(s);if(u)return u(n);throw"both async and sync fetching of the wasm failed"}catch(n){H(n)}}function G(n){for(;n.length>0;){var r=n.shift();if("function"!=typeof r){var t=r.func;"number"==typeof t?void 0===r.arg?C.get(t)():C.get(t)(r.arg):t(void 0===r.arg?null:r.arg)}else r(e)}}function Z(n){switch(n){case 1:return 0;case 2:return 1;case 4:return 2;case 8:return 3;default:throw new TypeError("Unknown type size: "+n)}}var Y=void 0;function J(n){for(var r="",e=n;w[e];)r+=Y[w[e++]];return r}var X={},$={},K={},Q=48,nn=57;function rn(n){if(void 0===n)return"_unknown";var r=(n=n.replace(/[^a-zA-Z0-9_]/g,"$")).charCodeAt(0);return r>=Q&&r<=nn?"_"+n:n}function en(n,r){return n=rn(n),new Function("body","return function "+n+'() {\n    "use strict";    return body.apply(this, arguments);\n};\n')(r)}function tn(n,r){var e=en(r,(function(n){this.name=r,this.message=n;var e=new Error(n).stack;void 0!==e&&(this.stack=this.toString()+"\n"+e.replace(/^Error(:[^\n]*)?\n/,""))}));return e.prototype=Object.create(n.prototype),e.prototype.constructor=e,e.prototype.toString=function(){return void 0===this.message?this.name:this.name+": "+this.message},e}var on=void 0;function an(n){throw new on(n)}var un=void 0;function cn(n){throw new un(n)}function fn(n,r,e){if(e=e||{},!("argPackAdvance"in r))throw new TypeError("registerType registeredInstance requires argPackAdvance");var t=r.name;if(n||an('type "'+t+'" must have a positive integer typeid pointer'),$.hasOwnProperty(n)){if(e.ignoreDuplicateRegistrations)return;an("Cannot register type '"+t+"' twice")}if($[n]=r,delete K[n],X.hasOwnProperty(n)){var o=X[n];delete X[n],o.forEach((function(n){n()}))}}var sn=[],ln=[{},{value:void 0},{value:null},{value:!0},{value:!1}];function pn(n){n>4&&0==--ln[n].refcount&&(ln[n]=void 0,sn.push(n))}function dn(){for(var n=0,r=5;r<ln.length;++r)void 0!==ln[r]&&++n;return n}function hn(){for(var n=5;n<ln.length;++n)if(void 0!==ln[n])return ln[n];return null}function vn(n){switch(n){case void 0:return 1;case null:return 2;case!0:return 3;case!1:return 4;default:var r=sn.length?sn.pop():ln.length;return ln[r]={refcount:1,value:n},r}}function yn(n){return this.fromWireType(A[n>>2])}function gn(n){if(null===n)return"null";var r=typeof n;return"object"===r||"array"===r||"function"===r?n.toString():""+n}function mn(n,r){switch(r){case 2:return function(n){return this.fromWireType(E[n>>2])};case 3:return function(n){return this.fromWireType(P[n>>3])};default:throw new TypeError("Unknown float type: "+n)}}function wn(n){for(;n.length;){var r=n.pop();n.pop()(r)}}function bn(n,r,e,t,o){var a=r.length;a<2&&an("argTypes array size mismatch! Must at least get return value and 'this' types!");for(var i=null!==r[1]&&null!==e,u=!1,c=1;c<r.length;++c)if(null!==r[c]&&void 0===r[c].destructorFunction){u=!0;break}var f="void"!==r[0].name,s="",l="";for(c=0;c<a-2;++c)s+=(0!==c?", ":"")+"arg"+c,l+=(0!==c?", ":"")+"arg"+c+"Wired";var p="return function "+rn(n)+"("+s+") {\nif (arguments.length !== "+(a-2)+") {\nthrowBindingError('function "+n+" called with ' + arguments.length + ' arguments, expected "+(a-2)+" args!');\n}\n";u&&(p+="var destructors = [];\n");var d=u?"destructors":"null",h=["throwBindingError","invoker","fn","runDestructors","retType","classParam"],v=[an,t,o,wn,r[0],r[1]];i&&(p+="var thisWired = classParam.toWireType("+d+", this);\n");for(c=0;c<a-2;++c)p+="var arg"+c+"Wired = argType"+c+".toWireType("+d+", arg"+c+"); // "+r[c+2].name+"\n",h.push("argType"+c),v.push(r[c+2]);if(i&&(l="thisWired"+(l.length>0?", ":"")+l),p+=(f?"var rv = ":"")+"invoker(fn"+(l.length>0?", ":"")+l+");\n",u)p+="runDestructors(destructors);\n";else for(c=i?1:2;c<r.length;++c){var y=1===c?"thisWired":"arg"+(c-2)+"Wired";null!==r[c].destructorFunction&&(p+=y+"_dtor("+y+"); // "+r[c].name+"\n",h.push(y+"_dtor"),v.push(r[c].destructorFunction))}return f&&(p+="var ret = retType.fromWireType(rv);\nreturn ret;\n"),p+="}\n",h.push(p),function(n,r){if(!(n instanceof Function))throw new TypeError("new_ called with constructor type "+typeof n+" which is not a function");var e=en(n.name||"unknownFunctionName",(function(){}));e.prototype=n.prototype;var t=new e,o=n.apply(t,r);return o instanceof Object?o:t}(Function,h).apply(null,v)}function Tn(n,r,t){e.hasOwnProperty(n)?((void 0===t||void 0!==e[n].overloadTable&&void 0!==e[n].overloadTable[t])&&an("Cannot register public name '"+n+"' twice"),function(n,r,e){if(void 0===n[r].overloadTable){var t=n[r];n[r]=function(){return n[r].overloadTable.hasOwnProperty(arguments.length)||an("Function '"+e+"' called with an invalid number of arguments ("+arguments.length+") - expects one of ("+n[r].overloadTable+")!"),n[r].overloadTable[arguments.length].apply(this,arguments)},n[r].overloadTable=[],n[r].overloadTable[t.argCount]=t}}(e,n,n),e.hasOwnProperty(t)&&an("Cannot register multiple overloads of a function with the same number of arguments ("+t+")!"),e[n].overloadTable[t]=r):(e[n]=r,void 0!==t&&(e[n].numArguments=t))}function _n(n,r,t){return n.includes("j")?function(n,r,t){var o=e["dynCall_"+n];return t&&t.length?o.apply(null,[r].concat(t)):o.call(null,r)}(n,r,t):C.get(r).apply(null,t)}function An(n,r){var e,t,o,a=(n=J(n)).includes("j")?(e=n,t=r,o=[],function(){o.length=arguments.length;for(var n=0;n<arguments.length;n++)o[n]=arguments[n];return _n(e,t,o)}):C.get(r);return"function"!=typeof a&&an("unknown function pointer with signature "+n+": "+r),a}var En=void 0;function Pn(n){var r=Dn(n),e=J(r);return jn(r),e}function Cn(n,r,e){switch(r){case 0:return e?function(n){return m[n]}:function(n){return w[n]};case 1:return e?function(n){return b[n>>1]}:function(n){return T[n>>1]};case 2:return e?function(n){return _[n>>2]}:function(n){return A[n>>2]};default:throw new TypeError("Unknown integer type: "+n)}}var Wn={};function Fn(){return"object"==typeof globalThis?globalThis:Function("return this")()}function Rn(n,r){var e=$[n];return void 0===e&&an(r+" has unknown type "+Pn(n)),e}var kn={};function Un(n){try{return l.grow(n-g.byteLength+65535>>>16),O(l.buffer),1}catch(n){}}var Sn={mappings:{},buffers:[null,[],[]],printChar:function(n,r){var e=Sn.buffers[n];0===r||10===r?((1===n?p:d)(function(n,r,e){for(var t=r+e,o=r;n[o]&&!(o>=t);)++o;return v.decode(n.subarray?n.subarray(r,o):new Uint8Array(n.slice(r,o)))}(e,0)),e.length=0):e.push(r)},varargs:void 0,get:function(){return Sn.varargs+=4,_[Sn.varargs-4>>2]},getStr:function(n){return y(n)},get64:function(n,r){return n}};!function(){for(var n=new Array(256),r=0;r<256;++r)n[r]=String.fromCharCode(r);Y=n}(),on=e.BindingError=tn(Error,"BindingError"),un=e.InternalError=tn(Error,"InternalError"),e.count_emval_handles=dn,e.get_first_emval=hn,En=e.UnboundTypeError=tn(Error,"UnboundTypeError");var In={m:function(n,r){},q:function(n,r,e,t,o){},n:function(n,r,e,t,o){var a=Z(e);fn(n,{name:r=J(r),fromWireType:function(n){return!!n},toWireType:function(n,r){return r?t:o},argPackAdvance:8,readValueFromPointer:function(n){var t;if(1===e)t=m;else if(2===e)t=b;else{if(4!==e)throw new TypeError("Unknown boolean type size: "+r);t=_}return this.fromWireType(t[n>>a])},destructorFunction:null})},v:function(n,r){fn(n,{name:r=J(r),fromWireType:function(n){var r=ln[n].value;return pn(n),r},toWireType:function(n,r){return vn(r)},argPackAdvance:8,readValueFromPointer:yn,destructorFunction:null})},l:function(n,r,e){var t=Z(e);fn(n,{name:r=J(r),fromWireType:function(n){return n},toWireType:function(n,r){if("number"!=typeof r&&"boolean"!=typeof r)throw new TypeError('Cannot convert "'+gn(r)+'" to '+this.name);return r},argPackAdvance:8,readValueFromPointer:mn(r,t),destructorFunction:null})},c:function(n,r,t,o,a,i){var u=function(n,r){for(var e=[],t=0;t<n;t++)e.push(_[(r>>2)+t]);return e}(r,t);n=J(n),a=An(o,a),Tn(n,(function(){!function(n,r){var e=[],t={};throw r.forEach((function n(r){t[r]||$[r]||(K[r]?K[r].forEach(n):(e.push(r),t[r]=!0))})),new En(n+": "+e.map(Pn).join([", "]))}("Cannot call "+n+" due to unbound types",u)}),r-1),function(n,r,e){function t(r){var t=e(r);t.length!==n.length&&cn("Mismatched type converter count");for(var o=0;o<n.length;++o)fn(n[o],t[o])}n.forEach((function(n){K[n]=r}));var o=new Array(r.length),a=[],i=0;r.forEach((function(n,r){$.hasOwnProperty(n)?o[r]=$[n]:(a.push(n),X.hasOwnProperty(n)||(X[n]=[]),X[n].push((function(){o[r]=$[n],++i===a.length&&t(o)})))})),0===a.length&&t(o)}([],u,(function(t){var o=[t[0],null].concat(t.slice(1));return function(n,r,t){e.hasOwnProperty(n)||cn("Replacing nonexistant public symbol"),void 0!==e[n].overloadTable&&void 0!==t?e[n].overloadTable[t]=r:(e[n]=r,e[n].argCount=t)}(n,bn(n,o,null,a,i),r-1),[]}))},b:function(n,r,e,t,o){r=J(r),-1===o&&(o=4294967295);var a=Z(e),i=function(n){return n};if(0===t){var u=32-8*e;i=function(n){return n<<u>>>u}}var c=r.includes("unsigned");fn(n,{name:r,fromWireType:i,toWireType:function(n,e){if("number"!=typeof e&&"boolean"!=typeof e)throw new TypeError('Cannot convert "'+gn(e)+'" to '+this.name);if(e<t||e>o)throw new TypeError('Passing a number "'+gn(e)+'" from JS side to C/C++ side to an argument of type "'+r+'", which is outside the valid range ['+t+", "+o+"]!");return c?e>>>0:0|e},argPackAdvance:8,readValueFromPointer:Cn(r,a,0!==t),destructorFunction:null})},a:function(n,r,e){var t=[Int8Array,Uint8Array,Int16Array,Uint16Array,Int32Array,Uint32Array,Float32Array,Float64Array][r];function o(n){var r=A,e=r[n>>=2],o=r[n+1];return new t(g,o,e)}fn(n,{name:e=J(e),fromWireType:o,argPackAdvance:8,readValueFromPointer:o},{ignoreDuplicateRegistrations:!0})},h:function(n,r){var e="std::string"===(r=J(r));fn(n,{name:r,fromWireType:function(n){var r,t=A[n>>2];if(e)for(var o=n+4,a=0;a<=t;++a){var i=n+4+a;if(a==t||0==w[i]){var u=y(o,i-o);void 0===r?r=u:(r+=String.fromCharCode(0),r+=u),o=i+1}}else{var c=new Array(t);for(a=0;a<t;++a)c[a]=String.fromCharCode(w[n+4+a]);r=c.join("")}return jn(n),r},toWireType:function(n,r){var t;r instanceof ArrayBuffer&&(r=new Uint8Array(r));var o="string"==typeof r;o||r instanceof Uint8Array||r instanceof Uint8ClampedArray||r instanceof Int8Array||an("Cannot pass non-string to std::string"),t=e&&o?function(){return function(n){for(var r=0,e=0;e<n.length;++e){var t=n.charCodeAt(e);t>=55296&&t<=57343&&(t=65536+((1023&t)<<10)|1023&n.charCodeAt(++e)),t<=127?++r:r+=t<=2047?2:t<=65535?3:4}return r}(r)}:function(){return r.length};var a=t(),i=xn(4+a+1);if(A[i>>2]=a,e&&o)(function(n,r,e,t){if(!(t>0))return 0;for(var o=e,a=e+t-1,i=0;i<n.length;++i){var u=n.charCodeAt(i);if(u>=55296&&u<=57343&&(u=65536+((1023&u)<<10)|1023&n.charCodeAt(++i)),u<=127){if(e>=a)break;r[e++]=u}else if(u<=2047){if(e+1>=a)break;r[e++]=192|u>>6,r[e++]=128|63&u}else if(u<=65535){if(e+2>=a)break;r[e++]=224|u>>12,r[e++]=128|u>>6&63,r[e++]=128|63&u}else{if(e+3>=a)break;r[e++]=240|u>>18,r[e++]=128|u>>12&63,r[e++]=128|u>>6&63,r[e++]=128|63&u}}r[e]=0})(r,w,i+4,a+1);else if(o)for(var u=0;u<a;++u){var c=r.charCodeAt(u);c>255&&(jn(i),an("String has UTF-16 code units that do not fit in 8 bits")),w[i+4+u]=c}else for(u=0;u<a;++u)w[i+4+u]=r[u];return null!==n&&n.push(jn,i),i},argPackAdvance:8,readValueFromPointer:yn,destructorFunction:function(n){jn(n)}})},f:function(n,r,e){var t,o,a,i,u;e=J(e),2===r?(t=F,o=R,i=k,a=function(){return T},u=1):4===r&&(t=U,o=S,i=I,a=function(){return A},u=2),fn(n,{name:e,fromWireType:function(n){for(var e,o=A[n>>2],i=a(),c=n+4,f=0;f<=o;++f){var s=n+4+f*r;if(f==o||0==i[s>>u]){var l=t(c,s-c);void 0===e?e=l:(e+=String.fromCharCode(0),e+=l),c=s+r}}return jn(n),e},toWireType:function(n,t){"string"!=typeof t&&an("Cannot pass non-string to C++ string type "+e);var a=i(t),c=xn(4+a+r);return A[c>>2]=a>>u,o(t,c+4,a+r),null!==n&&n.push(jn,c),c},argPackAdvance:8,readValueFromPointer:yn,destructorFunction:function(n){jn(n)}})},o:function(n,r){fn(n,{isVoid:!0,name:r=J(r),argPackAdvance:0,fromWireType:function(){},toWireType:function(n,r){}})},d:pn,k:function(n){return 0===n?vn(Fn()):(n=void 0===(e=Wn[r=n])?J(r):e,vn(Fn()[n]));var r,e},i:function(n){n>4&&(ln[n].refcount+=1)},j:function(n,r,t,o){n=function(n){return n||an("Cannot use deleted val. handle = "+n),ln[n].value}(n);var a=kn[r];return a||(a=function(n){for(var r="",t=0;t<n;++t)r+=(0!==t?", ":"")+"arg"+t;var o="return function emval_allocator_"+n+"(constructor, argTypes, args) {\n";for(t=0;t<n;++t)o+="var argType"+t+" = requireRegisteredType(Module['HEAP32'][(argTypes >>> 2) + "+t+'], "parameter '+t+'");\nvar arg'+t+" = argType"+t+".readValueFromPointer(args);\nargs += argType"+t+"['argPackAdvance'];\n";return o+="var obj = new constructor("+r+");\nreturn __emval_register(obj);\n}\n",new Function("requireRegisteredType","Module","__emval_register",o)(Rn,e,vn)}(r),kn[r]=a),a(n,t,o)},g:function(){H()},s:function(n,r,e){w.copyWithin(n,r,r+e)},e:function(n){var r,e,t=w.length,o=2147483648;if((n>>>=0)>o)return!1;for(var a=1;a<=4;a*=2){var i=t*(1+.2/a);if(i=Math.min(i,n+100663296),Un(Math.min(o,((r=Math.max(n,i))%(e=65536)>0&&(r+=e-r%e),r))))return!0}return!1},u:function(n){return 0},p:function(n,r,e,t,o){},t:function(n,r,e,t){for(var o=0,a=0;a<e;a++){for(var i=_[r+8*a>>2],u=_[r+(8*a+4)>>2],c=0;c<u;c++)Sn.printChar(n,w[i+c]);o+=u}return _[t>>2]=o,0},r:function(n){}};!function(){var n={a:In};function r(n,r){var t,o=n.exports;e.asm=o,O((l=e.asm.w).buffer),C=e.asm.C,t=e.asm.x,j.unshift(t),function(){if(M--,e.monitorRunDependencies&&e.monitorRunDependencies(M),0==M&&B){var n=B;B=null,n()}}()}function t(n){r(n.instance)}function a(r){return(!s&&c&&"function"==typeof fetch?fetch(N,{credentials:"same-origin"}).then((function(n){if(!n.ok)throw"failed to load wasm binary file at '"+N+"'";return n.arrayBuffer()})).catch((function(){return L(N)})):Promise.resolve().then((function(){return L(N)}))).then((function(r){return WebAssembly.instantiate(r,n)})).then(r,(function(n){d("failed to asynchronously prepare wasm: "+n),H(n)}))}if(M++,e.monitorRunDependencies&&e.monitorRunDependencies(M),e.instantiateWasm)try{return e.instantiateWasm(n,r)}catch(n){return d("Module.instantiateWasm callback failed with error: "+n),!1}(s||"function"!=typeof WebAssembly.instantiateStreaming||z(N)||"function"!=typeof fetch?a(t):fetch(N,{credentials:"same-origin"}).then((function(r){return WebAssembly.instantiateStreaming(r,n).then(t,(function(n){return d("wasm streaming compile failed: "+n),d("falling back to ArrayBuffer instantiation"),a(t)}))}))).catch(o)}(),e.___wasm_call_ctors=function(){return(e.___wasm_call_ctors=e.asm.x).apply(null,arguments)};var On,xn=e._malloc=function(){return(xn=e._malloc=e.asm.y).apply(null,arguments)},jn=e._free=function(){return(jn=e._free=e.asm.z).apply(null,arguments)},Dn=e.___getTypeName=function(){return(Dn=e.___getTypeName=e.asm.A).apply(null,arguments)};function Mn(n){function r(){On||(On=!0,e.calledRun=!0,h||(G(j),t(e),e.onRuntimeInitialized&&e.onRuntimeInitialized(),function(){if(e.postRun)for("function"==typeof e.postRun&&(e.postRun=[e.postRun]);e.postRun.length;)n=e.postRun.shift(),D.unshift(n);var n;G(D)}()))}M>0||(!function(){if(e.preRun)for("function"==typeof e.preRun&&(e.preRun=[e.preRun]);e.preRun.length;)n=e.preRun.shift(),x.unshift(n);var n;G(x)}(),M>0||(e.setStatus?(e.setStatus("Running..."),setTimeout((function(){setTimeout((function(){e.setStatus("")}),1),r()}),1)):r()))}if(e.___embind_register_native_and_builtin_types=function(){return(e.___embind_register_native_and_builtin_types=e.asm.B).apply(null,arguments)},e.dynCall_jiji=function(){return(e.dynCall_jiji=e.asm.D).apply(null,arguments)},B=function n(){On||Mn(),On||(B=n)},e.run=Mn,e.preInit)for("function"==typeof e.preInit&&(e.preInit=[e.preInit]);e.preInit.length>0;)e.preInit.pop()();return Mn(),e.ready}}();let I,O;let x=null;let j=0;function D(n,r){const e=r(1*n.length);return(null!==x&&x.buffer===O.memory.buffer||(x=new Uint8Array(O.memory.buffer)),x).set(n,e/1),j=n.length,e}let M=null;function B(){return null!==M&&M.buffer===O.memory.buffer||(M=new Int32Array(O.memory.buffer)),M}let H,q=null;function V(n,r){return(null!==q&&q.buffer===O.memory.buffer||(q=new Uint8ClampedArray(O.memory.buffer)),q).subarray(n/1,n/1+r)}async function z(r){void 0===r&&(r=new URL("/c/squoosh_resize_bg-3d426466.wasm",n.uri));("string"==typeof r||"function"==typeof Request&&r instanceof Request||"function"==typeof URL&&r instanceof URL)&&(r=fetch(r));const{instance:e,module:t}=await async function(n,r){if("function"==typeof Response&&n instanceof Response){if("function"==typeof WebAssembly.instantiateStreaming)try{return await WebAssembly.instantiateStreaming(n,r)}catch(r){if("application/wasm"==n.headers.get("Content-Type"))throw r;console.warn("`WebAssembly.instantiateStreaming` failed because your server does not serve wasm with `application/wasm` MIME type. Falling back to `WebAssembly.instantiate` which is slower. Original error:\n",r)}const e=await n.arrayBuffer();return await WebAssembly.instantiate(e,r)}{const e=await WebAssembly.instantiate(n,r);return e instanceof WebAssembly.Instance?{instance:e,module:n}:e}}(await r,{});return O=e.exports,z.__wbindgen_wasm_module=t,O}let N=null;function L(){return null!==N&&N.buffer===H.memory.buffer||(N=new Uint32Array(H.memory.buffer)),N}let G=0;let Z=null;function Y(){return null!==Z&&Z.buffer===H.memory.buffer||(Z=new Int32Array(H.memory.buffer)),Z}function J(n,r,e,t){try{const l=H.__wbindgen_add_to_stack_pointer(-16);var o=function(n,r){const e=r(4*n.length);return L().set(n,e/4),G=n.length,e}(n,H.__wbindgen_malloc),a=G;H.resize(l,o,a,r,e,t);var i=Y()[l/4+0],u=Y()[l/4+1],c=(f=i,s=u,L().subarray(f/4,f/4+s)).slice();return H.__wbindgen_free(i,4*u),c}finally{H.__wbindgen_add_to_stack_pointer(16)}var f,s}async function X(r){void 0===r&&(r=new URL("/c/compressflowhqx_bg-6e04a330.wasm",n.uri));("string"==typeof r||"function"==typeof Request&&r instanceof Request||"function"==typeof URL&&r instanceof URL)&&(r=fetch(r));const{instance:e,module:t}=await async function(n,r){if("function"==typeof Response&&n instanceof Response){if("function"==typeof WebAssembly.instantiateStreaming)try{return await WebAssembly.instantiateStreaming(n,r)}catch(r){if("application/wasm"==n.headers.get("Content-Type"))throw r;console.warn("`WebAssembly.instantiateStreaming` failed because your server does not serve wasm with `application/wasm` MIME type. Falling back to `WebAssembly.instantiate` which is slower. Original error:\n",r)}const e=await n.arrayBuffer();return await WebAssembly.instantiate(e,r)}{const e=await WebAssembly.instantiate(n,r);return e instanceof WebAssembly.Instance?{instance:e,module:n}:e}}(await r,{});return H=e.exports,X.__wbindgen_wasm_module=t,H}const $=["triangle","catrom","mitchell","lanczos3"];let K,Q;async function nn(n,r){Q||(Q=X()),await Q;const e=r.width/n.width,t=r.height/n.height,o=Math.max(e,t),a=function(n,{min:r=Number.MIN_VALUE,max:e=Number.MAX_VALUE}){return Math.min(Math.max(n,r),e)}(Math.ceil(o),{min:1,max:4});if(1===a)return n;const i=J(new Uint32Array(n.data.buffer),n.width,n.height,a);return new ImageData(new Uint8ClampedArray(i.buffer),n.width*a,n.height*a)}async function rn(n,r){let t=n;if(K||(K=z()),function(n){return"hqx"===n.method}(r)&&(t=await nn(t,r),r={...r,method:"catrom"}),await K,"contain"===r.fitMethod){const{sx:o,sy:a,sw:i,sh:u}=e.getContainOffsets(n.width,n.height,r.width,r.height);t=function(n,r,e,t,o){const a=new Uint32Array(n.data.buffer);for(let i=0;i<o;i+=1){const o=(i+e)*n.width+r;a.copyWithin(i*t,o,o+t)}return new ImageData(new Uint8ClampedArray(a.buffer.slice(0,t*o*4)),t,o)}(t,Math.round(o),Math.round(a),Math.round(i),Math.round(u))}const o=function(n,r,e,t,o,a,i,u){try{const d=O.__wbindgen_add_to_stack_pointer(-16);var c=D(n,O.__wbindgen_malloc),f=j;O.resize(d,c,f,r,e,t,o,a,i,u);var s=B()[d/4+0],l=B()[d/4+1],p=V(s,l).slice();return O.__wbindgen_free(s,1*l),p}finally{O.__wbindgen_add_to_stack_pointer(16)}}(new Uint8Array(t.data.buffer),t.width,t.height,r.width,r.height,$.indexOf(r.method),r.premultiply,r.linearRGB);return new ImageData(new Uint8ClampedArray(o.buffer),r.width,r.height)}const en={avifDecode:(...n)=>o("avifDecode",(()=>async function(n){if(!u){const n=await r("./avif_dec-61445e6d");u=a(n.default)}const[e,t]=await Promise.all([u,i(n)]),o=e.decode(t);if(!o)throw new Error("Decoding error");return o}(...n))),jxlDecode:(...n)=>o("jxlDecode",(()=>async function(n){s||(s=a(f));const[r,e]=await Promise.all([s,i(n)]),t=r.decode(e);if(!t)throw new Error("Decoding error");return t}(...n))),qoiDecode:(...n)=>o("qoiDecode",(()=>async function(n){p||(p=a(l));const[r,e]=await Promise.all([p,i(n)]),t=r.decode(e);if(!t)throw new Error("Decoding error");return t}(...n))),webpDecode:(...n)=>o("webpDecode",(()=>async function(n){if(!d){const n=await r("./webp_dec-318dc2b4");d=a(n.default)}const[e,t]=await Promise.all([d,i(n)]),o=e.decode(t);if(!o)throw new Error("Decoding error");return o}(...n))),wp2Decode:(...n)=>o("wp2Decode",(()=>async function(n){v||(v=a(h));const[r,e]=await Promise.all([v,i(n)]),t=r.decode(e);if(!t)throw new Error("Decoding error");return t}(...n))),avifEncode:(...n)=>o("avifEncode",(()=>m(...n))),jxlEncode:(...n)=>o("jxlEncode",(()=>w(...n))),mozjpegEncode:(...n)=>o("mozjpegEncode",(()=>async function(n,r){return T||(T=a(b)),(await T).encode(n.data,n.width,n.height,r).buffer}(...n))),oxipngEncode:(...n)=>o("oxipngEncode",(()=>A(...n))),qoiEncode:(...n)=>o("qoiEncode",(()=>F(...n))),webpEncode:(...n)=>o("webpEncode",(()=>R(...n))),wp2Encode:(...n)=>o("wp2Encode",(()=>k(...n))),rotate:(...n)=>o("rotate",(()=>async function(n,r){const e=(await U).instance,t=n.width*n.height*4,o=Math.ceil((2*t+8)/65536)-Math.floor(e.exports.memory.buffer.byteLength/65536);o>0&&e.exports.memory.grow(o);const a=new Uint8ClampedArray(e.exports.memory.buffer);a.set(n.data,8),e.exports.rotate(n.width,n.height,r.rotate);const i=r.rotate%180!=0;return new ImageData(a.slice(t+8,2*t+8),i?n.height:n.width,i?n.width:n.height)}(...n))),quantize:(...n)=>o("quantize",(()=>async function(n,r){I||(I=a(S));const e=await I,t=r.zx?e.zx_quantize(n.data,n.width,n.height,r.dither):e.quantize(n.data,n.width,n.height,r.maxNumColors,r.dither);return new ImageData(t,n.width,n.height)}(...n))),resize:(...n)=>o("resize",(()=>rn(...n)))};e.expose(en,self)}));
