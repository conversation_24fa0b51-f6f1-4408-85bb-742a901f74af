define(["module","require","exports"],(function(e,n,t){let i,a=new TextDecoder("utf-8",{ignoreBOM:!0,fatal:!0});a.decode();let r=null;function o(){return null!==r&&r.buffer===i.memory.buffer||(r=new Uint8Array(i.memory.buffer)),r}let s=0;let f=null;function u(){return null!==f&&f.buffer===i.memory.buffer||(f=new Int32Array(i.memory.buffer)),f}t.default=async function n(t){void 0===t&&(t=new URL("/c/squoosh_oxipng_bg-60d7d0b0.wasm",e.uri));const r={wbg:{}};r.wbg.__wbindgen_throw=function(e,n){throw new Error((t=e,i=n,a.decode(o().subarray(t,t+i))));var t,i},("string"==typeof t||"function"==typeof Request&&t instanceof Request||"function"==typeof URL&&t instanceof URL)&&(t=fetch(t));const{instance:s,module:f}=await async function(e,n){if("function"==typeof Response&&e instanceof Response){if("function"==typeof WebAssembly.instantiateStreaming)try{return await WebAssembly.instantiateStreaming(e,n)}catch(n){if("application/wasm"==e.headers.get("Content-Type"))throw n;console.warn("`WebAssembly.instantiateStreaming` failed because your server does not serve wasm with `application/wasm` MIME type. Falling back to `WebAssembly.instantiate` which is slower. Original error:\n",n)}const t=await e.arrayBuffer();return await WebAssembly.instantiate(t,n)}{const t=await WebAssembly.instantiate(e,n);return t instanceof WebAssembly.Instance?{instance:t,module:e}:t}}(await t,r);return i=s.exports,n.__wbindgen_wasm_module=f,i},t.optimise=function(e,n,t,a,r){try{const d=i.__wbindgen_add_to_stack_pointer(-16);var f=function(e,n){const t=n(1*e.length);return o().set(e,t/1),s=e.length,t}(e,i.__wbindgen_malloc),c=s;i.optimise(d,f,c,n,t,a,r);var l=u()[d/4+0],b=u()[d/4+1],w=(m=l,y=b,o().subarray(m/1,m/1+y)).slice();return i.__wbindgen_free(l,1*b),w}finally{i.__wbindgen_add_to_stack_pointer(16)}var m,y}}));
