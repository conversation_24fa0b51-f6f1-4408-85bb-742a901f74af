{"private": true, "name": "compressflow", "version": "1.0.0", "description": "Lightning-fast bulk image compression with cyberpunk neon UI - Enhanced with multiple export capabilities", "license": "apache-2.0", "homepage": "https://compressflow.netlify.app", "repository": {"type": "git", "url": "https://github.com/mvalencia464/compressflow.git"}, "keywords": ["image-compression", "bulk-compression", "webassembly", "batch-processing", "webp", "avif", "jpeg", "png", "compressflow"], "scripts": {"build": "rollup -c && node lib/move-output.js", "debug": "node --inspect-brk node_modules/.bin/rollup -c", "dev": "DEV_PORT=\"${DEV_PORT:=5000}\" run-p watch serve", "watch": "rollup -cw", "serve": "serve --listen=$DEV_PORT --config ../../../serve.json .tmp/build/static", "serve:prod": "serve --listen=8080 --config serve.json .tmp/build/static", "clean": "rm -rf .tmp node_modules/.cache", "lint": "prettier --check \"src/**/*.{js,ts,tsx}\"", "lint:fix": "prettier --write \"src/**/*.{js,ts,tsx}\"", "prepare": "husky install", "preview": "npm run build && npm run serve:prod"}, "devDependencies": {"@rollup/plugin-commonjs": "^17.0.0", "@rollup/plugin-node-resolve": "^11.1.0", "@rollup/plugin-replace": "^2.3.4", "@rollup/plugin-terser": "^0.4.4", "@surma/rollup-plugin-off-main-thread": "^2.2.2", "@types/dedent": "^0.7.0", "@types/mime-types": "^2.1.1", "@types/node": "^16.11.1", "@web/rollup-plugin-import-meta-assets": "^1.0.6", "comlink": "^4.3.0", "cssnano": "^4.1.10", "dedent": "^0.7.0", "del": "^5.1.0", "file-drop-element": "^1.0.1", "husky": "^7.0.2", "idb-keyval": "^3.2.0", "image-size": "^0.9.3", "linkstate": "^2.0.0", "lint-staged": "^10.5.1", "lodash.camelcase": "^4.3.0", "mime-types": "^2.1.28", "npm-run-all": "^4.1.5", "pointer-tracker": "^2.5.3", "postcss": "^7.0.35", "postcss-modules": "^3.2.2", "postcss-nested": "^4.2.3", "postcss-simple-vars": "^5.0.2", "postcss-url": "^8.0.0", "preact": "^10.5.5", "preact-render-to-string": "^5.1.11", "prettier": "^2.4.1", "rollup": "^2.38.0", "serve": "^11.3.2", "typescript": "^4.4.4", "wasm-feature-detect": "^1.2.11", "which": "^2.0.2"}, "lint-staged": {"*.{js,css,json,md,ts,tsx}": "prettier --write", "*.{c,h,cpp,hpp}": "clang-format -i", "*.rs": "rustfmt"}}