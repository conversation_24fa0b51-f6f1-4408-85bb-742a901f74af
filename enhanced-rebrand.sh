#!/bin/bash
echo "🔥 Starting CompressFlow Enhanced Rebranding..."

# Replace text in all relevant files
echo "📝 Updating text references..."
find . -type f \( -name "*.js" -o -name "*.ts" -o -name "*.jsx" -o -name "*.tsx" -o -name "*.json" -o -name "*.html" -o -name "*.md" -o -name "*.css" \) \
  -not -path "./node_modules/*" \
  -not -path "./.git/*" \
  -not -path "./dist/*" \
  -exec sed -i.bak 's/Squoosh/CompressFlow/g' {} \; 2>/dev/null

find . -type f \( -name "*.js" -o -name "*.ts" -o -name "*.jsx" -o -name "*.tsx" -o -name "*.json" -o -name "*.html" -o -name "*.md" -o -name "*.css" \) \
  -not -path "./node_modules/*" \
  -not -path "./.git/*" \
  -not -path "./dist/*" \
  -exec sed -i.bak 's/squoosh/compressflow/g' {} \; 2>/dev/null

# Clean up backup files
find . -name "*.bak" -delete

echo "✅ Text replacement complete!"

# Update package.json with enhanced description
cat > package.json << 'PKG_EOF'
{
  "name": "compressflow",
  "version": "1.0.0",
  "description": "Lightning-fast bulk image compression with cyberpunk neon UI - Enhanced with multiple export capabilities",
  "homepage": "https://compressflow.netlify.app",
  "repository": {
    "type": "git",
    "url": "https://github.com/mvalencia464/compressflow.git"
  },
  "keywords": [
    "image-compression",
    "bulk-compression",
    "webassembly",
    "pwa",
    "cyberpunk",
    "neon-ui"
  ],
  "scripts": {
    "dev": "vite dev --host",
    "build": "vite build",
    "preview": "vite preview",
    "serve": "vite preview --host"
  },
  "dependencies": {
    "@types/node": "^18.0.0",
    "react": "^18.2.0",
    "react-dom": "^18.2.0",
    "typescript": "^4.9.0"
  },
  "devDependencies": {
    "@vitejs/plugin-react": "^4.0.0",
    "vite": "^4.4.0",
    "vite-plugin-pwa": "^0.16.4"
  }
}
PKG_EOF

echo "✅ package.json updated with enhanced features!"

# Create comprehensive NOTICE file
cat > NOTICE << 'NOTICE_EOF'
CompressFlow - Enhanced Bulk Image Compression Tool
Copyright 2025 Mauricio Valencia

This product includes software developed by:

1. Google Chrome Labs - Original Squoosh Project
   https://github.com/GoogleChromeLabs/squoosh
   
2. Khongchai - Multiple Export Enhancement
   https://github.com/Khongchai/squoosh-multiple-export

All components are licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

THIRD-PARTY ACKNOWLEDGMENTS:
- WebAssembly codecs from various open-source projects
- UI components and compression algorithms from Squoosh project
- Bulk export functionality enhancements
NOTICE_EOF

echo "✅ Enhanced NOTICE file created!"

# Create updated README highlighting bulk features
cat > README.md << 'README_EOF'
# 🔥 CompressFlow - Enhanced Bulk Compression

Lightning-fast **bulk image compression** with cyberpunk neon UI and advanced export capabilities.

![CompressFlow Logo](https://page1.genspark.site/v1/base64_upload/2271c90f5513b3d57e744142ec978e2d)

## ⚡ Enhanced Features

### 🚀 **Bulk Processing**
- **Multiple file upload** - Drag & drop entire folders
- **Batch compression** - Process dozens of images simultaneously  
- **Bulk export** - Download all compressed images as ZIP
- **Progress tracking** - Real-time compression status

### 🎮 **Cyberpunk Experience**
- **Neon-powered UI** - Electric green & pink accents
- **Dark theme** - Easy on the eyes during long sessions
- **Smooth animations** - Futuristic interaction feedback
- **Gaming-inspired** - RGB lighting effects

### 🔒 **Privacy & Performance**
- **Client-side only** - No server uploads, complete privacy
- **WebAssembly powered** - Desktop-class compression speed
- **PWA enabled** - Install and use offline
- **Multiple formats** - JPEG, PNG, WebP, AVIF support

## 🚀 **Live Demo**
**[compressflow.netlify.app](https://compressflow.netlify.app)**

## 🛠️ **Quick Start**
```bash
npm install
npm run dev
```

## 📊 **Compression Stats**
- **Up to 90% size reduction** for photos
- **Lossless PNG optimization** 
- **WebP/AVIF modern formats**
- **Batch processing** up to 100 files

## 🎯 **Use Cases**
- **Web developers** - Optimize assets for faster loading
- **Photographers** - Reduce portfolio file sizes
- **Content creators** - Prepare images for social media
- **E-commerce** - Optimize product images

## 🔧 **Advanced Features**
- **Custom quality settings** per format
- **Resize during compression**
- **Format conversion** (PNG → WebP, etc.)
- **Metadata preservation** options

---

**Built with ❤️ by the CompressFlow team**
README_EOF

echo "✅ Enhanced README created!"

echo "🎉 CompressFlow Enhanced Rebranding Complete!"
echo "🚀 Ready to deploy your cyberpunk bulk compression tool!"
