import { h, Component } from 'preact';
import { linkRef } from 'shared/prerendered-app/util';
import * as style from './style.css';
import 'add-css:./style.css';
import 'file-drop-element';
import 'shared/custom-els/snack-bar';
import Intro from 'shared/prerendered-app/Intro';
import 'shared/custom-els/loading-spinner';
const ROUTE_EDITOR = '/editor';
const compressPromise = import('client/lazy-app/Compress');
const swBridgePromise = import('client/lazy-app/sw-bridge');
function back() {
    window.history.back();
}
export default class App extends Component {
    constructor() {
        super();
        this.state = {
            awaitingShareTarget: new URL(location.href).searchParams.has('share-target'),
            isEditorOpen: false,
            files: [],
            Compress: undefined,
        };
        this.onFileDrop = ({ files }) => {
            if (!files || files.length === 0)
                return;
            this.openEditor();
            this.setState({ files });
        };
        this.onIntroPickFile = (files) => {
            this.openEditor();
            this.setState({ files });
        };
        this.showSnack = (message, options = {}) => {
            if (!this.snackbar)
                throw Error('Snackbar missing');
            return this.snackbar.showSnackbar(message, options);
        };
        this.onPopState = () => {
            this.setState({ isEditorOpen: location.pathname === ROUTE_EDITOR });
        };
        this.openEditor = () => {
            if (this.state.isEditorOpen)
                return;
            // Change path, but preserve query string.
            const editorURL = new URL(location.href);
            editorURL.pathname = ROUTE_EDITOR;
            history.pushState(null, '', editorURL.href);
            this.setState({ isEditorOpen: true });
        };
        compressPromise
            .then((module) => {
            this.setState({ Compress: module.default });
        })
            .catch(() => {
            this.showSnack('Failed to load app');
        });
        swBridgePromise.then(async ({ offliner, getSharedImage }) => {
            offliner(this.showSnack);
            if (!this.state.awaitingShareTarget)
                return;
            const file = await getSharedImage();
            // Remove the ?share-target from the URL
            history.replaceState('', '', '/');
            this.openEditor();
            this.setState({ files: [file], awaitingShareTarget: false });
        });
        // Since iOS 10, Apple tries to prevent disabling pinch-zoom. This is great in theory, but
        // really breaks things on CompressFlow, as you can easily end up zooming the UI when you mean to
        // zoom the image. Once you've done this, it's really difficult to undo. Anyway, this seems to
        // prevent it.
        document.body.addEventListener('gesturestart', (event) => {
            event.preventDefault();
        });
        window.addEventListener('popstate', this.onPopState);
    }
    render({}, { files, isEditorOpen, Compress, awaitingShareTarget }) {
        const showSpinner = awaitingShareTarget || (isEditorOpen && !Compress);
        return (h("div", { class: style.app },
            h("file-drop", { onfiledrop: this.onFileDrop, class: style.drop, multiple: true },
                showSpinner ? (h("loading-spinner", { class: style.appLoader })) : isEditorOpen ? (Compress && (h(Compress, { files: files, showSnack: this.showSnack, onBack: back }))) : (h(Intro, { onFiles: this.onIntroPickFile, showSnack: this.showSnack })),
                h("snack-bar", { ref: linkRef(this, 'snackbar') }))));
    }
}
