{"program": {"fileNames": ["../../node_modules/typescript/lib/lib.es5.d.ts", "../../node_modules/typescript/lib/lib.es2015.d.ts", "../../node_modules/typescript/lib/lib.es2016.d.ts", "../../node_modules/typescript/lib/lib.es2017.d.ts", "../../node_modules/typescript/lib/lib.es2018.d.ts", "../../node_modules/typescript/lib/lib.es2019.d.ts", "../../node_modules/typescript/lib/lib.es2020.d.ts", "../../node_modules/typescript/lib/lib.es2021.d.ts", "../../node_modules/typescript/lib/lib.esnext.d.ts", "../../node_modules/typescript/lib/lib.webworker.d.ts", "../../node_modules/typescript/lib/lib.es2015.core.d.ts", "../../node_modules/typescript/lib/lib.es2015.collection.d.ts", "../../node_modules/typescript/lib/lib.es2015.generator.d.ts", "../../node_modules/typescript/lib/lib.es2015.iterable.d.ts", "../../node_modules/typescript/lib/lib.es2015.promise.d.ts", "../../node_modules/typescript/lib/lib.es2015.proxy.d.ts", "../../node_modules/typescript/lib/lib.es2015.reflect.d.ts", "../../node_modules/typescript/lib/lib.es2015.symbol.d.ts", "../../node_modules/typescript/lib/lib.es2015.symbol.wellknown.d.ts", "../../node_modules/typescript/lib/lib.es2016.array.include.d.ts", "../../node_modules/typescript/lib/lib.es2017.object.d.ts", "../../node_modules/typescript/lib/lib.es2017.sharedmemory.d.ts", "../../node_modules/typescript/lib/lib.es2017.string.d.ts", "../../node_modules/typescript/lib/lib.es2017.intl.d.ts", "../../node_modules/typescript/lib/lib.es2017.typedarrays.d.ts", "../../node_modules/typescript/lib/lib.es2018.asyncgenerator.d.ts", "../../node_modules/typescript/lib/lib.es2018.asynciterable.d.ts", "../../node_modules/typescript/lib/lib.es2018.intl.d.ts", "../../node_modules/typescript/lib/lib.es2018.promise.d.ts", "../../node_modules/typescript/lib/lib.es2018.regexp.d.ts", "../../node_modules/typescript/lib/lib.es2019.array.d.ts", "../../node_modules/typescript/lib/lib.es2019.object.d.ts", "../../node_modules/typescript/lib/lib.es2019.string.d.ts", "../../node_modules/typescript/lib/lib.es2019.symbol.d.ts", "../../node_modules/typescript/lib/lib.es2020.bigint.d.ts", "../../node_modules/typescript/lib/lib.es2020.promise.d.ts", "../../node_modules/typescript/lib/lib.es2020.sharedmemory.d.ts", "../../node_modules/typescript/lib/lib.es2020.string.d.ts", "../../node_modules/typescript/lib/lib.es2020.symbol.wellknown.d.ts", "../../node_modules/typescript/lib/lib.es2020.intl.d.ts", "../../node_modules/typescript/lib/lib.es2021.promise.d.ts", "../../node_modules/typescript/lib/lib.es2021.string.d.ts", "../../node_modules/typescript/lib/lib.es2021.weakref.d.ts", "../../node_modules/typescript/lib/lib.esnext.intl.d.ts", "../../codecs/avif/dec/avif_dec.d.ts", "../../src/features/worker-utils/index.ts", "../../src/features/decoders/avif/worker/avifdecode.ts", "../../codecs/jxl/dec/jxl_dec.d.ts", "../../src/features/decoders/jxl/worker/jxldecode.ts", "../../codecs/qoi/dec/qoi_dec.d.ts", "../../src/features/decoders/qoi/worker/qoidecode.ts", "../../codecs/webp/dec/webp_dec.d.ts", "../../src/features/decoders/webp/worker/webpdecode.ts", "../../codecs/wp2/dec/wp2_dec.d.ts", "../../src/features/decoders/wp2/worker/wp2decode.ts", "../../codecs/avif/enc/avif_enc.d.ts", "../../src/features/encoders/avif/shared/meta.ts", "../../node_modules/wasm-feature-detect/dist/index.d.ts", "../../src/worker-shared/supports-wasm-threads.ts", "../../codecs/avif/enc/avif_enc_mt.d.ts", "../../src/features/encoders/avif/worker/avifencode.ts", "../../emscripten-types.d.ts", "../../missing-types.d.ts", "../../src/features/encoders/avif/worker/missing-types.d.ts", "../../codecs/jxl/enc/jxl_enc.d.ts", "../../src/features/encoders/jxl/shared/meta.ts", "../../codecs/jxl/enc/jxl_enc_mt_simd.d.ts", "../../codecs/jxl/enc/jxl_enc_mt.d.ts", "../../src/features/encoders/jxl/worker/jxlencode.ts", "../../src/features/encoders/mozjpeg/worker/missing-types.d.ts", "../../codecs/mozjpeg/enc/mozjpeg_enc.d.ts", "../../src/features/encoders/mozjpeg/shared/meta.ts", "../../src/features/encoders/mozjpeg/worker/mozjpegencode.ts", "../../src/features/encoders/oxipng/shared/meta.ts", "../../codecs/oxipng/pkg-parallel/squoosh_oxipng.d.ts", "../../codecs/oxipng/pkg/squoosh_oxipng.d.ts", "../../src/features/encoders/oxipng/worker/oxipngencode.ts", "../../src/features/encoders/qoi/worker/missing-types.d.ts", "../../codecs/qoi/enc/qoi_enc.d.ts", "../../src/features/encoders/qoi/shared/meta.ts", "../../src/features/encoders/qoi/worker/qoiencode.ts", "../../codecs/webp/enc/webp_enc.d.ts", "../../src/features/encoders/webp/shared/meta.ts", "../../codecs/webp/enc/webp_enc_simd.d.ts", "../../src/features/encoders/webp/worker/webpencode.ts", "../../codecs/wp2/enc/wp2_enc.d.ts", "../../src/features/encoders/wp2/shared/meta.ts", "../../codecs/wp2/enc/wp2_enc_mt_simd.d.ts", "../../codecs/wp2/enc/wp2_enc_mt.d.ts", "../../src/features/encoders/wp2/worker/wp2encode.ts", "../../src/features/preprocessors/rotate/worker/missing-types.d.ts", "../../src/features/preprocessors/rotate/shared/meta.ts", "../../src/features/preprocessors/rotate/worker/rotate.ts", "../../src/features/processors/quantize/worker/missing-types.d.ts", "../../codecs/imagequant/imagequant.d.ts", "../../src/features/processors/quantize/shared/meta.ts", "../../src/features/processors/quantize/worker/quantize.ts", "../../src/features/processors/resize/worker/missing-types.d.ts", "../../src/features/processors/resize/shared/meta.ts", "../../src/features/processors/resize/shared/util.ts", "../../src/features/processors/resize/worker/resize.ts", "../../src/features/encoders/avif/shared/missing-types.d.ts", "../../src/features/encoders/browsergif/shared/meta.ts", "../../src/features/encoders/browsergif/shared/missing-types.d.ts", "../../src/features/encoders/browserjpeg/shared/meta.ts", "../../src/features/encoders/browserjpeg/shared/missing-types.d.ts", "../../src/features/encoders/browserpng/shared/meta.ts", "../../src/features/encoders/browserpng/shared/missing-types.d.ts", "../../src/features/encoders/mozjpeg/shared/missing-types.d.ts", "../../src/features/encoders/qoi/shared/missing-types.d.ts", "../../src/features/preprocessors/rotate/shared/missing-types.d.ts", "../../src/features/processors/quantize/shared/missing-types.d.ts", "../../src/features/processors/resize/shared/missing-types.d.ts", "../../node_modules/comlink/dist/umd/protocol.d.ts", "../../node_modules/comlink/dist/umd/comlink.d.ts", "../../src/features-worker/util.ts", "../../src/features-worker/index.ts", "../../src/features-worker/missing-types.d.ts", "../../src/sw/to-cache.ts", "../../src/sw/util.ts", "../../node_modules/idb-keyval/dist/idb-keyval.d.ts", "../../src/sw/index.ts", "../../src/sw/missing-types.d.ts", "../../node_modules/@types/color-name/index.d.ts", "../../node_modules/@types/dedent/index.d.ts", "../../node_modules/@types/estree/index.d.ts", "../../node_modules/@types/node/assert.d.ts", "../../node_modules/@types/node/assert/strict.d.ts", "../../node_modules/@types/node/globals.d.ts", "../../node_modules/@types/node/async_hooks.d.ts", "../../node_modules/@types/node/buffer.d.ts", "../../node_modules/@types/node/child_process.d.ts", "../../node_modules/@types/node/cluster.d.ts", "../../node_modules/@types/node/console.d.ts", "../../node_modules/@types/node/constants.d.ts", "../../node_modules/@types/node/crypto.d.ts", "../../node_modules/@types/node/dgram.d.ts", "../../node_modules/@types/node/diagnostics_channel.d.ts", "../../node_modules/@types/node/dns.d.ts", "../../node_modules/@types/node/dns/promises.d.ts", "../../node_modules/@types/node/domain.d.ts", "../../node_modules/@types/node/events.d.ts", "../../node_modules/@types/node/fs.d.ts", "../../node_modules/@types/node/fs/promises.d.ts", "../../node_modules/@types/node/http.d.ts", "../../node_modules/@types/node/http2.d.ts", "../../node_modules/@types/node/https.d.ts", "../../node_modules/@types/node/inspector.d.ts", "../../node_modules/@types/node/module.d.ts", "../../node_modules/@types/node/net.d.ts", "../../node_modules/@types/node/os.d.ts", "../../node_modules/@types/node/path.d.ts", "../../node_modules/@types/node/perf_hooks.d.ts", "../../node_modules/@types/node/process.d.ts", "../../node_modules/@types/node/punycode.d.ts", "../../node_modules/@types/node/querystring.d.ts", "../../node_modules/@types/node/readline.d.ts", "../../node_modules/@types/node/repl.d.ts", "../../node_modules/@types/node/stream.d.ts", "../../node_modules/@types/node/stream/promises.d.ts", "../../node_modules/@types/node/stream/consumers.d.ts", "../../node_modules/@types/node/stream/web.d.ts", "../../node_modules/@types/node/string_decoder.d.ts", "../../node_modules/@types/node/timers.d.ts", "../../node_modules/@types/node/timers/promises.d.ts", "../../node_modules/@types/node/tls.d.ts", "../../node_modules/@types/node/trace_events.d.ts", "../../node_modules/@types/node/tty.d.ts", "../../node_modules/@types/node/url.d.ts", "../../node_modules/@types/node/util.d.ts", "../../node_modules/@types/node/v8.d.ts", "../../node_modules/@types/node/vm.d.ts", "../../node_modules/@types/node/wasi.d.ts", "../../node_modules/@types/node/worker_threads.d.ts", "../../node_modules/@types/node/zlib.d.ts", "../../node_modules/@types/node/globals.global.d.ts", "../../node_modules/@types/node/index.d.ts", "../../node_modules/@types/minimatch/index.d.ts", "../../node_modules/@types/glob/index.d.ts", "../../node_modules/@types/mime-types/index.d.ts", "../../node_modules/@types/parse-json/index.d.ts", "../../node_modules/@types/q/index.d.ts", "../../node_modules/@types/resolve/index.d.ts"], "fileInfos": [{"version": "aa9fb4c70f369237c2f45f9d969c9a59e0eae9a192962eb48581fe864aa609db", "affectsGlobalScope": true}, "dc47c4fa66b9b9890cf076304de2a9c5201e94b740cffdf09f87296d877d71f6", "7a387c58583dfca701b6c85e0adaf43fb17d590fb16d5b2dc0a2fbd89f35c467", "8a12173c586e95f4433e0c6dc446bc88346be73ffe9ca6eec7aa63c8f3dca7f9", "5f4e733ced4e129482ae2186aae29fde948ab7182844c3a5a51dd346182c7b06", "e6b724280c694a9f588847f754198fb96c43d805f065c3a5b28bbc9594541c84", "e21c071ca3e1b4a815d5f04a7475adcaeea5d64367e840dd0154096d705c3940", "eb75e89d63b3b72dd9ca8b0cac801cecae5be352307c004adeaa60bc9d6df51f", "2cc028cd0bdb35b1b5eb723d84666a255933fffbea607f72cbd0c7c7b4bee144", {"version": "28ab3a152fefb456d9ca44de158f3239ca0d03c1a4db6d645c51b837f1f9ee02", "affectsGlobalScope": true}, {"version": "51b8b27c21c066bf877646e320bf6a722b80d1ade65e686923cd9d4494aef1ca", "affectsGlobalScope": true}, {"version": "43fb1d932e4966a39a41b464a12a81899d9ae5f2c829063f5571b6b87e6d2f9c", "affectsGlobalScope": true}, {"version": "cdccba9a388c2ee3fd6ad4018c640a471a6c060e96f1232062223063b0a5ac6a", "affectsGlobalScope": true}, {"version": "2c8c5ee58f30e7c944e04ab1fb5506fdbb4dd507c9efa6972cf4b91cec90c503", "affectsGlobalScope": true}, {"version": "2bb4b3927299434052b37851a47bf5c39764f2ba88a888a107b32262e9292b7c", "affectsGlobalScope": true}, {"version": "810627a82ac06fb5166da5ada4159c4ec11978dfbb0805fe804c86406dab8357", "affectsGlobalScope": true}, {"version": "62d80405c46c3f4c527ee657ae9d43fda65a0bf582292429aea1e69144a522a6", "affectsGlobalScope": true}, {"version": "3013574108c36fd3aaca79764002b3717da09725a36a6fc02eac386593110f93", "affectsGlobalScope": true}, {"version": "75ec0bdd727d887f1b79ed6619412ea72ba3c81d92d0787ccb64bab18d261f14", "affectsGlobalScope": true}, {"version": "3be5a1453daa63e031d266bf342f3943603873d890ab8b9ada95e22389389006", "affectsGlobalScope": true}, {"version": "17bb1fc99591b00515502d264fa55dc8370c45c5298f4a5c2083557dccba5a2a", "affectsGlobalScope": true}, {"version": "7ce9f0bde3307ca1f944119f6365f2d776d281a393b576a18a2f2893a2d75c98", "affectsGlobalScope": true}, {"version": "6a6b173e739a6a99629a8594bfb294cc7329bfb7b227f12e1f7c11bc163b8577", "affectsGlobalScope": true}, {"version": "12a310447c5d23c7d0d5ca2af606e3bd08afda69100166730ab92c62999ebb9d", "affectsGlobalScope": true}, {"version": "b0124885ef82641903d232172577f2ceb5d3e60aed4da1153bab4221e1f6dd4e", "affectsGlobalScope": true}, {"version": "0eb85d6c590b0d577919a79e0084fa1744c1beba6fd0d4e951432fa1ede5510a", "affectsGlobalScope": true}, {"version": "da233fc1c8a377ba9e0bed690a73c290d843c2c3d23a7bd7ec5cd3d7d73ba1e0", "affectsGlobalScope": true}, {"version": "df9c8a72ca8b0ed62f5470b41208a0587f0f73f0a7db28e5a1272cf92537518e", "affectsGlobalScope": true}, {"version": "bb2d3fb05a1d2ffbca947cc7cbc95d23e1d053d6595391bd325deb265a18d36c", "affectsGlobalScope": true}, {"version": "c80df75850fea5caa2afe43b9949338ce4e2de086f91713e9af1a06f973872b8", "affectsGlobalScope": true}, {"version": "9d57b2b5d15838ed094aa9ff1299eecef40b190722eb619bac4616657a05f951", "affectsGlobalScope": true}, {"version": "6c51b5dd26a2c31dbf37f00cfc32b2aa6a92e19c995aefb5b97a3a64f1ac99de", "affectsGlobalScope": true}, {"version": "93544ca2f26a48716c1b6c5091842cad63129daac422dfa4bc52460465f22bb1", "affectsGlobalScope": true}, {"version": "2ad234885a4240522efccd77de6c7d99eecf9b4de0914adb9a35c0c22433f993", "affectsGlobalScope": true}, {"version": "1b3fe904465430e030c93239a348f05e1be80640d91f2f004c3512c2c2c89f34", "affectsGlobalScope": true}, {"version": "7435b75fdf3509622e79622dbe5091cf4b09688410ee2034e4fc17d0c99d0862", "affectsGlobalScope": true}, {"version": "e7e8e1d368290e9295ef18ca23f405cf40d5456fa9f20db6373a61ca45f75f40", "affectsGlobalScope": true}, {"version": "faf0221ae0465363c842ce6aa8a0cbda5d9296940a8e26c86e04cc4081eea21e", "affectsGlobalScope": true}, {"version": "06393d13ea207a1bfe08ec8d7be562549c5e2da8983f2ee074e00002629d1871", "affectsGlobalScope": true}, {"version": "9f1817f7c3f02f6d56e0f403b927e90bb133f371dcebc36fa7d6d208ef6899da", "affectsGlobalScope": true}, {"version": "cd6efb9467a8b6338ece2e2855e37765700f2cd061ca54b01b33878cf5c7677e", "affectsGlobalScope": true}, {"version": "fb4416144c1bf0323ccbc9afb0ab289c07312214e8820ad17d709498c865a3fe", "affectsGlobalScope": true}, {"version": "5b0ca94ec819d68d33da516306c15297acec88efeb0ae9e2b39f71dbd9685ef7", "affectsGlobalScope": true}, {"version": "4632665b87204bb1caa8b44d165bce0c50dfab177df5b561b345a567cabacf9a", "affectsGlobalScope": true}, "2c4ddcb1f32c4816799752684666370abb2db568f0b117079fef27d43c59c290", "92826ef817c52e9b8b1d1d3ba6c0b39e44436117103e6cfadf1d60ebec787c68", "0d88ec8a3566500825b9e9a37e637acc605358d16f2012395d55104e6707f0dd", "5846165d5b980a54e57088ab145cd2e181febb91aab6e2351c770747d94ee6e8", "26bf55e5b9c048844617936b2d3a8c8562e48c3f32432fc66f3d461847d4bdd2", "783e43d6741426a637e9e4f67661a886ddf8cbe02c77c0e43d941dda03b1984f", "1b55147c49b04f4ab46b73b29086982cb60153009de68fd2b8dec47bf035c61a", "43771959601d9a0a45e4f68d5f730c2ad0a09441da0a99cb3b40668368880663", "3960bd370c703429317b9866ed65eb5ba99830e79a13b16fe9bdfdc49b4f71e1", "ea31eebf5aa0e2383a5527e76731ae63dff9adf2f9a2cd0d956cf4867888f8a5", "2d4772501e3645ad6d0e06768f17e4938130ff53830de414ca610a8caff9f482", "42986b572db45fde178fac5370ab7d05892c584289e8f642a9879ac249a042ff", "aa7f5649ed3b3376fe939fce4fbc3f2e55e916dbeb76db6e0e34e5e6bc9c50a7", "8df209e603e7f2b0edaa278884f6855006ad080d154378b574d0ecd93bee8b2b", {"version": "fb5c5a645947c00571c52c9b82493439e13aba655da922e03aeac81ac6fc84b7", "signature": "191f870a8addd4975f5415c6da801f4659e36db1edb57e5ff9355cf6278f6499"}, "9cda2654f286944eeaff6025b64fe034e242038ffdc77a7a2c509048d6543f8c", {"version": "12e50e76bf4a63e84d3442debe91c23d70494a2f03491c6616f5ae7478dfc37e", "signature": "50208e53c8b71a8512acd8b6d03b780480f9af95f77184b5bdcbabdface25516"}, {"version": "a07c805d64dadabd27f5ca77a5902621484b97719296db7141441f331174a281", "affectsGlobalScope": true}, {"version": "66c6f51eefd942dd715f57f3ad2d94cd88619a81d1947691d4c82c5b134a93c1", "affectsGlobalScope": true}, "5dbd3d79f7cf23ad5ac0fe54ba95bda45123acb5748165617b7e8b7c6a577ff6", "47a7923f1fb9c7c9c5b2beacfe0348779c419620ac92e23c2e3a14814fa428b2", "336959ee06cb77c85ccbe07e6fc7a88241e3c95e49a9c5b26fee7dd56759f30d", "f2b55da3f8e7fe419de7a2d431c8ebc288ab0502dadbd6ada44ff7247a844560", "f2b55da3f8e7fe419de7a2d431c8ebc288ab0502dadbd6ada44ff7247a844560", {"version": "75c6bfbe2b095f18c46a8eb867a4368c99bcab18112bdba37b156a17d33f1852", "signature": "50208e53c8b71a8512acd8b6d03b780480f9af95f77184b5bdcbabdface25516"}, "5dbd3d79f7cf23ad5ac0fe54ba95bda45123acb5748165617b7e8b7c6a577ff6", "d87b1d5872f90669bf97f8b83a47cb77b3501efdd0ddc1d3bf6c20ab724cebeb", "4d8d807f62b42d6542c08bd9745883f8f669ff5d36ecc20545a3c240dc325916", "164ec0e511b94e35d76d1446e6e2550d267810d41e160a6cf71939e15f402d8d", "8a2ec9ca62b8b427a03af61690af526b6e00e44d90266218b8914382b8f1cb0f", "b4318a4a1e89a391296ac74f9c8e69943321c3161b0f61c789dbe88570589e95", "0a007a4b7252115549f6846a2b4ee4837921d7b1042bc340381ce7f1922bdcfd", {"version": "42b30ffe9232a9d2a170d6e49b319883c28a64ee0be0683b8184d735fdf25999", "signature": "c4219dab88d3d0b2e9e27e519f7abb778d1641e09e8d2c64ea5f96f8ec15694e"}, "5dbd3d79f7cf23ad5ac0fe54ba95bda45123acb5748165617b7e8b7c6a577ff6", "f1dbddaffb8f28204cde75ed3dccd7ac32f5146796ef7156e24cf7c5908a4e5b", "fbf3e1d595dab4581c74652f95f306f26891d3bd4521f72da32b399ab99b2f62", "42d7ac9f82ae760f045e74ee48b1ff5040f2eb465350dc9bb4c870dd26f8ffb0", "ca06b07b4a41f7e53e57f5bea598600cb962e275ca76686a78c3f61d8a5cd458", "7f12252b51ea21595e4fb66b8aadeff43b18b25ae91e817a2a80a2e01f1a1079", "8fcd1a5abfa1db0de15a39581c23c712d9f904eec2bf22b241d9e98f489e6e23", "b84c55fcbb307383113a032502df8187f9653fe854ec3d5dea32f59912cb4d4b", "2f1a945218f01334529eeadfab8130dfd6f5c0385855b94a4e89ee1b021e3c8e", "accfc892c743c13a711d63d179cd7f55af7a5ac2ec6c1267aee4eb5d210993a5", "2b2f95e39846a7c671251952a9f279ac6f01bec08f2e40416e2cedd9c90bd948", "2b2f95e39846a7c671251952a9f279ac6f01bec08f2e40416e2cedd9c90bd948", {"version": "f9d374933b81a5f35f4d33bcd10fc8763c04407b75c1f9804664f42b219175e1", "signature": "50208e53c8b71a8512acd8b6d03b780480f9af95f77184b5bdcbabdface25516"}, "5dbd3d79f7cf23ad5ac0fe54ba95bda45123acb5748165617b7e8b7c6a577ff6", "c24482f3296090673a117885cde31734206ea5ed7e874fd2e92fa13b29992e15", "79e105155d9dbb7f00a900556cdc21294eefb4615123c37ad58daa5a1055c77e", "5dbd3d79f7cf23ad5ac0fe54ba95bda45123acb5748165617b7e8b7c6a577ff6", "479f54235e950206ff27b7e90fa1624beb2122b15862af5a8cc8d750f2728970", "2fa543d06903cc2cda33aeb707f01d5722d72c7cca0c5debff52697de2e78910", "7b667aeedc79e3f9248d2f9b6919c920e60647e8d9b1fa524623e127580a69e7", "5dbd3d79f7cf23ad5ac0fe54ba95bda45123acb5748165617b7e8b7c6a577ff6", "e1f9cc24d778514e35aeb618675dc655d9d080719b18a4fc8dd2538a52ce227b", "01925ff6e0092b676fb28c5d04a4dffca0638408b26f76e188f608eb12757016", {"version": "27e917c4ab160f22a197d257c57ec49376c4b4b24fb7013a9cf1ee8adbdc73af", "signature": "80894840221f8a08b57503cca771f3c09d2db71c00c59c912b89e673e924d3ef"}, "5dbd3d79f7cf23ad5ac0fe54ba95bda45123acb5748165617b7e8b7c6a577ff6", "2e9fd59c7feb0df4f8867452acb2fd69c91a46651e5ab673fbeef3e0249b3f88", "5dbd3d79f7cf23ad5ac0fe54ba95bda45123acb5748165617b7e8b7c6a577ff6", "38864eb5babcf5af6a3a78961b2ba8c3de1de4dd945a2387f82cf4f1ce0d9121", "5dbd3d79f7cf23ad5ac0fe54ba95bda45123acb5748165617b7e8b7c6a577ff6", "a3f1f6b7af8cef0f66611a6179510d4144a856df95c78e7d2a11d60f7a9463f0", "5dbd3d79f7cf23ad5ac0fe54ba95bda45123acb5748165617b7e8b7c6a577ff6", "5dbd3d79f7cf23ad5ac0fe54ba95bda45123acb5748165617b7e8b7c6a577ff6", "5dbd3d79f7cf23ad5ac0fe54ba95bda45123acb5748165617b7e8b7c6a577ff6", "5dbd3d79f7cf23ad5ac0fe54ba95bda45123acb5748165617b7e8b7c6a577ff6", "5dbd3d79f7cf23ad5ac0fe54ba95bda45123acb5748165617b7e8b7c6a577ff6", "5dbd3d79f7cf23ad5ac0fe54ba95bda45123acb5748165617b7e8b7c6a577ff6", "8b51bf0307af4de46a981eaaf8ff99b76fdd424b4b185c92bb6ce149ba7430ec", "e68b7e9cb314ef99931b20576350fea7c6405bdb77ea80fece3d4987514c5e84", "2f2d630e763ffbd978172be99cd589a3bcf2ff55880427e074990193e16960e2", {"version": "ac712647bf9d6f8a631b87660f975ef7e454ec357845d782c225de9c714b0648", "signature": "36ebab42ba1a7aec04e7b4e7faa5120c65c23b5e0f4da7bccc2dcdf03531ee43"}, "0b11b1d0a091c2f401d103ed950743000a6e8eccd6efd185b891d8e75101ee42", {"version": "b07833c6afe88a8f9e179bd3da9a952cf404d9ec512a9d12ee7c1dd02a54fbfa", "signature": "810f4679507968866115e27c5b648a19873ebb13ecf155c9b94a758861f87b03"}, {"version": "5246125add2e40459cd7c0814591d21327e4b9d6c8227129462fbb2a593ba60b", "signature": "61e5c07d055acf2ed299884714b8134b993fd0b4908cfaf003b9915c03b348f1"}, "f658b846801c8e5d94e6667aba3780267571b38d94c4ac3671c490fadcecf54d", {"version": "d667ba11d4165bc164ba93717e6dddbbd87e1c300e7c9705bc8cb37fed22af3b", "signature": "8e609bb71c20b858c77f0e9f90bb1319db8477b13f9f965f1a1e18524bf50881"}, {"version": "03fffd5e237da8fefca935d1d9e977aa9d3bd9742fc6f572ec6ae3cba1693125", "affectsGlobalScope": true}, "f0cb4b3ab88193e3e51e9e2622e4c375955003f1f81239d72c5b7a95415dad3e", "70646d9cb37b62611766d18a9bcca6cbf28ca9aec33a66244e974056ab1193f6", "89ccbe04e737ce613f5f04990271cfa84901446350b8551b0555ddf19319723b", "0d5a2ee1fdfa82740e0103389b9efd6bfe145a20018a2da3c02b89666181f4d9", "a69c09dbea52352f479d3e7ac949fde3d17b195abe90b045d619f747b38d6d1a", {"version": "92d63add669d18ebc349efbacd88966d6f2ccdddfb1b880b2db98ae3aa7bf7c4", "affectsGlobalScope": true}, "ccc94049a9841fe47abe5baef6be9a38fc6228807974ae675fb15dc22531b4be", {"version": "9acfe4d1ff027015151ce81d60797b04b52bffe97ad8310bb0ec2e8fd61e1303", "affectsGlobalScope": true}, "43978f18d1165eea81040bc9bfac1a551717f5cc9bd0f13b31bf490c5fcdc75f", "afc6e96061af46bcff47246158caee7e056f5288783f2d83d6858cd25be1c565", {"version": "34f5bcac12b36d70304b73de5f5aab3bb91bd9919f984be80579ebcad03a624e", "affectsGlobalScope": true}, "82408ed3e959ddc60d3e9904481b5a8dc16469928257af22a3f7d1a3bc7fd8c4", "2f520601649a893e6a49a8851ebfcf4be8ce090dc1281c2a08a871cb04e8251f", "f50c975ab7b50e25a69e3d8a3773894125b44e9698924105f23b812bf7488baf", "2b8c764f856a1dd0a9a2bf23e5efddbff157de8138b0754010be561ae5fcaa90", "ad4b60488fb1e562bf375dac9299815f7028bf667d9b5887b2d01d501b7d1ddd", "246341c3a7a2638cf830d690e69de1e6085a102c6a30596435b050e6ac86c11a", "6972fca26f6e9bd56197568d4379f99071a90766e06b4fcb5920a0130a9202be", {"version": "4a2628e95962c8ab756121faa3ac2ed348112ff7a87b5c286dd2cc3326546b4c", "affectsGlobalScope": true}, "aa8fe22e10f78a67b2ffbcc614b45fe258ff9e71d53ddb56e75fa7883c530270", "a049a59a02009fc023684fcfaf0ac526fe36c35dcc5d2b7d620c1750ba11b083", "fa9859478a480a6dfe625d204d5a3a38810b60933ac2b36ba77126cace1d44d3", "b287b810b5035d5685f1df6e1e418f1ca452a3ed4f59fd5cc081dbf2045f0d9b", "4b9a003b5c556c96784132945bb41c655ea11273b1917f5c8d0c154dd5fd20dd", "a458dc78104cc80048ac24fdc02fe6dce254838094c2f25641b3f954d9721241", {"version": "e8b18c6385ff784228a6f369694fcf1a6b475355ba89090a88de13587a9391d5", "affectsGlobalScope": true}, "f3e8bcce378a26bc672fce0ec05affabbbbfa18493b76f24c39136dea87100d0", "abc1c425b2ad6720433f40f1877abfa4223f0f3dd486c9c28c492179ca183cb6", "cd4854d38f4eb5592afd98ab95ca17389a7dfe38013d9079e802d739bdbcc939", "94eed4cc2f5f658d5e229ff1ccd38860bddf4233e347bf78edd2154dee1f2b99", {"version": "e51bee3200733b1f58818b5a9ea90fcd61c5b8afa3a0378391991f3696826a65", "affectsGlobalScope": true}, "9f1069b9e2c051737b1f9b4f1baf50e4a63385a6a89c32235549ae87fc3d5492", "ee18f2da7a037c6ceeb112a084e485aead9ea166980bf433474559eac1b46553", "e70339a3d63f806c43f24250c42aa0000093923457b0ed7dfc10e0ac910ebca9", "0acbf26bf958f9e80c1ffa587b74749d2697b75b484062d36e103c137c562bc3", "d7838022c7dab596357a9604b9c6adffe37dc34085ce0779c958ce9545bd7139", "1b952304137851e45bc009785de89ada562d9376177c97e37702e39e60c2f1ff", {"version": "806ef4cac3b3d9fa4a48d849c8e084d7c72fcd7b16d76e06049a9ed742ff79c0", "affectsGlobalScope": true}, "a279435e7813d1f061c0cab6ab77b1b9377e8d96851e5ed4a76a1ce6eb6e628f", "c33a6ea7147af60d8e98f1ac127047f4b0d4e2ce28b8f08ff3de07ca7cc00637", {"version": "b42b47e17b8ece2424ae8039feb944c2e3ba4b262986aebd582e51efbdca93dc", "affectsGlobalScope": true}, "664d8f2d59164f2e08c543981453893bc7e003e4dfd29651ce09db13e9457980", "2408611d9b4146e35d1dbd1f443ccd8e187c74614a54b80300728277529dbf11", "998a3de5237518c0b3ac00a11b3b4417affb008aa20aedee52f3fdae3cb86151", "ad41008ffe077206e1811fc873f4d9005b5fd7f6ab52bb6118fef600815a5cb4", "d88ecca73348e7c337541c4b8b60a50aca5e87384f6b8a422fc6603c637e4c21", "badae0df9a8016ac36994b0a0e7b82ba6aaa3528e175a8c3cb161e4683eec03e", "c3db860bcaaaeb3bbc23f353bbda1f8ab82756c8d5e973bebb3953cb09ea68f2", "235a53595bd20b0b0eeb1a29cb2887c67c48375e92f03749b2488fbd46d0b1a0", "bc09393cd4cd13f69cf1366d4236fbae5359bb550f0de4e15767e9a91d63dfb1", "9c266243b01545e11d2733a55ad02b4c00ecdbda99c561cd1674f96e89cdc958", "c71155c05fc76ff948a4759abc1cb9feec036509f500174bc18dad4c7827a60c", {"version": "ab9b9a36e5284fd8d3bf2f7d5fcbc60052f25f27e4d20954782099282c60d23e", "affectsGlobalScope": true}, "e704d7faa97765900963ac1ef5c675cb4c354f2a7fc9fbc104052e14bd65d614", "1d1e6bd176eee5970968423d7e215bfd66828b6db8d54d17afec05a831322633", "393137c76bd922ba70a2f8bf1ade4f59a16171a02fb25918c168d48875b0cfb0", "c757372a092924f5c16eaf11a1475b80b95bb4dae49fe3242d2ad908f97d5abe", "2b8264b2fefd7367e0f20e2c04eed5d3038831fe00f5efbc110ff0131aab899b", "f9a2dd6a6084665f093ed0e9664b8e673be2a45e342a59dd4e0e4e552e68a9ad", "8a19491eba2108d5c333c249699f40aff05ad312c04a17504573b27d91f0aede"], "options": {"allowSyntheticDefaultImports": true, "composite": true, "declarationMap": true, "downlevelIteration": true, "jsx": 2, "module": 99, "outDir": "./", "rootDir": "../..", "strict": true, "target": 6}, "fileIdsList": [[170], [56, 170], [65, 170], [82, 170], [86, 170], [62, 170], [142, 170, 177, 178], [127, 170], [130, 170], [131, 136, 170], [132, 142, 143, 150, 159, 169, 170], [132, 133, 142, 150, 170], [134, 170], [135, 136, 143, 151, 170], [136, 159, 166, 170], [137, 139, 142, 150, 170], [138, 170], [139, 140, 170], [141, 142, 170], [142, 170], [142, 143, 144, 159, 169, 170], [142, 143, 144, 159, 170], [145, 150, 159, 169, 170], [142, 143, 145, 146, 150, 159, 166, 169, 170], [145, 147, 159, 166, 169, 170], [127, 128, 129, 130, 131, 132, 133, 134, 135, 136, 137, 138, 139, 140, 141, 142, 143, 144, 145, 146, 147, 148, 149, 150, 151, 152, 153, 154, 155, 156, 157, 158, 159, 160, 161, 162, 163, 164, 165, 166, 167, 168, 169, 170, 171, 172, 173, 174, 175, 176], [142, 148, 170], [149, 169, 170], [139, 142, 150, 159, 170], [151, 170], [152, 170], [130, 153, 170], [154, 168, 170, 174], [155, 170], [156, 170], [142, 157, 170], [157, 158, 170, 172], [142, 159, 160, 161, 170], [159, 161, 170], [159, 160, 170], [162, 170], [163, 170], [142, 164, 165, 170], [164, 165, 170], [136, 150, 166, 170], [167, 170], [150, 168, 170], [131, 145, 156, 169, 170], [136, 170], [159, 170, 171], [170, 172], [170, 173], [131, 136, 142, 144, 153, 159, 169, 170, 172, 174], [159, 170, 175], [170, 177], [114, 170], [47, 49, 51, 53, 55, 61, 69, 73, 77, 81, 85, 90, 93, 97, 101, 115, 116, 170], [63, 170], [45, 46, 170], [46, 48, 170], [46, 50, 170], [46, 52, 170], [46, 54, 170], [46, 56, 57, 59, 60, 170], [46, 58, 59, 65, 66, 67, 68, 170], [71, 170], [46, 71, 72, 170], [59, 74, 75, 76, 170], [79, 170], [46, 79, 80, 170], [46, 58, 82, 83, 84, 170], [46, 58, 59, 86, 87, 88, 89, 170], [63, 92, 170], [46, 95, 96, 170], [99, 100, 170], [119, 120, 121, 170], [58, 59, 63, 170], [119, 170], [58, 170], [47, 49, 51, 53, 55, 56, 61, 65, 69, 71, 73, 74, 77, 79, 81, 82, 85, 86, 90, 92, 93, 96, 97, 99, 101], [57], [66], [74], [87], [99]], "referencedMap": [[45, 1], [56, 1], [60, 2], [95, 1], [48, 1], [65, 1], [68, 3], [67, 3], [71, 1], [75, 1], [76, 1], [50, 1], [79, 1], [52, 1], [82, 1], [84, 4], [54, 1], [86, 1], [89, 5], [88, 5], [62, 1], [63, 6], [124, 1], [125, 1], [126, 1], [179, 7], [180, 1], [178, 1], [127, 8], [128, 8], [130, 9], [131, 10], [132, 11], [133, 12], [134, 13], [135, 14], [136, 15], [137, 16], [138, 17], [139, 18], [140, 18], [141, 19], [142, 20], [143, 21], [144, 22], [129, 1], [176, 1], [145, 23], [146, 24], [147, 25], [177, 26], [148, 27], [149, 28], [150, 29], [151, 30], [152, 31], [153, 32], [154, 33], [155, 34], [156, 35], [157, 36], [158, 37], [159, 38], [161, 39], [160, 40], [162, 41], [163, 42], [164, 43], [165, 44], [166, 45], [167, 46], [168, 47], [169, 48], [170, 49], [171, 50], [172, 51], [173, 52], [174, 53], [175, 54], [181, 1], [182, 1], [183, 55], [115, 56], [114, 1], [121, 1], [12, 1], [11, 1], [2, 1], [13, 1], [14, 1], [15, 1], [16, 1], [17, 1], [18, 1], [19, 1], [20, 1], [3, 1], [4, 1], [24, 1], [21, 1], [22, 1], [23, 1], [25, 1], [26, 1], [27, 1], [5, 1], [28, 1], [29, 1], [30, 1], [31, 1], [6, 1], [32, 1], [33, 1], [34, 1], [35, 1], [7, 1], [40, 1], [36, 1], [37, 1], [38, 1], [39, 1], [8, 1], [41, 1], [42, 1], [43, 1], [1, 1], [9, 1], [44, 1], [10, 1], [58, 1], [117, 57], [118, 58], [116, 1], [47, 59], [49, 60], [51, 61], [53, 62], [55, 63], [57, 2], [102, 58], [61, 64], [64, 58], [103, 1], [104, 58], [105, 1], [106, 58], [107, 1], [108, 58], [66, 3], [69, 65], [72, 66], [109, 58], [70, 58], [73, 67], [74, 1], [77, 68], [80, 69], [110, 58], [78, 58], [81, 70], [83, 4], [85, 71], [87, 5], [90, 72], [92, 1], [111, 58], [91, 58], [93, 73], [96, 1], [112, 58], [94, 58], [97, 74], [99, 1], [113, 58], [100, 1], [98, 58], [101, 75], [46, 1], [122, 76], [123, 58], [119, 77], [120, 78], [59, 79]], "exportedModulesMap": [[45, 1], [56, 1], [60, 2], [95, 1], [48, 1], [65, 1], [68, 3], [67, 3], [71, 1], [75, 1], [76, 1], [50, 1], [79, 1], [52, 1], [82, 1], [84, 4], [54, 1], [86, 1], [89, 5], [88, 5], [62, 1], [63, 6], [124, 1], [125, 1], [126, 1], [179, 7], [180, 1], [178, 1], [127, 8], [128, 8], [130, 9], [131, 10], [132, 11], [133, 12], [134, 13], [135, 14], [136, 15], [137, 16], [138, 17], [139, 18], [140, 18], [141, 19], [142, 20], [143, 21], [144, 22], [129, 1], [176, 1], [145, 23], [146, 24], [147, 25], [177, 26], [148, 27], [149, 28], [150, 29], [151, 30], [152, 31], [153, 32], [154, 33], [155, 34], [156, 35], [157, 36], [158, 37], [159, 38], [161, 39], [160, 40], [162, 41], [163, 42], [164, 43], [165, 44], [166, 45], [167, 46], [168, 47], [169, 48], [170, 49], [171, 50], [172, 51], [173, 52], [174, 53], [175, 54], [181, 1], [182, 1], [183, 55], [115, 56], [114, 1], [121, 1], [12, 1], [11, 1], [2, 1], [13, 1], [14, 1], [15, 1], [16, 1], [17, 1], [18, 1], [19, 1], [20, 1], [3, 1], [4, 1], [24, 1], [21, 1], [22, 1], [23, 1], [25, 1], [26, 1], [27, 1], [5, 1], [28, 1], [29, 1], [30, 1], [31, 1], [6, 1], [32, 1], [33, 1], [34, 1], [35, 1], [7, 1], [40, 1], [36, 1], [37, 1], [38, 1], [39, 1], [8, 1], [41, 1], [42, 1], [43, 1], [1, 1], [9, 1], [44, 1], [10, 1], [58, 1], [117, 80], [118, 58], [116, 1], [47, 59], [49, 60], [51, 61], [53, 62], [55, 63], [57, 2], [102, 58], [61, 81], [64, 58], [103, 1], [104, 58], [105, 1], [106, 58], [107, 1], [108, 58], [66, 3], [69, 82], [72, 66], [109, 58], [70, 58], [73, 67], [74, 1], [77, 83], [80, 69], [110, 58], [78, 58], [81, 70], [83, 4], [85, 71], [87, 5], [90, 84], [92, 1], [111, 58], [91, 58], [93, 73], [96, 1], [112, 58], [94, 58], [97, 74], [99, 1], [113, 58], [100, 1], [98, 58], [101, 85], [46, 1], [123, 58]], "semanticDiagnosticsPerFile": [45, 56, 60, 95, 48, 65, 68, 67, 71, 75, 76, 50, 79, 52, 82, 84, 54, 86, 89, 88, 62, 63, 124, 125, 126, 179, 180, 178, 127, 128, 130, 131, 132, 133, 134, 135, 136, 137, 138, 139, 140, 141, 142, 143, 144, 129, 176, 145, 146, 147, 177, 148, 149, 150, 151, 152, 153, 154, 155, 156, 157, 158, 159, 161, 160, 162, 163, 164, 165, 166, 167, 168, 169, 170, 171, 172, 173, 174, 175, 181, 182, 183, 115, 114, 121, 12, 11, 2, 13, 14, 15, 16, 17, 18, 19, 20, 3, 4, 24, 21, 22, 23, 25, 26, 27, 5, 28, 29, 30, 31, 6, 32, 33, 34, 35, 7, 40, 36, 37, 38, 39, 8, 41, 42, 43, 1, 9, 44, 10, 58, 117, 118, 116, 47, 49, 51, 53, 55, 57, 102, 61, 64, 103, 104, 105, 106, 107, 108, 66, 69, 72, 109, 70, 73, 74, 77, 80, 110, 78, 81, 83, 85, 87, 90, 92, 111, 91, 93, 96, 112, 94, 97, 99, 113, 100, 98, [101, [{"file": "../../src/features/processors/resize/worker/resize.ts", "start": 164, "length": 19, "messageText": "Cannot find module 'codecs/resize/pkg' or its corresponding type declarations.", "category": 1, "code": 2307}, {"file": "../../src/features/processors/resize/worker/resize.ts", "start": 232, "length": 16, "messageText": "Cannot find module 'codecs/hqx/pkg' or its corresponding type declarations.", "category": 1, "code": 2307}]], 46, 122, 123, 119, 120, 59], "affectedFilesPendingEmit": [[75, 1], [76, 1], [117, 1], [61, 1], [69, 1], [77, 1], [90, 1], [101, 1], [122, 1], [119, 1], [120, 1], [59, 1]]}, "version": "4.4.4"}