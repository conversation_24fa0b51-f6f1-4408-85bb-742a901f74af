'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

/** Start points, for the shape we use in prerender */
const startBlobs = [
    [
        [-0.232, -1.029, 0.073, -1.029, 0.377, -1.029],
        [0.565, -1.098, 0.755, -0.86, 0.945, -0.622],
        [0.917, -0.01, 0.849, 0.286, 0.782, 0.583],
        [0.85, 0.687, 0.576, 0.819, 0.302, 0.951],
        [-0.198, 1.009, -0.472, 0.877, -0.746, 0.745],
        [-0.98, 0.513, -1.048, 0.216, -1.116, -0.08],
        [-0.964, -0.395, -0.774, -0.633, -0.584, -0.871],
    ],
    [
        [-0.505, -1.109, -0.201, -1.109, 0.104, -1.109],
        [0.641, -0.684, 0.831, -0.446, 1.02, -0.208],
        [1.041, 0.034, 0.973, 0.331, 0.905, 0.628],
        [0.734, 0.794, 0.46, 0.926, 0.186, 1.058],
        [-0.135, 0.809, -0.409, 0.677, -0.684, 0.545],
        [-0.935, 0.404, -1.002, 0.108, -1.07, -0.189],
        [-0.883, -0.402, -0.693, -0.64, -0.503, -0.878],
    ],
    [
        [-0.376, -1.168, -0.071, -1.168, 0.233, -1.168],
        [0.732, -0.956, 0.922, -0.718, 1.112, -0.48],
        [1.173, 0.027, 1.105, 0.324, 1.038, 0.621],
        [0.707, 0.81, 0.433, 0.943, 0.159, 1.075],
        [-0.096, 1.135, -0.37, 1.003, -0.644, 0.871],
        [-0.86, 0.457, -0.927, 0.161, -0.995, -0.136],
        [-0.87, -0.516, -0.68, -0.754, -0.49, -0.992],
    ],
    [
        [-0.309, -0.998, -0.004, -0.998, 0.3, -0.998],
        [0.535, -0.852, 0.725, -0.614, 0.915, -0.376],
        [1.05, -0.09, 0.982, 0.207, 0.915, 0.504],
        [0.659, 0.807, 0.385, 0.939, 0.111, 1.071],
        [-0.178, 1.048, -0.452, 0.916, -0.727, 0.784],
        [-0.942, 0.582, -1.009, 0.285, -1.077, -0.011],
        [-1.141, -0.335, -0.951, -0.573, -0.761, -0.811],
    ],
];

exports.startBlobs = startBlobs;
