define(['module', 'require', 'exports', './workerHelpers-9499ba45'], function (module, require, exports, workerHelpers) { 'use strict';

    let wasm;

    let cachedTextDecoder = new TextDecoder('utf-8', { ignoreBOM: true, fatal: true });

    cachedTextDecoder.decode();

    let cachegetUint8Memory0 = null;
    function getUint8Memory0() {
        if (cachegetUint8Memory0 === null || cachegetUint8Memory0.buffer !== wasm.__wbindgen_export_0.buffer) {
            cachegetUint8Memory0 = new Uint8Array(wasm.__wbindgen_export_0.buffer);
        }
        return cachegetUint8Memory0;
    }

    function getStringFromWasm0(ptr, len) {
        return cachedTextDecoder.decode(getUint8Memory0().slice(ptr, ptr + len));
    }

    const heap = new Array(32).fill(undefined);

    heap.push(undefined, null, true, false);

    let heap_next = heap.length;

    function addHeapObject(obj) {
        if (heap_next === heap.length) heap.push(heap.length + 1);
        const idx = heap_next;
        heap_next = heap[idx];

        heap[idx] = obj;
        return idx;
    }

    let WASM_VECTOR_LEN = 0;

    function passArray8ToWasm0(arg, malloc) {
        const ptr = malloc(arg.length * 1);
        getUint8Memory0().set(arg, ptr / 1);
        WASM_VECTOR_LEN = arg.length;
        return ptr;
    }

    let cachegetInt32Memory0 = null;
    function getInt32Memory0() {
        if (cachegetInt32Memory0 === null || cachegetInt32Memory0.buffer !== wasm.__wbindgen_export_0.buffer) {
            cachegetInt32Memory0 = new Int32Array(wasm.__wbindgen_export_0.buffer);
        }
        return cachegetInt32Memory0;
    }

    function getArrayU8FromWasm0(ptr, len) {
        return getUint8Memory0().subarray(ptr / 1, ptr / 1 + len);
    }
    /**
    * @param {Uint8ClampedArray} data
    * @param {number} width
    * @param {number} height
    * @param {number} level
    * @param {boolean} interlace
    * @returns {Uint8Array}
    */
    function optimise(data, width, height, level, interlace) {
        try {
            const retptr = wasm.__wbindgen_add_to_stack_pointer(-16);
            var ptr0 = passArray8ToWasm0(data, wasm.__wbindgen_malloc);
            var len0 = WASM_VECTOR_LEN;
            wasm.optimise(retptr, ptr0, len0, width, height, level, interlace);
            var r0 = getInt32Memory0()[retptr / 4 + 0];
            var r1 = getInt32Memory0()[retptr / 4 + 1];
            var v1 = getArrayU8FromWasm0(r0, r1).slice();
            wasm.__wbindgen_free(r0, r1 * 1);
            return v1;
        } finally {
            wasm.__wbindgen_add_to_stack_pointer(16);
        }
    }

    function getObject(idx) { return heap[idx]; }

    function dropObject(idx) {
        if (idx < 36) return;
        heap[idx] = heap_next;
        heap_next = idx;
    }

    function takeObject(idx) {
        const ret = getObject(idx);
        dropObject(idx);
        return ret;
    }
    /**
    * @param {number} num_threads
    * @returns {Promise<any>}
    */
    function initThreadPool(num_threads) {
        var ret = wasm.initThreadPool(num_threads);
        return takeObject(ret);
    }

    /**
    * @param {number} receiver
    */
    function wbg_rayon_start_worker(receiver) {
        wasm.wbg_rayon_start_worker(receiver);
    }

    /**
    */
    class wbg_rayon_PoolBuilder {

        static __wrap(ptr) {
            const obj = Object.create(wbg_rayon_PoolBuilder.prototype);
            obj.ptr = ptr;

            return obj;
        }

        __destroy_into_raw() {
            const ptr = this.ptr;
            this.ptr = 0;

            return ptr;
        }

        free() {
            const ptr = this.__destroy_into_raw();
            wasm.__wbg_wbg_rayon_poolbuilder_free(ptr);
        }
        /**
        * @returns {number}
        */
        numThreads() {
            var ret = wasm.wbg_rayon_poolbuilder_numThreads(this.ptr);
            return ret >>> 0;
        }
        /**
        * @returns {number}
        */
        receiver() {
            var ret = wasm.wbg_rayon_poolbuilder_receiver(this.ptr);
            return ret;
        }
        /**
        */
        build() {
            wasm.wbg_rayon_poolbuilder_build(this.ptr);
        }
    }

    async function load(module, imports) {
        if (typeof Response === 'function' && module instanceof Response) {
            if (typeof WebAssembly.instantiateStreaming === 'function') {
                try {
                    return await WebAssembly.instantiateStreaming(module, imports);

                } catch (e) {
                    if (module.headers.get('Content-Type') != 'application/wasm') {
                        console.warn("`WebAssembly.instantiateStreaming` failed because your server does not serve wasm with `application/wasm` MIME type. Falling back to `WebAssembly.instantiate` which is slower. Original error:\n", e);

                    } else {
                        throw e;
                    }
                }
            }

            const bytes = await module.arrayBuffer();
            return await WebAssembly.instantiate(bytes, imports);

        } else {
            const instance = await WebAssembly.instantiate(module, imports);

            if (instance instanceof WebAssembly.Instance) {
                return { instance, module };

            } else {
                return instance;
            }
        }
    }

    async function init(input, maybe_memory) {
        if (typeof input === 'undefined') {
            input = new URL("/c/squoosh_oxipng_bg-5c8fadb7.wasm", module.uri);
        }
        const imports = {};
        imports.wbg = {};
        imports.wbg.__wbindgen_throw = function(arg0, arg1) {
            throw new Error(getStringFromWasm0(arg0, arg1));
        };
        imports.wbg.__wbindgen_module = function() {
            var ret = init.__wbindgen_wasm_module;
            return addHeapObject(ret);
        };
        imports.wbg.__wbindgen_memory = function() {
            var ret = wasm.__wbindgen_export_0;
            return addHeapObject(ret);
        };
        imports.wbg.__wbg_startWorkers_914655bb4d5bb5e1 = function(arg0, arg1, arg2) {
            var ret = workerHelpers.startWorkers(takeObject(arg0), takeObject(arg1), wbg_rayon_PoolBuilder.__wrap(arg2));
            return addHeapObject(ret);
        };

        if (typeof input === 'string' || (typeof Request === 'function' && input instanceof Request) || (typeof URL === 'function' && input instanceof URL)) {
            input = fetch(input);
        }

        imports.wbg.memory = maybe_memory || new WebAssembly.Memory({initial:17,maximum:16384,shared:true});

        const { instance, module: module$1 } = await load(await input, imports);

        wasm = instance.exports;
        init.__wbindgen_wasm_module = module$1;
        wasm.__wbindgen_start();
        return wasm;
    }

    exports.default = init;
    exports.initThreadPool = initThreadPool;
    exports.optimise = optimise;
    exports.wbg_rayon_PoolBuilder = wbg_rayon_PoolBuilder;
    exports.wbg_rayon_start_worker = wbg_rayon_start_worker;

});
