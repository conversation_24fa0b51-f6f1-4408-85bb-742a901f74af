define(['exports'], function (exports) { 'use strict';

    const simd=async()=>WebAssembly.validate(new Uint8Array([0,97,115,109,1,0,0,0,1,5,1,96,0,1,123,3,2,1,0,10,10,1,8,0,65,0,253,15,253,98,11])),threads=()=>(async e=>{try{return "undefined"!=typeof MessageChannel&&(new MessageChannel).port1.postMessage(new SharedArrayBuffer(1)),WebAssembly.validate(e)}catch(e){return !1}})(new Uint8Array([0,97,115,109,1,0,0,0,1,4,1,96,0,0,3,2,1,0,5,4,1,3,1,1,10,11,1,9,0,65,0,254,16,2,0,26,11]));

    async function checkThreadsSupport() {
        const supportsWasmThreads = await threads();
        if (!supportsWasmThreads)
            return false;
        // Safari 16 shipped with WASM threads support, but it didn't ship with nested workers support.
        // This meant CompressFlow failed in Safari 16, since we call our wasm from inside a worker to begin with.
        // Right now, this check is only run from a worker.
        // More implementation is needed to run it from a page.
        if (!('importScripts' in self)) {
            throw Error('Not implemented');
        }
        return 'Worker' in self;
    }

    exports.checkThreadsSupport = checkThreadsSupport;
    exports.simd = simd;

});
