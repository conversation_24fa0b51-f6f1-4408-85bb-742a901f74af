# CompressFlow Deployment Guide

This guide covers different deployment options for CompressFlow.

## 🚀 Quick Deployment Options

### 1. Vercel (Recommended)

```bash
# Install Vercel CLI
npm i -g vercel

# Deploy
vercel

# For production deployment
vercel --prod
```

### 2. Netlify

```bash
# Install Netlify CLI
npm i -g netlify-cli

# Build the project
npm run build

# Deploy
netlify deploy --dir=.tmp/build/static

# For production
netlify deploy --prod --dir=.tmp/build/static
```

### 3. GitHub Pages

```bash
# Build the project
npm run build

# Deploy to gh-pages branch (requires gh-pages package)
npm install -g gh-pages
gh-pages -d .tmp/build/static
```

### 4. Static File Hosting

After running `npm run build`, the static files will be in `.tmp/build/static/`. You can upload these files to any static hosting service:

- AWS S3 + CloudFront
- Google Cloud Storage
- Azure Static Web Apps
- Firebase Hosting

## 📋 Pre-deployment Checklist

- [ ] Run `npm run build` successfully
- [ ] Test the built application locally with `npm run serve`
- [ ] Verify all compression codecs work
- [ ] Test multiple file upload and batch download
- [ ] Check that all assets load correctly
- [ ] Verify PWA functionality (if applicable)

## 🔧 Configuration

### Environment Variables

CompressFlow doesn't require environment variables for basic functionality, but you may want to configure:

- `DEV_PORT`: Development server port (default: 5000)

### Headers Configuration

The app requires specific headers for WebAssembly and SharedArrayBuffer support:

```json
{
  "Cross-Origin-Embedder-Policy": "require-corp",
  "Cross-Origin-Opener-Policy": "same-origin"
}
```

These are configured in `serve.json` for local development.

## 🐛 Troubleshooting

### Common Issues

1. **WebAssembly not loading**: Ensure CORS headers are properly configured
2. **SharedArrayBuffer errors**: Check Cross-Origin headers
3. **File upload not working**: Verify file size limits on your hosting platform
4. **Slow compression**: This is normal for large images; processing happens client-side

### Performance Tips

- Enable gzip/brotli compression on your server
- Set appropriate cache headers for static assets
- Consider using a CDN for better global performance

## 📊 Monitoring

Consider adding:
- Error tracking (Sentry, Bugsnag)
- Analytics (Google Analytics, Plausible)
- Performance monitoring (Web Vitals)

Remember: CompressFlow processes everything client-side, so server requirements are minimal!
